# Content Security Policy Implementation

This document provides an overview of the Content Security Policy (CSP) implementation in the IEC Courses Portal application.

## Overview

The Content Security Policy implementation addresses security vulnerabilities by removing unsafe CSP directives (`unsafe-inline` and `unsafe-eval`) and properly implementing CSP by extracting all inline CSS and JavaScript into external files.

## Key Components

### 1. Enhanced CSP Middleware

The `EnhancedContentSecurityPolicy` middleware applies strict CSP headers to all responses. It:

- Generates secure nonces for each request
- Applies appropriate CSP headers based on environment
- Handles CSP reporting
- Provides helper methods for views to access nonces

### 2. Asset Management System

The asset management system organizes extracted CSS and JavaScript files and provides helper functions for views to include external resources:

- `AssetManager` class for registering and enqueueing assets
- Blade directives for including external resources
- Helper functions for working with assets

### 3. CSP Configuration

The CSP configuration is defined in `config/csp.php` and includes:

- Production environment configuration (strict, no unsafe directives)
- Development environment configuration (with some relaxed constraints)
- Reporting configuration

### 4. CSP Violation Reporting

The CSP violation reporting system collects and logs CSP violations:

- `CspReportController` handles violation reports
- Violations are logged to a dedicated log channel
- Aggregation prevents log flooding
- Alerts for critical violations

## Implementation Details

### Extracted CSS and JavaScript

All inline CSS and JavaScript has been extracted to external files:

- Auth pages: `public/assets/css/extracted/auth.css` and `public/assets/js/extracted/auth.js`
- Admin dashboard: `public/assets/css/extracted/admin-dashboard.css` and `public/assets/js/extracted/admin-dashboard.js`

### Nonce Generation

Secure nonces are generated for each request and made available to views:

- `NonceGenerator` class generates cryptographically secure nonces
- Nonces are shared with views via view composers
- Blade directives make it easy to apply nonces to script and style tags

### Event Handlers

Inline event handlers have been replaced with event delegation:

- Event listeners are attached in external JavaScript files
- Data attributes are used to identify elements that need event handling

## Testing

The implementation includes comprehensive tests:

- Unit tests for CSP middleware
- Integration tests for CSP headers
- Security tests for CSP effectiveness

## Usage

### Including External Resources

To include external CSS and JavaScript files in views:

```php
@push('styles')
    <link rel="stylesheet" href="{{ asset('path/to/style.css') }}" @cspNonce>
@endpush

@push('scripts')
    <script src="{{ asset('path/to/script.js') }}" @cspNonce></script>
@endpush
```

### Using Blade Directives

```php
@externalStyle('style-name')
@externalScript('script-name')
```

### Using Helper Functions

```php
{!! render_styles() !!}
{!! render_scripts() !!}
```

## Security Benefits

This implementation provides several security benefits:

- Protection against Cross-Site Scripting (XSS) attacks
- Reduced attack surface by eliminating unsafe directives
- Improved security posture through strict CSP
- Monitoring and alerting for potential security issues

## Browser Compatibility

The implementation is compatible with all modern browsers. For older browsers that don't fully support CSP, the application will still function correctly, but with potentially reduced security benefits.