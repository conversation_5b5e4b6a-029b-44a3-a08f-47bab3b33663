/**
 * Simple CSP Fix
 * A clean, non-intrusive solution for CSP violations
 */

(function() {
    'use strict';
    
    // Get CSP nonce
    const nonceMetaTag = document.querySelector('meta[name="csp-nonce"]');
    const nonce = nonceMetaTag ? nonceMetaTag.getAttribute('content') : null;
    
    if (!nonce) {
        console.warn('Simple CSP Fix: CSP nonce not found');
        return;
    }
    
    console.log('Simple CSP Fix: Initialized with nonce:', nonce);
    
    let fixCounter = 0;
    
    // Function to create a unique class name
    function createUniqueClass() {
        return 'scf-' + Date.now() + '-' + (++fixCounter);
    }
    
    // Function to create a nonce-enabled style
    function createNoncedStyle(css) {
        const style = document.createElement('style');
        style.setAttribute('nonce', nonce);
        style.textContent = css;
        document.head.appendChild(style);
        return style;
    }
    
    // Function to fix inline styles
    function fixInlineStyles() {
        const elementsWithStyle = document.querySelectorAll('[style]');
        let fixed = 0;
        
        elementsWithStyle.forEach(element => {
            const inlineStyle = element.getAttribute('style');
            if (inlineStyle && inlineStyle.trim()) {
                try {
                    // Remove the inline style
                    element.removeAttribute('style');
                    
                    // Create unique class
                    const uniqueClass = createUniqueClass();
                    element.classList.add(uniqueClass);
                    
                    // Create the style rule
                    createNoncedStyle('.' + uniqueClass + ' { ' + inlineStyle + ' }');
                    fixed++;
                } catch (error) {
                    console.error('Simple CSP Fix: Error fixing inline style:', error);
                }
            }
        });
        
        if (fixed > 0) {
            console.log('Simple CSP Fix: Fixed', fixed, 'inline styles');
        }
        
        return fixed;
    }
    
    // Function to fix inline event handlers
    function fixInlineEventHandlers() {
        const eventTypes = ['click', 'change', 'submit', 'load', 'error', 'mouseover', 'mouseout', 'focus', 'blur'];
        let fixed = 0;
        
        eventTypes.forEach(eventType => {
            const handlerAttr = 'on' + eventType;
            const elements = document.querySelectorAll('[' + handlerAttr + ']');
            
            elements.forEach(element => {
                const handlerCode = element.getAttribute(handlerAttr);
                if (handlerCode && handlerCode.trim()) {
                    try {
                        // Remove the inline handler
                        element.removeAttribute(handlerAttr);
                        
                        // Add proper event listener
                        element.addEventListener(eventType, function(event) {
                            try {
                                const func = new Function('event', handlerCode);
                                func.call(this, event);
                            } catch (error) {
                                console.error('Simple CSP Fix: Error executing event handler:', error);
                            }
                        });
                        
                        fixed++;
                    } catch (error) {
                        console.error('Simple CSP Fix: Error fixing event handler:', error);
                    }
                }
            });
        });
        
        if (fixed > 0) {
            console.log('Simple CSP Fix: Fixed', fixed, 'inline event handlers');
        }
        
        return fixed;
    }
    
    // Function to fix inline scripts
    function fixInlineScripts() {
        const inlineScripts = document.querySelectorAll('script:not([src]):not([nonce])');
        let fixed = 0;
        
        inlineScripts.forEach(script => {
            try {
                script.setAttribute('nonce', nonce);
                fixed++;
            } catch (error) {
                console.error('Simple CSP Fix: Error fixing inline script:', error);
            }
        });
        
        if (fixed > 0) {
            console.log('Simple CSP Fix: Fixed', fixed, 'inline scripts');
        }
        
        return fixed;
    }
    
    // Function to fix styles without nonce
    function fixStylesWithoutNonce() {
        const stylesWithoutNonce = document.querySelectorAll('style:not([nonce])');
        let fixed = 0;
        
        stylesWithoutNonce.forEach(style => {
            try {
                style.setAttribute('nonce', nonce);
                fixed++;
            } catch (error) {
                console.error('Simple CSP Fix: Error fixing style element:', error);
            }
        });
        
        if (fixed > 0) {
            console.log('Simple CSP Fix: Fixed', fixed, 'styles without nonce');
        }
        
        return fixed;
    }
    
    // Function to fix Livewire styles
    function fixLivewireStyles() {
        const livewireStylesContainer = document.getElementById('livewire-styles-container');
        if (livewireStylesContainer) {
            const styles = livewireStylesContainer.querySelectorAll('style:not([nonce])');
            let fixed = 0;
            
            styles.forEach(style => {
                try {
                    style.setAttribute('nonce', nonce);
                    fixed++;
                } catch (error) {
                    console.error('Simple CSP Fix: Error fixing Livewire style:', error);
                }
            });
            
            if (fixed > 0) {
                console.log('Simple CSP Fix: Fixed', fixed, 'Livewire styles');
            }
            
            return fixed;
        }
        
        return 0;
    }
    
    // Main fix function
    function runFixes() {
        let totalFixed = 0;
        
        totalFixed += fixInlineStyles();
        totalFixed += fixInlineEventHandlers();
        totalFixed += fixInlineScripts();
        totalFixed += fixStylesWithoutNonce();
        totalFixed += fixLivewireStyles();
        
        return totalFixed;
    }
    
    // Initialize
    function initialize() {
        console.log('Simple CSP Fix: Starting initialization');
        
        // Initial fix
        runFixes();
        
        // Set up MutationObserver for dynamic content
        const observer = new MutationObserver(function(mutations) {
            let needsFix = false;
            
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            if (node.hasAttribute && (
                                node.hasAttribute('style') || 
                                node.hasAttribute('onclick') ||
                                node.tagName === 'SCRIPT' ||
                                node.tagName === 'STYLE'
                            )) {
                                needsFix = true;
                            }
                            
                            // Check child elements
                            const problematicChildren = node.querySelectorAll ? 
                                node.querySelectorAll('[style], [onclick], script:not([nonce]), style:not([nonce])') : [];
                            if (problematicChildren.length > 0) {
                                needsFix = true;
                            }
                        }
                    });
                } else if (mutation.type === 'attributes') {
                    if (mutation.attributeName === 'style' || mutation.attributeName.startsWith('on')) {
                        needsFix = true;
                    }
                }
            });
            
            if (needsFix) {
                setTimeout(runFixes, 10);
            }
        });
        
        // Start observing
        observer.observe(document.documentElement, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'onclick', 'onchange', 'onsubmit', 'onload', 'onerror']
        });
        
        // Periodic fixes
        setInterval(runFixes, 1000);
        
        console.log('Simple CSP Fix: Initialization complete');
    }
    
    // Start when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    // Expose global function
    window.simpleCSPFix = {
        run: runFixes,
        nonce: nonce
    };
    
})();