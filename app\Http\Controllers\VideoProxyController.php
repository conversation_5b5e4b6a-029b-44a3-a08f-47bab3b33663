<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use App\Models\Lecture;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Carbon\Carbon;

class VideoProxyController extends Controller
{
    /**
     * Generate encrypted token for video URL
     */
    public static function generateVideoToken($videoUrl)
    {
        // Extract video ID if it's a YouTube URL
        if (Str::contains($videoUrl, ['youtube.com', 'youtu.be'])) {
            preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $videoUrl, $matches);
            $videoId = $matches[1] ?? null;

            if (!$videoId) {
                return null;
            }

            // Create token data
            $tokenData = [
                'id' => base64_encode($videoId),
                'exp' => Carbon::now()->addMinutes(5)->timestamp,
                'nonce' => Str::random(16)
            ];

            // Encrypt the entire token
            return Crypt::encryptString(json_encode($tokenData));
        }

        // For non-YouTube videos, encrypt the full URL
        return Crypt::encryptString($videoUrl);
    }

    /**
     * Proxy video content with obfuscated URLs
     */
    public function getVideo($token)
    {
        try {
            if (empty($token)) {
                abort(404);
            }

            // Decrypt the token
            $decrypted = Crypt::decryptString($token);
            $tokenData = json_decode($decrypted, true);

            // Handle YouTube videos
            if (isset($tokenData['id'])) {
                // Verify token expiration
                if ($tokenData['exp'] < Carbon::now()->timestamp) {
                    return response()->json(['error' => 'Token expired'], 403);
                }

                // Decode video ID
                $videoId = base64_decode($tokenData['id']);
                $videoUrl = "https://www.youtube.com/embed/{$videoId}";
                return redirect()->away($videoUrl);
            }

            // For non-YouTube videos, implement streaming logic here
            return response()->json(['error' => 'Video type not supported'], 400);
        } catch (\Exception $e) {
            \Log::error('Video stream error: ' . $e->getMessage());
            abort(404);
        }
    }

    /**
     * Show the secure embed page
     */
    public function embedVideo($token)
    {
        try {
            if (empty($token)) {
                abort(404);
            }

            // Generate a unique session key for this view
            $sessionKey = Str::random(32);
            Cache::put('video_session_' . $sessionKey, $token, now()->addMinutes(5));

            return view('video.embed', [
                'token' => $token,
                'sessionKey' => $sessionKey
            ]);
        } catch (\Exception $e) {
            \Log::error('Video embed error: ' . $e->getMessage());
            abort(404);
        }
    }

    /**
     * Get embed URL for AJAX request with token verification
     */
    public function getEmbedUrl(Request $request)
    {
        try {
            $token = $request->input('token');
            if (empty($token)) {
                return response()->json(['error' => 'Invalid request'], 400);
            }

            // Decrypt and verify token
            $decrypted = Crypt::decryptString($token);
            $tokenData = json_decode($decrypted, true);

            // Handle YouTube videos
            if (isset($tokenData['id'])) {
                // Verify token expiration
                if ($tokenData['exp'] < Carbon::now()->timestamp) {
                    return response()->json(['error' => 'Token expired'], 403);
                }

                // Decode video ID
                $videoId = base64_decode($tokenData['id']);

                // Return data attributes for Plyr instead of direct URL
                return response()->json([
                    'type' => 'youtube',
                    'provider' => 'youtube',
                    'embedId' => $videoId
                ]);
            }

            // Handle other video types
            return response()->json([
                'type' => 'other',
                'embedUrl' => $decrypted
            ]);
        } catch (\Exception $e) {
            \Log::error('Get embed URL error: ' . $e->getMessage());
            return response()->json(['error' => 'Unable to process video'], 500);
        }
    }
}
