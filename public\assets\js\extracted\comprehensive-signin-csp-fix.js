/**
 * Comprehensive Sign-in Page CSP Fix
 * This script fixes all CSP violations on the sign-in page by removing inline styles and scripts
 */

(function() {
    'use strict';
    
    // Get CSP nonce from meta tag
    const nonceMetaTag = document.querySelector('meta[name="csp-nonce"]');
    const nonce = nonceMetaTag ? nonceMetaTag.getAttribute('content') : null;
    
    if (!nonce) {
        console.warn('CSP nonce not found in meta tag');
        return;
    }
    
    // Function to create a style element with nonce
    function createNoncedStyle(css, className) {
        const styleElement = document.createElement('style');
        styleElement.setAttribute('nonce', nonce);
        styleElement.textContent = css;
        document.head.appendChild(styleElement);
        return className;
    }
    
    // Function to fix all inline styles
    function fixInlineStyles() {
        const elementsWithStyle = document.querySelectorAll('[style]');
        elementsWithStyle.forEach((element, index) => {
            const inlineStyle = element.getAttribute('style');
            if (inlineStyle) {
                element.removeAttribute('style');
                
                // Create a unique class name
                const uniqueClass = 'csp-fix-style-' + Date.now() + '-' + index;
                element.classList.add(uniqueClass);
                
                // Create a style element with the nonce
                createNoncedStyle('.' + uniqueClass + ' { ' + inlineStyle + ' }', uniqueClass);
            }
        });
    }
    
    // Function to fix all inline event handlers
    function fixInlineEventHandlers() {
        // Fix onclick handlers
        const elementsWithOnclick = document.querySelectorAll('[onclick]');
        elementsWithOnclick.forEach(element => {
            const onclickCode = element.getAttribute('onclick');
            element.removeAttribute('onclick');
            
            // Add proper event listener
            element.addEventListener('click', function(event) {
                event.preventDefault();
                try {
                    // Safely execute the onclick code
                    const func = new Function('event', onclickCode);
                    func.call(this, event);
                } catch (error) {
                    console.error('Error executing onclick handler:', error);
                }
            });
        });
        
        // Fix other inline event handlers
        const eventHandlers = ['onchange', 'onsubmit', 'onload', 'onerror', 'onmouseover', 'onmouseout'];
        eventHandlers.forEach(handler => {
            const elements = document.querySelectorAll('[' + handler + ']');
            elements.forEach(element => {
                const handlerCode = element.getAttribute(handler);
                element.removeAttribute(handler);
                
                const eventType = handler.substring(2); // Remove 'on' prefix
                element.addEventListener(eventType, function(event) {
                    try {
                        const func = new Function('event', handlerCode);
                        func.call(this, event);
                    } catch (error) {
                        console.error('Error executing ' + handler + ' handler:', error);
                    }
                });
            });
        });
    }
    
    // Function to fix Livewire styles
    function fixLivewireStyles() {
        // Fix Livewire generated styles
        const livewireStyles = document.querySelectorAll('style:not([nonce])');
        livewireStyles.forEach(style => {
            if (!style.hasAttribute('nonce')) {
                style.setAttribute('nonce', nonce);
            }
        });
        
        // Fix Livewire container styles
        const livewireContainers = document.querySelectorAll('[wire\\:id]');
        livewireContainers.forEach(container => {
            if (container.hasAttribute('style')) {
                const inlineStyle = container.getAttribute('style');
                container.removeAttribute('style');
                
                const uniqueClass = 'livewire-csp-fix-' + Date.now();
                container.classList.add(uniqueClass);
                
                createNoncedStyle('.' + uniqueClass + ' { ' + inlineStyle + ' }', uniqueClass);
            }
        });
    }
    
    // Function to fix specific sign-in page elements
    function fixSignInSpecificElements() {
        // Fix captcha display
        const captchaDisplay = document.getElementById('captcha-display');
        if (captchaDisplay && captchaDisplay.hasAttribute('style')) {
            const inlineStyle = captchaDisplay.getAttribute('style');
            captchaDisplay.removeAttribute('style');
            captchaDisplay.classList.add('captcha-display-fixed');
            
            createNoncedStyle('.captcha-display-fixed { ' + inlineStyle + ' }', 'captcha-display-fixed');
        }
        
        // Fix oblique image background
        const obliqueImage = document.querySelector('.oblique-image');
        if (obliqueImage && obliqueImage.hasAttribute('style')) {
            const inlineStyle = obliqueImage.getAttribute('style');
            obliqueImage.removeAttribute('style');
            obliqueImage.classList.add('oblique-image-fixed');
            
            createNoncedStyle('.oblique-image-fixed { ' + inlineStyle + ' }', 'oblique-image-fixed');
        }
        
        // Fix any progress bars with inline styles
        const progressBars = document.querySelectorAll('.progress-bar[style]');
        progressBars.forEach((bar, index) => {
            const inlineStyle = bar.getAttribute('style');
            bar.removeAttribute('style');
            
            const uniqueClass = 'progress-bar-fixed-' + index;
            bar.classList.add(uniqueClass);
            
            createNoncedStyle('.' + uniqueClass + ' { ' + inlineStyle + ' }', uniqueClass);
        });
        
        // Fix any card backgrounds with inline styles
        const cardBackgrounds = document.querySelectorAll('.full-background[style]');
        cardBackgrounds.forEach((bg, index) => {
            const inlineStyle = bg.getAttribute('style');
            bg.removeAttribute('style');
            
            const uniqueClass = 'card-bg-fixed-' + index;
            bg.classList.add(uniqueClass);
            
            createNoncedStyle('.' + uniqueClass + ' { ' + inlineStyle + ' }', uniqueClass);
        });
    }
    
    // Function to fix any remaining inline scripts
    function fixInlineScripts() {
        const inlineScripts = document.querySelectorAll('script:not([src]):not([nonce])');
        inlineScripts.forEach(script => {
            if (!script.hasAttribute('nonce')) {
                script.setAttribute('nonce', nonce);
            }
        });
    }
    
    // Main fix function
    function runComprehensiveFix() {
        fixInlineStyles();
        fixInlineEventHandlers();
        fixLivewireStyles();
        fixSignInSpecificElements();
        fixInlineScripts();
    }
    
    // Run immediately if DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runComprehensiveFix);
    } else {
        runComprehensiveFix();
    }
    
    // Set up MutationObserver to catch dynamically added content
    const observer = new MutationObserver(function(mutations) {
        let needsUpdate = false;
        
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                // Check if any new nodes have inline styles or scripts
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        if (node.hasAttribute && (node.hasAttribute('style') || node.hasAttribute('onclick'))) {
                            needsUpdate = true;
                        }
                        
                        // Check child elements
                        const childrenWithStyle = node.querySelectorAll ? node.querySelectorAll('[style], [onclick]') : [];
                        if (childrenWithStyle.length > 0) {
                            needsUpdate = true;
                        }
                    }
                });
            } else if (mutation.type === 'attributes') {
                if (mutation.attributeName === 'style' || mutation.attributeName === 'onclick') {
                    needsUpdate = true;
                }
            }
        });
        
        if (needsUpdate) {
            setTimeout(runComprehensiveFix, 10); // Small delay to allow DOM to settle
        }
    });
    
    // Start observing
    observer.observe(document.documentElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'onclick', 'onchange', 'onsubmit', 'onload', 'onerror']
    });
    
    // Run periodically to catch any missed elements
    setInterval(runComprehensiveFix, 1000);
    
})();