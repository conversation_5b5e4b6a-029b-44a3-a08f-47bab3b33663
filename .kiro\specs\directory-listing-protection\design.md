# Design Document: Directory Listing Protection

## Overview

This design document outlines the approach to implement directory listing protection for the IEC Courses Portal. The solution will ensure that directory contents are not exposed to users, addressing the security vulnerability identified in the staging environment. The implementation will be consistent across all environments (local, staging, and production).

## Architecture

The directory listing protection will be implemented through multiple layers:

1. **Server Configuration Layer**: Primary protection through web server configuration (.htaccess for Apache, server blocks for Nginx)
2. **Application Layer**: Secondary protection through Laravel middleware
3. **Fallback Layer**: Custom error handling for cases where the above layers might fail

This multi-layered approach ensures robust protection even if one layer is misconfigured or bypassed.

## Components and Interfaces

### 1. Server Configuration Components

#### Apache Configuration (.htaccess)

The primary defense will be an optimized .htaccess file in the public directory that explicitly disables directory listing. The current .htaccess file needs to be properly formatted and enhanced with additional security directives.

```apache
<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Force HTTPS
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

# Explicitly disable directory browsing
<IfModule mod_autoindex.c>
    Options -Indexes
</IfModule>

# Return 403 for directory access attempts
<FilesMatch "^$">
    Order Allow,Deny
    Deny from all
</FilesMatch>
```

#### Nginx Configuration (if applicable)

For environments using Nginx, the server block configuration will include:

```nginx
location / {
    try_files $uri $uri/ /index.php?$query_string;
    autoindex off;
}

# Explicitly deny access to directories without index files
location ~ ^/(.+)/$ {
    return 403;
}
```

### 2. Application Layer Components

#### Directory Protection Middleware

A new Laravel middleware will be created to provide an additional layer of protection:

```php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class PreventDirectoryListing
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if the request is attempting to access a directory
        $path = $request->path();
        if (substr($path, -1) === '/' && !file_exists(public_path($path . 'index.php')) && !file_exists(public_path($path . 'index.html'))) {
            return redirect('/');
        }

        return $next($request);
    }
}
```

This middleware will be registered in the `app/Http/Kernel.php` file in the `$middleware` array to apply it globally.

### 3. Fallback Layer Components

#### Custom Error Pages

Custom error pages for 403 (Forbidden) and 404 (Not Found) responses will be created or updated to provide a consistent user experience when directory access is denied:

- `resources/views/errors/403.blade.php`
- `resources/views/errors/404.blade.php`

## Data Models

No new data models are required for this feature.

## Error Handling

1. **403 Forbidden Responses**: When a user attempts to access a directory directly, they will receive a 403 Forbidden response with a custom error page.

2. **Logging**: Failed directory access attempts will be logged for security monitoring purposes. This will be implemented by extending the existing logging configuration in `config/logging.php`.

3. **Notification System**: For repeated access attempts that might indicate a security probe, the system will log these events with a higher severity level.

## Testing Strategy

### 1. Unit Testing

- Test the `PreventDirectoryListing` middleware with various path scenarios
- Verify that the middleware correctly identifies directory access attempts
- Ensure proper redirection or error responses are returned

### 2. Integration Testing

- Test the complete request lifecycle with the middleware in place
- Verify that the middleware integrates correctly with the Laravel routing system
- Ensure that legitimate requests are not affected by the protection measures

### 3. Security Testing

- Attempt to access all previously vulnerable directories to verify protection
- Test with different HTTP methods (GET, POST, etc.)
- Test with various URL encoding techniques that might bypass simple protections
- Verify protection works across all environments (local, staging, production)

### 4. Deployment Testing

- Create a checklist for verifying directory listing protection after each deployment
- Include automated tests in the CI/CD pipeline to verify protection is active
- Document the testing process for future reference

## Implementation Considerations

1. **Environment Consistency**: Ensure that the protection measures work consistently across all environments by standardizing server configurations.

2. **Performance Impact**: The middleware solution has minimal performance impact as it only performs additional checks for requests that end with a slash.

3. **Backward Compatibility**: The implementation should not affect any legitimate functionality of the application.

4. **Documentation**: Update deployment documentation to include verification steps for directory listing protection.

## Diagrams

### Security Flow Diagram

```mermaid
flowchart TD
    A[User Request] --> B{Is Directory?}
    B -->|Yes| C{Has index file?}
    B -->|No| D[Process Normal Request]
    C -->|Yes| E[Serve Index File]
    C -->|No| F{Server Config Blocks?}
    F -->|Yes| G[Return 403 Forbidden]
    F -->|No| H{Middleware Blocks?}
    H -->|Yes| I[Redirect to Home]
    H -->|No| J[Fallback: Custom Error]
```

### Implementation Layers

```mermaid
flowchart TD
    A[Directory Protection] --> B[Server Configuration Layer]
    A --> C[Application Layer]
    A --> D[Fallback Layer]
    
    B --> B1[Apache .htaccess]
    B --> B2[Nginx config]
    
    C --> C1[PreventDirectoryListing Middleware]
    
    D --> D1[Custom Error Pages]
    D --> D2[Enhanced Logging]
```