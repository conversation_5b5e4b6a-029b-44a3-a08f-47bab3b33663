// Account Signup JavaScript

// Global variables
let captchaAnswer = 0;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    generateCaptcha();
    detectDevice();
    initializePasswordToggles();
    initializeFormHandlers();
});

// Password toggle functionality
function togglePassword(fieldId) {
    const passwordField = document.getElementById(fieldId);
    const eyeIcon = document.getElementById(fieldId + '-eye');
    
    if (passwordField && eyeIcon) {
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            eyeIcon.classList.remove('fa-eye');
            eyeIcon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            eyeIcon.classList.remove('fa-eye-slash');
            eyeIcon.classList.add('fa-eye');
        }
    }
}

// Initialize password toggle buttons
function initializePasswordToggles() {
    const passwordToggleBtns = document.querySelectorAll('.password-toggle-btn');
    passwordToggleBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const fieldId = this.getAttribute('data-field');
            if (fieldId) {
                togglePassword(fieldId);
            }
        });
    });
}

// Generate captcha
function generateCaptcha() {
    const num1 = Math.floor(Math.random() * 10) + 1;
    const num2 = Math.floor(Math.random() * 10) + 1;
    const operators = ['+', '-', '*'];
    const operator = operators[Math.floor(Math.random() * operators.length)];
    
    let question = `${num1} ${operator} ${num2}`;
    
    switch(operator) {
        case '+':
            captchaAnswer = num1 + num2;
            break;
        case '-':
            captchaAnswer = num1 - num2;
            break;
        case '*':
            captchaAnswer = num1 * num2;
            break;
    }
    
    const captchaDisplay = document.getElementById('captcha-display');
    if (captchaDisplay) {
        captchaDisplay.textContent = question;
    }
    
    // Clear captcha input
    const captchaInput = document.getElementById('captcha');
    if (captchaInput) {
        captchaInput.value = '';
    }
}

// Initialize captcha refresh button
function initializeCaptchaButton() {
    const captchaBtn = document.querySelector('.captcha-refresh-btn');
    if (captchaBtn) {
        captchaBtn.addEventListener('click', generateCaptcha);
    }
}

// Device detection
function detectDevice() {
    const userAgent = navigator.userAgent.toLowerCase();
    let deviceType = 'Desktop';
    let iconClass = 'fas fa-desktop text-white';

    if (/mobile|android|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent)) {
        deviceType = 'Mobile Device';
        iconClass = 'fas fa-mobile-alt text-white';
    } else if (/tablet|ipad/i.test(userAgent)) {
        deviceType = 'Tablet';
        iconClass = 'fas fa-tablet-alt text-white';
    }

    // Update current device display
    const currentDeviceText = document.getElementById('current-device');
    const deviceIcon = document.getElementById('device-icon');
    
    if (currentDeviceText) {
        currentDeviceText.textContent = deviceType;
    }
    
    if (deviceIcon) {
        deviceIcon.className = iconClass + ' me-2';
    }

    // Update modal content
    const modalDeviceType = document.getElementById('modal-device-type');
    const modalDeviceIcon = document.getElementById('modal-device-icon');
    
    if (modalDeviceType) {
        modalDeviceType.textContent = deviceType;
    }
    
    if (modalDeviceIcon) {
        modalDeviceIcon.className = iconClass.replace('text-white', '') + ' device-icon';
    }
}

// Form validation
function validateForm() {
    const form = document.querySelector('form[action="/signup"]');
    if (!form) return false;

    const formData = new FormData(form);
    const captchaInput = document.getElementById('captcha');
    
    // Validate captcha
    if (captchaInput && parseInt(captchaInput.value) !== captchaAnswer) {
        alert('Captcha verification failed. Please try again.');
        generateCaptcha();
        captchaInput.value = '';
        captchaInput.focus();
        return false;
    }

    // Validate password match
    const password = formData.get('password');
    const passwordConfirmation = formData.get('password_confirmation');
    
    if (password !== passwordConfirmation) {
        alert('Passwords do not match.');
        return false;
    }

    // Validate terms acceptance
    const termsCheckbox = document.getElementById('terms');
    if (termsCheckbox && !termsCheckbox.checked) {
        alert('Please accept the Terms and Conditions.');
        termsCheckbox.focus();
        return false;
    }

    return true;
}

// Confirm and submit form
function confirmAndSubmit() {
    if (validateForm()) {
        // Close the modal first
        const modal = document.getElementById('deviceConfirmModal');
        if (modal) {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
        }
        
        // Submit the form
        const form = document.querySelector('form[action="/signup"]');
        if (form) {
            form.submit();
        }
    }
}

// Initialize form handlers
function initializeFormHandlers() {
    // Initialize captcha button
    initializeCaptchaButton();
    
    // Handle form submission
    const form = document.querySelector('form[action="/signup"]');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                return false;
            }
        });
    }

    // Handle confirm button
    const confirmBtn = document.querySelector('.confirm-submit-btn');
    if (confirmBtn) {
        confirmBtn.addEventListener('click', confirmAndSubmit);
    }

    // Handle captcha refresh button
    const captchaRefreshBtn = document.querySelector('.captcha-refresh-btn');
    if (captchaRefreshBtn) {
        captchaRefreshBtn.addEventListener('click', generateCaptcha);
    }

    // Handle modal show event
    const deviceModal = document.getElementById('deviceConfirmModal');
    if (deviceModal) {
        deviceModal.addEventListener('show.bs.modal', function () {
            // Update modal with current device info when it opens
            detectDevice();
        });
    }
}

// Show device confirmation modal
function showDeviceConfirmation() {
    const modal = document.getElementById('deviceConfirmModal');
    if (modal) {
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
    }
}

// Handle back to form
function backToForm() {
    const modal = document.getElementById('deviceConfirmModal');
    if (modal) {
        const bsModal = bootstrap.Modal.getInstance(modal);
        if (bsModal) {
            bsModal.hide();
        }
    }
}

// Initialize back button
document.addEventListener('DOMContentLoaded', function() {
    const backBtn = document.querySelector('.back-to-form-btn');
    if (backBtn) {
        backBtn.addEventListener('click', backToForm);
    }
});

// Make functions globally available for inline event handlers
window.togglePassword = togglePassword;
window.generateCaptcha = generateCaptcha;
window.confirmAndSubmit = confirmAndSubmit;