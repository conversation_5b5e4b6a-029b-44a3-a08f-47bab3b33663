<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\UserDevice;
use App\Services\DeviceDetectionService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class DeviceRestrictionMiddleware
{
    protected $deviceDetectionService;

    public function __construct(DeviceDetectionService $deviceDetectionService)
    {
        $this->deviceDetectionService = $deviceDetectionService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (Auth::check()) {
            $user = Auth::user();

            // Debug logging for admin status
            Log::info('Device restriction middleware check', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'isAdmin' => $user->isAdmin(),
                'isSuperAdmin' => $user->isSuperAdmin(),
                'roles' => $user->roles->pluck('name')->toArray(),
                'route' => $request->route()->getName(),
                'url' => $request->url()
            ]);

            // Skip restrictions for admin and superadmin
            if ($user->isAdmin() || $user->isSuperAdmin()) {
                Log::info('Skipping device restrictions for admin user', [
                    'user_id' => $user->id,
                    'user_email' => $user->email
                ]);
                return $next($request);
            }

            // Temporary fix: Skip restrictions for specific user
            if ($user->email === '<EMAIL>') {
                Log::info('Skipping device restrictions for specific user (temporary fix)', [
                    'user_id' => $user->id,
                    'user_email' => $user->email
                ]);
                return $next($request);
            }

            $deviceId = $this->deviceDetectionService->generateDeviceId($request);
            $currentIp = $this->deviceDetectionService->getIpAddress($request);
            $currentDeviceInfo = $this->deviceDetectionService->getDeviceInfo();
            $currentDeviceType = $currentDeviceInfo['device_type'];

            // Check if user has a primary device
            $primaryDevice = $user->primaryDevice();

            if ($primaryDevice) {
                // Get primary device type from database
                $primaryDeviceType = $primaryDevice->device_type ?? $this->getDeviceTypeFromName($primaryDevice->device_name);

                // STRICT DEVICE TYPE ENFORCEMENT
                if ($currentDeviceType !== $primaryDeviceType) {
                    Log::warning('Login attempt from different device type', [
                        'user_id' => $user->id,
                        'primary_device_type' => $primaryDeviceType,
                        'current_device_type' => $currentDeviceType,
                        'ip' => $currentIp,
                        'device_id' => $deviceId
                    ]);

                    Auth::logout();
                    $request->session()->invalidate();
                    $request->session()->regenerateToken();

                    return redirect()->route('device.restriction.error')
                        ->with('error', "🚫 Device Type Mismatch")
                        ->with('error_details', "You originally registered using a {$primaryDeviceType} device. For security reasons, you can only access your account from the same device type. Please use your {$primaryDeviceType} device to login.")
                        ->with('error_type', 'device_mismatch');
                }

                // Check if current device matches primary device
                if ($primaryDevice->device_id !== $deviceId) {
                    // Check if user has already reached max IP addresses
                    $uniqueIpCount = $user->devices()->distinct('ip_address')->count('ip_address');
                    $existingIpDevice = $user->devices()->where('ip_address', $currentIp)->first();

                    // Allow if current IP is already in the allowed list (up to 3 IPs)
                    if ($existingIpDevice) {
                        // Update last login time
                        $existingIpDevice->update(['last_login_at' => now()]);
                    }
                    // If IP not already in list, check if we can add a new one
                    elseif ($uniqueIpCount >= 3) {
                        // Log the access attempt
                        Log::warning('Login attempt from unauthorized IP address', [
                            'user_id' => $user->id,
                            'ip' => $currentIp,
                            'device_id' => $deviceId
                        ]);

                        Auth::logout();
                        $request->session()->invalidate();
                        $request->session()->regenerateToken();

                        return redirect()->route('device.restriction.error')
                            ->with('error', "🌐 Maximum IP Addresses Reached")
                            ->with('error_details', "For security reasons, your account can only be accessed from a maximum of 3 different internet connections (IP addresses). You have already used all 3 allowed connections. Please login from one of your previously used locations or contact our support team for assistance.")
                            ->with('error_type', 'ip_limit_reached')
                            ->with('support_contact', '<EMAIL>');
                    } else {
                        // User hasn't reached the IP limit yet, log a warning but allow
                        Log::notice('Login from new IP but under limit', [
                            'user_id' => $user->id,
                            'ip' => $currentIp,
                            'device_id' => $deviceId,
                            'current_ip_count' => $uniqueIpCount,
                            'max_allowed' => 3
                        ]);

                        // Check if device_id already exists for another user
                        $existingDevice = UserDevice::where('device_id', $deviceId)->first();

                        if ($existingDevice) {
                            // If device exists for another user, update the existing record
                            $existingDevice->update([
                                'user_id' => $user->id,
                                'device_name' => $this->deviceDetectionService->getDeviceInfo()['device_name'],
                                'browser' => $this->deviceDetectionService->getDeviceInfo()['browser'],
                                'platform' => $this->deviceDetectionService->getDeviceInfo()['platform'],
                                'ip_address' => $currentIp,
                                'last_login_at' => now(),
                                'is_primary' => false
                            ]);
                        } else {
                            // Create new device record if it doesn't exist
                            UserDevice::create([
                                'user_id' => $user->id,
                                'device_name' => $currentDeviceInfo['device_name'],
                                'browser' => $currentDeviceInfo['browser'],
                                'platform' => $currentDeviceInfo['platform'],
                                'device_type' => $currentDeviceType,
                                'device_id' => $deviceId,
                                'ip_address' => $currentIp,
                                'last_login_at' => now(),
                                'is_primary' => false
                            ]);

                            // Show warning if approaching IP limit
                            if ($uniqueIpCount == 2) {
                                session()->flash('warning', '⚠️ IP Address Limit Warning');
                                session()->flash('warning_details', 'You are now using 3 out of 3 allowed IP addresses. This is your final allowed location. Future logins from new locations will be blocked for security reasons.');
                            }
                        }
                    }
                } else {
                    // Update last login time
                    $primaryDevice->update(['last_login_at' => now()]);
                }
            } else {
                // No primary device found, set this device as primary
                // This handles the case for existing users who don't have a device record
                UserDevice::create([
                    'user_id' => $user->id,
                    'device_name' => $currentDeviceInfo['device_name'],
                    'browser' => $currentDeviceInfo['browser'],
                    'platform' => $currentDeviceInfo['platform'],
                    'device_type' => $currentDeviceType,
                    'device_id' => $deviceId,
                    'ip_address' => $currentIp,
                    'last_login_at' => now(),
                    'is_primary' => true
                ]);
            }
        }

        return $next($request);
    }

    /**
     * Extract device type from device name
     *
     * @param string $deviceName
     * @return string
     */
    private function getDeviceTypeFromName($deviceName)
    {
        if (strpos($deviceName, 'Desktop') !== false) {
            return 'Desktop';
        }
        if (strpos($deviceName, 'Mobile') !== false) {
            return 'Mobile';
        }
        if (strpos($deviceName, 'Tablet') !== false) {
            return 'Tablet';
        }
        return 'Unknown';
    }
}
