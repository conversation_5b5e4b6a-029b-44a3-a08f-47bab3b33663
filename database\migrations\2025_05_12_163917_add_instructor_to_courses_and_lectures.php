<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            $table->string('instructor')->nullable()->after('description');
        });

        Schema::table('lectures', function (Blueprint $table) {
            $table->string('instructor')->nullable()->after('description');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            $table->dropColumn('instructor');
        });

        Schema::table('lectures', function (Blueprint $table) {
            $table->dropColumn('instructor');
        });
    }
};
