<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\AdminPermission;
use App\Models\Role;

class CheckAdminPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $page): Response
    {
        // Check if user is a super admin (set by SuperAdminBypass middleware)
        if ($request->attributes->get('is_super_admin', false)) {
            return $next($request);
        }

        $user = Auth::user();

        // Double-check if user is a super admin directly
        $superAdminRole = Role::where('name', 'Super Admin')->first();
        if ($superAdminRole && $user->roles()->where('role_id', $superAdminRole->id)->exists()) {
            return $next($request);
        }

        // Log for debugging
        Log::info('Permission check for user', [
            'user_id' => $user->id,
            'page' => $page,
            'roles' => $user->roles()->pluck('name')->toArray()
        ]);

        // Check if this admin has permission for this page
        $hasPermission = AdminPermission::where('admin_user_id', $user->id)
            ->where('page', $page)
            ->where('is_allowed', true)
            ->exists();

        if (!$hasPermission) {
            // Redirect to dashboard with error message
            return redirect()->route('admin.dashboard')
                ->with('error', 'You do not have permission to access this page.');
        }

        return $next($request);
    }
}
