<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Shoppingcart;
use App\Models\Course;
use App\Models\Category;
use App\Models\Lecture;
use Illuminate\Support\Facades\Auth;
use Livewire\WithPagination;
use App\Models\UserCourse;
use App\Models\Order;

class CoursesList extends Component
{
    public $courses;
    public $categories;
    public $priceToggle = 'weekly'; // Default toggle
    public $selectedType = 'all'; // Default to all courses
    public $selectedCategory = 'all'; // Default to all categories
    public $selectedInstructor = null; // For filtering by instructor

    public function mount($instructor = null)
    {
        $this->selectedInstructor = $instructor;
        $this->loadCourses();
        $this->categories = Category::all();
    }

    public function loadCourses()
    {
        $query = Course::with([
            'lectures', 
            'ratings' => function($query) {
                $query->where('is_approved', true)
                     ->where('show_publicly', true);
            },
            'lectures.ratings' => function($query) {
                $query->where('is_approved', true)
                     ->where('show_publicly', true);
            }
        ]);

        // Filter by category if not "all"
        if ($this->selectedCategory !== 'all') {
            $query->where('category_id', $this->selectedCategory);
        }

        // Filter by instructor if provided
        if ($this->selectedInstructor) {
            $query->where(function($q) {
                $q->where('instructor', $this->selectedInstructor)
                  ->orWhereHas('lectures', function($subQuery) {
                      $subQuery->where('instructor', $this->selectedInstructor);
                  });
            });
        }

        $this->courses = $query->get();
    }

    public function filterByType($type)
    {
        $this->selectedType = $type;
    }

    public function filterByCategory($categoryId)
    {
        $this->selectedCategory = $categoryId;
        $this->loadCourses();
    }

    public function addToCart($id, $priceType = 'weekly', $type = 'course')
    {
        if (!auth()->check()) {
            session()->flash('error', 'Please login or register to add items to cart.');
            return redirect()->route('sign-in');
        }

        // Check if user is verified
        if (!auth()->user()->email_verified_at || !auth()->user()->phone_verified_at) {
            Auth::logout();
            session()->invalidate();
            session()->regenerateToken();
            return redirect()->route('sign-in')
                ->with('error', 'Please verify your email and phone number to add items to cart.');
        }

        $userId = auth()->id();

        if ($type === 'course') {
            // Add course to cart
            $course = Course::find($id);
            $price = $priceType === 'weekly' ? $course->weekly_price : $course->monthly_price;

            // Check if user has already purchased any lectures from this course
            $purchasedLectures = UserCourse::where('user_id', $userId)
                ->whereNotNull('lecture_id')
                ->where('status', 'active')
                ->whereHas('lecture', function ($query) use ($id) {
                    $query->where('course_id', $id);
                })
                ->with('lecture')
                ->get();

            // If user has purchased lectures from this course, subtract their cost
            $discountAmount = 0;
            foreach ($purchasedLectures as $purchasedLecture) {
                if ($purchasedLecture->lecture) {
                    $lecturePrice = $priceType === 'weekly'
                        ? $purchasedLecture->lecture->weekly_price
                        : $purchasedLecture->lecture->monthly_price;
                    $discountAmount += $lecturePrice;
                }
            }

            // Apply discount - don't go below zero
            $finalPrice = max(0, $price - $discountAmount);

            // Generate a reason message if there's a discount
            $discountReason = null;
            if ($discountAmount > 0) {
                $purchasedLectureNames = $purchasedLectures->pluck('lecture.name')->implode(', ');
                $discountReason = "Discount for previously purchased lecture(s): " . $purchasedLectureNames;
            }

            \App\Models\Shoppingcart::updateOrCreate(
                ['user_id' => $userId, 'course_id' => $id],
                [
                    'price' => $finalPrice,
                    'price_type' => (string)$priceType,
                    'original_price' => $price,
                    'discount_amount' => $discountAmount,
                    'discount_reason' => $discountReason
                ]
            );

            // If there was a discount, show a message
            if ($discountAmount > 0) {
                session()->flash('success', 'Course added to cart! $' . number_format($discountAmount, 2) . ' discount applied for previously purchased lectures.');
            } else {
                session()->flash('success', 'Course added to cart successfully!');
            }
        } else {
            // Add lecture to cart
            $lecture = Lecture::find($id);
            $price = $priceType === 'weekly' ? $lecture->weekly_price : $lecture->monthly_price;

            \App\Models\Shoppingcart::updateOrCreate(
                ['user_id' => $userId, 'lecture_id' => $id],
                ['price' => $price, 'price_type' => (string)$priceType]
            );

            session()->flash('success', 'Lecture added to cart successfully!');
        }

        $this->dispatch('cartUpdated');
    }

    public function isInCart($itemId, $type = 'course')
    {
        if (!auth()->check()) {
            return false;
        }

        // Check if the item itself is in cart
        $directlyInCart = Shoppingcart::where('user_id', auth()->id())
            ->where($type . '_id', $itemId)
            ->exists();

        // If it's directly in cart or not a lecture, return the result
        if ($directlyInCart || $type !== 'lecture') {
            return $directlyInCart;
        }

        // If it's a lecture, also check if its parent course is in cart
        $lecture = Lecture::find($itemId);
        if ($lecture) {
            return Shoppingcart::where('user_id', auth()->id())
                ->where('course_id', $lecture->course_id)
                ->exists();
        }

        return false;
    }

    public function isPurchased($itemId, $type = 'course')
    {
        if (!auth()->check()) {
            return false;
        }

        // Check if the item itself is purchased
        $directPurchase = UserCourse::where('user_id', auth()->id())
            ->where($type.'_id', $itemId)
            ->where('status', 'active')
            ->exists();

        // If it's a direct purchase or not a lecture, return the result
        if ($directPurchase || $type !== 'lecture') {
            return $directPurchase;
        }

        // If it's a lecture, also check if its parent course is purchased
        $lecture = Lecture::find($itemId);
        if ($lecture) {
            return UserCourse::where('user_id', auth()->id())
                ->where('course_id', $lecture->course_id)
                ->where('status', 'active')
                ->exists();
        }

        return false;
    }

    public function isPending($itemId, $type = 'course')
    {
        if (!auth()->check()) {
            return false;
        }

        $userId = auth()->id();
        $pendingOrders = Order::where('user_id', $userId)
            ->whereIn('status', ['pending', 'awaiting_payment'])
            ->get();

        // Check if the item itself is pending
        $directPending = false;
        foreach ($pendingOrders as $order) {
            $cartItems = json_decode($order->cart_items, true);
            if (is_array($cartItems)) {
                foreach ($cartItems as $item) {
                    $itemKey = $type . '_id';
                    if (isset($item[$itemKey]) && $item[$itemKey] == $itemId) {
                        $directPending = true;
                        break 2;
                    }
                }
            }
        }

        // If it's directly pending or not a lecture, return the result
        if ($directPending || $type !== 'lecture') {
            return $directPending;
        }

        // If it's a lecture, also check if its parent course is pending
        $lecture = Lecture::find($itemId);
        if ($lecture) {
            foreach ($pendingOrders as $order) {
                $cartItems = json_decode($order->cart_items, true);
                if (is_array($cartItems)) {
                    foreach ($cartItems as $item) {
                        if (isset($item['course_id']) && $item['course_id'] == $lecture->course_id) {
                            return true;
                        }
                    }
                }
            }
        }

        return false;
    }

    public function render()
    {
        return view('livewire.courseslist', [
            'courses' => $this->courses,
            'categories' => $this->categories,
            'selectedType' => $this->selectedType,
            'selectedCategory' => $this->selectedCategory
        ])->layout('layouts.app');
    }
}
