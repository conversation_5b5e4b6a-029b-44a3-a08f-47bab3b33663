<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Course;
use App\Models\CertificateRequest;
use App\Models\Certificate;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class CertificateController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    /**
     * Display a listing of user's certificates.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get all certificates and pending requests
        $certificates = $user->certificates()->with('course')->get();
        $pendingRequests = $user->certificateRequests()
            ->where('status', 'pending')
            ->with('course')
            ->get();
        $rejectedRequests = $user->certificateRequests()
            ->where('status', 'rejected')
            ->with('course')
            ->get();
            
        return view('user.certificates.index', compact('certificates', 'pendingRequests', 'rejectedRequests'));
    }
    
    /**
     * Request a certificate for a course.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $courseId
     * @return \Illuminate\Http\Response
     */
    public function requestCertificate(Request $request, $courseId)
    {
        $user = Auth::user();
        $course = Course::findOrFail($courseId);
        
        // Check eligibility
        if (!$user->canRequestCertificate($courseId)) {
            $progressPercent = $course->getProgressPercentageForUser($user->id);
            
            // Determine the reason for ineligibility
            if ($progressPercent < 90) {
                return redirect()->back()
                    ->with('error', 'You need to complete at least 90% of the course before requesting a certificate. Your current progress is ' . number_format($progressPercent, 0) . '%.');
            }
            
            // Check if any required quizzes are not passed
            $requiredQuizIds = $course->quizzes()->where('required_for_completion', true)->pluck('id');
            foreach ($requiredQuizIds as $quizId) {
                $quiz = \App\Models\Quiz::find($quizId);
                if (!$quiz->isPassed($user->id)) {
                    return redirect()->back()
                        ->with('error', 'You need to pass all required quizzes before requesting a certificate. You have not passed: ' . $quiz->title);
                }
            }
            
            // Check if they already have a pending request
            if ($user->hasPendingCertificateRequest($courseId)) {
                return redirect()->back()
                    ->with('error', 'You already have a pending certificate request for this course.');
            }
            
            // If they have a certificate already
            if ($user->certificates()->where('course_id', $courseId)->exists()) {
                return redirect()->back()
                    ->with('error', 'You have already been issued a certificate for this course.');
            }
            
            return redirect()->back()
                ->with('error', 'You are not eligible for a certificate at this time.');
        }
        
        // Create certificate request
        CertificateRequest::create([
            'user_id' => $user->id,
            'course_id' => $courseId,
            'status' => 'pending',
        ]);
        
        return redirect()->back()
            ->with('success', 'Your certificate request has been submitted and is pending review.');
    }
    
    /**
     * Download a certificate.
     *
     * @param  int  $certificateId
     * @return \Illuminate\Http\Response
     */
    public function download($certificateId)
    {
        $user = Auth::user();
        $certificate = Certificate::where('id', $certificateId)
            ->where('user_id', $user->id)
            ->firstOrFail();
            
        // Check if the certificate file exists
        if (!$certificate->file_path || !Storage::disk('public')->exists($certificate->file_path)) {
            return redirect()->back()
                ->with('error', 'Certificate file not found. Please contact support.');
        }
        
        $fileName = 'Certificate - ' . $certificate->course->name . '.pdf';
        
        return response()->download(
            storage_path('app/public/' . $certificate->file_path), 
            $fileName
        );
    }
    
    /**
     * View a certificate.
     *
     * @param  int  $certificateId
     * @return \Illuminate\Http\Response
     */
    public function view($certificateId)
    {
        $user = Auth::user();
        $certificate = Certificate::where('id', $certificateId)
            ->where('user_id', $user->id)
            ->with(['course', 'user'])
            ->firstOrFail();
            
        return view('user.certificates.view', compact('certificate'));
    }
}
