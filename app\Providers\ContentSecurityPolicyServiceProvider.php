<?php

namespace App\Providers;

use App\Services\ContentSecurityPolicy\NonceGenerator;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;

class ContentSecurityPolicyServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        // Register the NonceGenerator as a singleton
        $this->app->singleton('csp.nonce', function () {
            return new NonceGenerator();
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Share the nonce with all views
        view()->composer('*', function ($view) {
            $view->with('cspNonce', NonceGenerator::getNonce());
        });

        // Register Blade directives for CSP
        $this->registerBladeDirectives();
        
        // Initialize Livewire CSP compatibility if Livewire is installed
        if (class_exists('\Livewire\Livewire')) {
            if (function_exists('livewire_csp_init')) {
                livewire_csp_init();
            }
        }
    }

    /**
     * Register custom Blade directives for CSP.
     *
     * @return void
     */
    protected function registerBladeDirectives()
    {
        // @cspNonce - Outputs the nonce attribute for script and style tags
        Blade::directive('cspNonce', function () {
            return "<?php echo e(app('csp.nonce')->getNonceAttribute()); ?>";
        });

        // @nonceAttr - Outputs just the nonce attribute value
        Blade::directive('nonceAttr', function () {
            return "<?php echo e(app('csp.nonce')->getNonce()); ?>";
        });
    }
}