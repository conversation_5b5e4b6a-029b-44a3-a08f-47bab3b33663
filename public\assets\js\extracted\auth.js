/**
 * Authentication pages JavaScript functionality
 */

// Password toggle function
function togglePassword(fieldId) {
    const passwordField = document.getElementById(fieldId);
    const eyeIcon = document.getElementById(fieldId + '-eye');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        eyeIcon.classList.remove('fa-eye');
        eyeIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        eyeIcon.classList.remove('fa-eye-slash');
        eyeIcon.classList.add('fa-eye');
    }
}

// Captcha generation function
function generateCaptcha() {
    const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789';
    let captcha = '';
    for (let i = 0; i < 6; i++) {
        captcha += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    // Create a span element with the captcha text
    const captchaDisplay = document.getElementById('captcha-display');
    
    // Clear any existing content
    while (captchaDisplay.firstChild) {
        captchaDisplay.removeChild(captchaDisplay.firstChild);
    }
    
    // Create a text node with the captcha
    const textNode = document.createTextNode(captcha);
    captchaDisplay.appendChild(textNode);
    
    // Set the hidden input value
    document.getElementById('captcha-answer').value = captcha;
    document.getElementById('captcha').value = '';
}

// Initialize auth page functionality
document.addEventListener('DOMContentLoaded', function () {
    // Initialize captcha on page load
    if (document.getElementById('captcha-display')) {
        generateCaptcha();
    }

    // Event delegation for password toggle buttons
    document.addEventListener('click', function (e) {
        if (e.target.closest('.auth-password-toggle')) {
            e.preventDefault();
            const passwordField = e.target.closest('.position-relative').querySelector('input[type="password"], input[type="text"]');
            if (passwordField) {
                togglePassword(passwordField.id);
            }
        }

        // Event delegation for captcha refresh button
        if (e.target.closest('[data-action="refresh-captcha"]')) {
            e.preventDefault();
            generateCaptcha();
        }
    });

    // Form submission with captcha validation
    const signinForm = document.querySelector('form[action="/signin"]');
    if (signinForm) {
        signinForm.addEventListener('submit', function (e) {
            const captchaInput = document.getElementById('captcha').value;
            const captchaAnswer = document.getElementById('captcha-answer').value;

            if (captchaInput !== captchaAnswer) {
                e.preventDefault();
                alert('Captcha verification failed. Please try again.');
                generateCaptcha();
                return false;
            }
        });
    }
});

// Additional auth page functionality

// GitHub buttons initialization
document.addEventListener('DOMContentLoaded', function () {
    // Handle GitHub buttons
    if (typeof window.GitHubButtons !== 'undefined') {
        window.GitHubButtons.init();
    }

    // Handle any form validation
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function (e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('is-invalid');
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('Please fill in all required fields.');
            }
        });
    });

    // Handle password strength indicator if present
    const passwordField = document.getElementById('password');
    if (passwordField) {
        passwordField.addEventListener('input', function () {
            const strength = document.getElementById('password-strength');
            if (strength) {
                // Simple password strength check
                const value = this.value;
                let strengthValue = 0;

                if (value.length >= 8) strengthValue += 1;
                if (/[A-Z]/.test(value)) strengthValue += 1;
                if (/[0-9]/.test(value)) strengthValue += 1;
                if (/[^A-Za-z0-9]/.test(value)) strengthValue += 1;

                // Update strength indicator
                switch (strengthValue) {
                    case 0:
                    case 1:
                        strength.textContent = 'Weak';
                        strength.className = 'text-danger';
                        break;
                    case 2:
                        strength.textContent = 'Medium';
                        strength.className = 'text-warning';
                        break;
                    case 3:
                        strength.textContent = 'Strong';
                        strength.className = 'text-success';
                        break;
                    case 4:
                        strength.textContent = 'Very Strong';
                        strength.className = 'text-success';
                        break;
                }
            }
        });
    }
});

// Fix for GitHub buttons
if (typeof window.GitHubButtons === 'undefined') {
    window.GitHubButtons = {
        init: function () {
            console.log('GitHub Buttons initialized');
        }
    };
}