<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Course;
use App\Models\Lecture;
use App\Models\Order;
use App\Models\UserCourse;

class LectureDetail extends Component
{
    public $course;
    public $lecture;
    public $priceToggle = 'weekly';
    public $features;
    public $instructorProfiles = [];

    public function mount($course, $lecture)
    {
        $this->course = Course::findOrFail($course);
        $this->lecture = Lecture::where('id', $lecture)
                               ->where('course_id', $this->course->id)
                               ->with(['ratings' => function($query) {
                                   $query->where('is_approved', true)
                                        ->where('show_publicly', true)
                                        ->with('user')
                                        ->latest();
                               }])
                               ->firstOrFail();

        // Handle instructor display - if lecture has no instructor, use course instructor
        if (!$this->lecture->instructor && $this->course->instructor) {
            $this->lecture->instructor = $this->course->instructor;
        } elseif (!$this->lecture->instructor) {
            $this->lecture->instructor = 'Instructor'; // Default value if none set
        }

        $this->features = [
            'learn' => $this->lecture->getFeaturesByType('learn'),
            'requirement' => $this->lecture->getFeaturesByType('requirement'),
        ];

        // Load instructor profiles
        $this->loadInstructorProfiles();
    }

    /**
     * Load instructor profiles for the lecture and course
     */
    private function loadInstructorProfiles()
    {
        // Get instructor names
        $instructorNames = collect([$this->lecture->instructor, $this->course->instructor])
                            ->filter()
                            ->unique()
                            ->values();

        if ($instructorNames->isEmpty()) {
            return;
        }

        // Load instructor profiles with debug info
        $profiles = \App\Models\InstructorProfile::whereIn('name', $instructorNames)
            ->where('is_active', true)
            ->get();

        // Log retrieved profiles for debugging
        \Log::info('Instructor profiles loaded for lecture', [
            'lecture_id' => $this->lecture->id,
            'instructor_names' => $instructorNames->toArray(),
            'profiles_count' => $profiles->count(),
            'profiles' => $profiles->pluck('name', 'id')->toArray()
        ]);

        $this->instructorProfiles = $profiles->keyBy('name')->toArray();
    }

    public function addToCart($id, $priceType)
    {
        if (!auth()->check()) {
            session()->flash('error', 'Please log in to add items to your cart.');
            return redirect()->route('sign-in');
        }

        $userId = auth()->id();
        $lecturePrice = $priceType === 'weekly' ? $this->lecture->weekly_price : $this->lecture->monthly_price;

        \App\Models\Shoppingcart::updateOrCreate(
            ['user_id' => $userId, 'lecture_id' => $id],
            ['price' => $lecturePrice, 'price_type' => $priceType]
        );

        session()->flash('success', 'Lecture added to cart successfully!');

        // Dispatch event to update cart icon
        $this->dispatch('cartUpdated');
    }

    public function isPurchased($itemId, $type = 'lecture')
    {
        if (!auth()->check()) {
            return false;
        }

        return UserCourse::where('user_id', auth()->id())
            ->where($type.'_id', $itemId)
            ->where('status', 'active')
            ->exists();
    }

    public function isPending($itemId, $type = 'lecture')
    {
        if (!auth()->check()) {
            return false;
        }

        $userId = auth()->id();
        $pendingOrders = Order::where('user_id', $userId)
            ->whereIn('status', ['pending', 'awaiting_payment'])
            ->get();

        foreach ($pendingOrders as $order) {
            $cartItems = json_decode($order->cart_items, true);
            if (is_array($cartItems)) {
                foreach ($cartItems as $item) {
                    $itemKey = $type . '_id';
                    if (isset($item[$itemKey]) && $item[$itemKey] == $itemId) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    public function render()
    {
        return view('livewire.lecture-detail')->layout('layouts.app');
    }
}
