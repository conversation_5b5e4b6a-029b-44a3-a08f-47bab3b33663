/**
 * Global CSP Fix Script
 * This script handles all CSP violations across the application
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get CSP nonce from meta tag
    const nonceMetaTag = document.querySelector('meta[name="csp-nonce"]');
    const nonce = nonceMetaTag ? nonceMetaTag.getAttribute('content') : null;
    
    if (!nonce) {
        console.warn('CSP nonce not found in meta tag');
        return;
    }
    
    // Function to add nonce to all style and script elements
    function addNonceToElements() {
        // Add nonce to style elements
        document.querySelectorAll('style:not([nonce])').forEach(style => {
            style.setAttribute('nonce', nonce);
        });
        
        // Add nonce to script elements
        document.querySelectorAll('script:not([nonce])').forEach(script => {
            script.setAttribute('nonce', nonce);
        });
        
        // Handle inline styles
        document.querySelectorAll('[style]').forEach(element => {
            // Get the inline style
            const inlineStyle = element.getAttribute('style');
            
            // Remove the inline style
            element.removeAttribute('style');
            
            // Create a unique class name for this element
            const uniqueClassName = 'csp-fix-' + Math.random().toString(36).substr(2, 9);
            
            // Add the class to the element
            element.classList.add(uniqueClassName);
            
            // Create a style element with the nonce
            const styleElement = document.createElement('style');
            styleElement.setAttribute('nonce', nonce);
            styleElement.textContent = '.' + uniqueClassName + ' {' + inlineStyle + '}';
            
            // Add the style element to the head
            document.head.appendChild(styleElement);
        });
        
        // Handle onclick attributes
        document.querySelectorAll('[onclick]').forEach(element => {
            // Get the onclick attribute
            const onclickCode = element.getAttribute('onclick');
            
            // Remove the onclick attribute
            element.removeAttribute('onclick');
            
            // Add an event listener instead
            element.addEventListener('click', function(event) {
                // Create a new function from the onclick code
                try {
                    // Create a function that takes 'this' and 'event' as parameters
                    const fn = new Function('this', 'event', onclickCode);
                    
                    // Call the function with the element as 'this' and the event
                    fn.call(element, event);
                } catch (error) {
                    console.error('Error executing onclick code:', error);
                }
            });
        });
    }
    
    // Run immediately
    addNonceToElements();
    
    // Set up a MutationObserver to watch for new elements
    const observer = new MutationObserver(mutations => {
        let needsUpdate = false;
        
        mutations.forEach(mutation => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) { // Element node
                        // Check for style, script, inline style, or onclick attributes
                        if ((node.tagName === 'STYLE' || node.tagName === 'SCRIPT') && !node.hasAttribute('nonce')) {
                            needsUpdate = true;
                        }
                        
                        if (node.hasAttribute && (node.hasAttribute('style') || node.hasAttribute('onclick'))) {
                            needsUpdate = true;
                        }
                        
                        // Check for elements within the added node
                        if (node.querySelectorAll) {
                            const elements = node.querySelectorAll('style:not([nonce]), script:not([nonce]), [style], [onclick]');
                            if (elements.length > 0) {
                                needsUpdate = true;
                            }
                        }
                    }
                });
            } else if (mutation.type === 'attributes') {
                // If a style or onclick attribute was added
                if (mutation.attributeName === 'style' || mutation.attributeName === 'onclick') {
                    needsUpdate = true;
                }
            }
        });
        
        if (needsUpdate) {
            // Update all elements
            addNonceToElements();
        }
    });
    
    // Start observing the document with all possible mutation types
    observer.observe(document.documentElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'onclick']
    });
    
    // Run periodically to catch any missed elements
    setInterval(addNonceToElements, 500);
});