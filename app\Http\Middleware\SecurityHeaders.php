<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SecurityHeaders
{
    /**
     * Add security headers to the response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Force HTTPS in production (before processing request)
        if (!$request->secure() && app()->environment('production')) {
            return redirect()->secure($request->getRequestUri(), 301);
        }

        $response = $next($request);

        // Enhanced security headers
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'DENY'); // Changed from SAMEORIGIN to DENY for better security
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        $response->headers->set('Permissions-Policy', 'accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()');

        // Enable strict transport security with preload (only in production)
        if (app()->environment('production')) {
            $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
        }

        // Prevent external redirects to insecure endpoints
        if ($response->isRedirect()) {
            $location = $response->headers->get('Location');
            if ($location && $this->isInsecureExternalRedirect($location)) {
                abort(403, 'Insecure external redirect blocked');
            }
        }

        // Add additional security headers
        $response->headers->set('X-Permitted-Cross-Domain-Policies', 'none');
        $response->headers->set('Cross-Origin-Embedder-Policy', 'require-corp');
        $response->headers->set('Cross-Origin-Opener-Policy', 'same-origin');
        $response->headers->set('Cross-Origin-Resource-Policy', 'same-origin');

        return $response;
    }

    /**
     * Check if redirect is to an insecure external endpoint
     */
    private function isInsecureExternalRedirect(string $location): bool
    {
        // Block HTTP redirects to external IPs or domains
        if (str_starts_with($location, 'http://')) {
            $host = parse_url($location, PHP_URL_HOST);
            
            // Block redirects to IP addresses
            if (filter_var($host, FILTER_VALIDATE_IP)) {
                return true;
            }
            
            // Block redirects to non-standard ports (like 2030)
            $port = parse_url($location, PHP_URL_PORT);
            if ($port && !in_array($port, [80, 443])) {
                return true;
            }
            
            // Block redirects to external domains
            $currentHost = request()->getHost();
            if ($host !== $currentHost) {
                return true;
            }
        }

        return false;
    }
}
