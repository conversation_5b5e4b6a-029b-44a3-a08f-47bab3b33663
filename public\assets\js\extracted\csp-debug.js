/**
 * CSP Debug Script
 * This script helps identify what's causing CSP violations
 */

(function() {
    'use strict';
    
    const nonce = document.querySelector('meta[name="csp-nonce"]')?.getAttribute('content');
    
    if (!nonce) {
        console.error('CSP Debug: No nonce found');
        return;
    }
    
    console.log('CSP Debug: Starting with nonce:', nonce);
    
    // Function to scan for CSP violations
    function scanForViolations() {
        const violations = [];
        
        // Check for inline styles
        const elementsWithStyle = document.querySelectorAll('[style]');
        elementsWithStyle.forEach((element, index) => {
            violations.push({
                type: 'inline-style',
                element: element,
                content: element.getAttribute('style'),
                location: `Element ${index + 1}: ${element.tagName}${element.id ? '#' + element.id : ''}${element.className ? '.' + element.className.split(' ').join('.') : ''}`
            });
        });
        
        // Check for inline event handlers
        const eventTypes = ['onclick', 'onchange', 'onsubmit', 'onload', 'onerror', 'onmouseover', 'onmouseout'];
        eventTypes.forEach(eventType => {
            const elements = document.querySelectorAll('[' + eventType + ']');
            elements.forEach((element, index) => {
                violations.push({
                    type: 'inline-event',
                    element: element,
                    content: element.getAttribute(eventType),
                    location: `Element ${index + 1}: ${element.tagName}${element.id ? '#' + element.id : ''}${element.className ? '.' + element.className.split(' ').join('.') : ''}`
                });
            });
        });
        
        // Check for scripts without nonce
        const scriptsWithoutNonce = document.querySelectorAll('script:not([src]):not([nonce])');
        scriptsWithoutNonce.forEach((script, index) => {
            violations.push({
                type: 'script-no-nonce',
                element: script,
                content: script.textContent.substring(0, 100) + '...',
                location: `Script ${index + 1}`
            });
        });
        
        // Check for styles without nonce
        const stylesWithoutNonce = document.querySelectorAll('style:not([nonce])');
        stylesWithoutNonce.forEach((style, index) => {
            violations.push({
                type: 'style-no-nonce',
                element: style,
                content: style.textContent.substring(0, 100) + '...',
                location: `Style ${index + 1}`
            });
        });
        
        return violations;
    }
    
    // Function to log violations
    function logViolations() {
        const violations = scanForViolations();
        
        if (violations.length === 0) {
            console.log('CSP Debug: No violations found! ✅');
            return;
        }
        
        console.group('CSP Debug: Found ' + violations.length + ' potential violations:');
        
        violations.forEach((violation, index) => {
            console.group(`${index + 1}. ${violation.type.toUpperCase()}`);
            console.log('Location:', violation.location);
            console.log('Content:', violation.content);
            console.log('Element:', violation.element);
            console.groupEnd();
        });
        
        console.groupEnd();
        
        return violations;
    }
    
    // Function to get line numbers of elements (approximate)
    function getElementLineNumber(element) {
        try {
            const html = document.documentElement.outerHTML;
            const elementHTML = element.outerHTML;
            const lines = html.split('\n');
            
            for (let i = 0; i < lines.length; i++) {
                if (lines[i].includes(elementHTML.substring(0, 50))) {
                    return i + 1;
                }
            }
        } catch (error) {
            // Ignore errors
        }
        return 'unknown';
    }
    
    // Function to monitor for new violations
    function startMonitoring() {
        const observer = new MutationObserver(function(mutations) {
            let hasNewViolations = false;
            
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            if (node.hasAttribute && (
                                node.hasAttribute('style') ||
                                node.hasAttribute('onclick') ||
                                (node.tagName === 'SCRIPT' && !node.hasAttribute('nonce') && !node.hasAttribute('src')) ||
                                (node.tagName === 'STYLE' && !node.hasAttribute('nonce'))
                            )) {
                                hasNewViolations = true;
                            }
                        }
                    });
                } else if (mutation.type === 'attributes') {
                    if (mutation.attributeName === 'style' || mutation.attributeName.startsWith('on')) {
                        hasNewViolations = true;
                    }
                }
            });
            
            if (hasNewViolations) {
                console.warn('CSP Debug: New violations detected!');
                setTimeout(logViolations, 100);
            }
        });
        
        observer.observe(document.documentElement, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'onclick', 'onchange', 'onsubmit', 'onload', 'onerror']
        });
        
        console.log('CSP Debug: Monitoring started');
    }
    
    // Initialize
    function initialize() {
        console.log('CSP Debug: Initializing...');
        
        // Initial scan
        setTimeout(function() {
            console.log('CSP Debug: Initial scan...');
            logViolations();
        }, 100);
        
        // Scan after DOM is fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(function() {
                    console.log('CSP Debug: DOM loaded scan...');
                    logViolations();
                }, 100);
            });
        }
        
        // Scan after everything is loaded
        window.addEventListener('load', function() {
            setTimeout(function() {
                console.log('CSP Debug: Window loaded scan...');
                logViolations();
            }, 100);
        });
        
        // Start monitoring
        startMonitoring();
        
        // Periodic scans
        setInterval(function() {
            const violations = scanForViolations();
            if (violations.length > 0) {
                console.warn('CSP Debug: Periodic scan found', violations.length, 'violations');
            }
        }, 5000);
    }
    
    // Start immediately
    initialize();
    
    // Expose global function
    window.cspDebug = {
        scan: scanForViolations,
        log: logViolations,
        nonce: nonce
    };
    
})();