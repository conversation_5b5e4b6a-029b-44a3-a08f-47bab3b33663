<?php

namespace App\Http\Middleware;

use App\Providers\RouteServiceProvider;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;
use App\Models\UserCourse;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$guards): Response
    {
        $guards = empty($guards) ? [null] : $guards;

        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {
                // Get authenticated user
                $user = Auth::guard($guard)->user();

                // Check if user needs verification
                if (!$user->email_verified_at || !$user->phone_verified_at) {
                    return redirect()->route('verification.notice');
                }

                // Check user roles and redirect accordingly
                if ($user->isSuperAdmin() || $user->isAdmin()) {
                    return redirect('/admin');
                }

                // Check if user has any purchases
                $hasPurchases = UserCourse::where('user_id', $user->id)
                    ->where('status', 'active')
                    ->exists();

                if ($hasPurchases) {
                    return redirect()->route('user.dashboard');
                }

                // Default redirect for authenticated users with no special conditions
                return redirect()->route('authenticated.dashboard');
            }
        }

        return $next($request);
    }
}
