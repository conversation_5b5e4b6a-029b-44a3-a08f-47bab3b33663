<?php
/**
 * Test Root Route Functionality
 * Tests if the root route is working correctly
 */

$baseUrl = 'http://127.0.0.1:8000';

echo "🧪 ROOT ROUTE FUNCTIONALITY TEST\n";
echo "================================\n\n";

function testRoute($url, $description) {
    echo "Testing: $description\n";
    echo "URL: $url\n";
    
    // Initialize cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_MAXREDIRS, 5);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Test Browser)');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $finalUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
    $contentLength = curl_getinfo($ch, CURLINFO_CONTENT_LENGTH_DOWNLOAD);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "  ❌ Connection Error: $error\n";
        return false;
    }
    
    echo "  HTTP Code: $httpCode\n";
    echo "  Final URL: $finalUrl\n";
    echo "  Content Length: " . ($contentLength > 0 ? $contentLength . " bytes" : "Unknown") . "\n";
    
    // Check if response has content
    $bodyStart = strpos($response, "\r\n\r\n");
    if ($bodyStart !== false) {
        $body = substr($response, $bodyStart + 4);
        $bodyLength = strlen(trim($body));
        echo "  Body Length: $bodyLength bytes\n";
        
        if ($bodyLength === 0) {
            echo "  ⚠️  WARNING: Empty response body\n";
        } elseif ($bodyLength < 100) {
            echo "  ⚠️  WARNING: Very short response\n";
            echo "  Body Preview: " . substr(trim($body), 0, 100) . "\n";
        } else {
            echo "  ✅ Response has content\n";
            // Check for common HTML elements
            if (stripos($body, '<html') !== false) {
                echo "  ✅ Valid HTML response\n";
            } else {
                echo "  ⚠️  Response doesn't appear to be HTML\n";
            }
        }
    }
    
    switch ($httpCode) {
        case 200:
            echo "  ✅ SUCCESS - Page loaded successfully\n";
            return true;
        case 302:
        case 301:
            echo "  ✅ SUCCESS - Redirected correctly\n";
            return true;
        case 500:
            echo "  ❌ ERROR - Server error (500)\n";
            return false;
        case 404:
            echo "  ❌ ERROR - Page not found (404)\n";
            return false;
        default:
            echo "  ⚠️  HTTP $httpCode - Needs review\n";
            return false;
    }
}

// Test routes
echo "1️⃣  TESTING ROOT ROUTE\n";
echo "=====================\n";
testRoute($baseUrl . '/', 'Root Route (/)');
echo "\n";

echo "2️⃣  TESTING DASHBOARD ROUTE\n";
echo "===========================\n";
testRoute($baseUrl . '/dashboard', 'Dashboard Route (/dashboard)');
echo "\n";

echo "3️⃣  TESTING SIGN-IN ROUTE\n";
echo "=========================\n";
testRoute($baseUrl . '/sign-in', 'Sign In Route (/sign-in)');
echo "\n";

echo "4️⃣  TESTING COURSES ROUTE\n";
echo "=========================\n";
testRoute($baseUrl . '/courses', 'Courses Route (/courses)');
echo "\n";

echo "📊 TEST SUMMARY\n";
echo "===============\n";
echo "If the root route shows empty content or very short response,\n";
echo "there might be an issue with:\n";
echo "1. View rendering\n";
echo "2. Asset loading\n";
echo "3. JavaScript errors\n";
echo "4. CSP blocking resources\n";
echo "5. Database connection issues\n";
?>
