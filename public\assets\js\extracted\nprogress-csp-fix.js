/**
 * NProgress CSP Fix
 * Specifically handles NProgress styles generated by Livewire
 */

(function() {
    'use strict';
    
    // Get nonce
    const nonce = document.querySelector('meta[name="csp-nonce"]')?.getAttribute('content') || 
                  document.currentScript?.getAttribute('nonce');
    
    if (!nonce) {
        console.error('NProgress CSP Fix: No nonce available');
        return;
    }
    
    console.log('NProgress CSP Fix: Starting with nonce:', nonce);
    
    // Function to fix NProgress styles
    function fixNProgressStyles() {
        // Find all styles that contain nprogress content
        const allStyles = document.querySelectorAll('style');
        allStyles.forEach(style => {
            if (!style.hasAttribute('nonce') && style.textContent && (
                style.textContent.includes('nprogress') ||
                style.textContent.includes('.nprogress-custom-parent') ||
                style.textContent.includes('@-webkit-keyframes nprogress-spinner') ||
                style.textContent.includes('@keyframes nprogress-spinner')
            )) {
                style.setAttribute('nonce', nonce);
                console.log('NProgress CSP Fix: Added nonce to NProgress style');
            }
        });
        
        // Fix any elements with nprogress-related inline styles
        const elementsWithStyle = document.querySelectorAll('[style]');
        elementsWithStyle.forEach(element => {
            const inlineStyle = element.getAttribute('style');
            if (inlineStyle && (
                inlineStyle.includes('nprogress') ||
                element.classList.contains('nprogress-custom-parent') ||
                element.classList.contains('nprogress-bar') ||
                element.id === 'nprogress'
            )) {
                element.removeAttribute('style');
                const className = 'nprogress-csp-' + Date.now();
                element.classList.add(className);
                
                const style = document.createElement('style');
                style.setAttribute('nonce', nonce);
                style.textContent = '.' + className + ' { ' + inlineStyle + ' }';
                document.head.appendChild(style);
                
                console.log('NProgress CSP Fix: Fixed inline style for NProgress element');
            }
        });
    }
    
    // Override the getNonce function that Livewire might use
    if (typeof window.getNonce === 'undefined') {
        window.getNonce = function() {
            return nonce;
        };
    }
    
    // Run immediately
    fixNProgressStyles();
    
    // Set up observer specifically for NProgress
    const observer = new MutationObserver(function(mutations) {
        let needsFix = false;
        
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        if (node.tagName === 'STYLE' && node.textContent && 
                            node.textContent.includes('nprogress')) {
                            needsFix = true;
                        }
                        
                        if (node.hasAttribute && node.hasAttribute('style') && (
                            node.classList.contains('nprogress-custom-parent') ||
                            node.id === 'nprogress'
                        )) {
                            needsFix = true;
                        }
                    }
                });
            }
        });
        
        if (needsFix) {
            setTimeout(fixNProgressStyles, 1);
        }
    });
    
    observer.observe(document.head, {
        childList: true,
        subtree: true
    });
    
    // Observe body when it's available
    if (document.body) {
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style']
        });
    } else {
        document.addEventListener('DOMContentLoaded', function() {
            if (document.body) {
                observer.observe(document.body, {
                    childList: true,
                    subtree: true,
                    attributes: true,
                    attributeFilter: ['style']
                });
            }
        });
    }
    
    // Run periodically for first 5 seconds
    const interval = setInterval(fixNProgressStyles, 5);
    setTimeout(() => {
        clearInterval(interval);
        console.log('NProgress CSP Fix: Periodic fixing stopped');
    }, 5000);
    
    console.log('NProgress CSP Fix: Initialized');
    
})();