/**
 * Comprehensive CSP Fix
 * This script provides a complete solution for CSP issues across the application
 */

(function() {
    // Execute immediately to patch before DOM content loaded
    // Get CSP nonce from meta tag
    const nonceMetaTag = document.querySelector('meta[name="csp-nonce"]');
    const nonce = nonceMetaTag ? nonceMetaTag.getAttribute('content') : null;
    
    if (!nonce) {
        console.warn('CSP nonce not found in meta tag');
        return;
    }
    
    // Create a container for dynamic styles with nonce
    const dynamicStyleContainer = document.createElement('style');
    dynamicStyleContainer.setAttribute('nonce', nonce);
    dynamicStyleContainer.setAttribute('id', 'dynamic-styles-container');
    document.head.appendChild(dynamicStyleContainer);
    
    // Create a container for dynamic scripts with nonce
    const dynamicScriptContainer = document.createElement('script');
    dynamicScriptContainer.setAttribute('nonce', nonce);
    dynamicScriptContainer.setAttribute('id', 'dynamic-scripts-container');
    document.head.appendChild(dynamicScriptContainer);
    
    // Function to add nonce to all elements that need it
    function addNonceToElements() {
        // Add nonce to all style elements
        document.querySelectorAll('style:not([nonce])').forEach(style => {
            style.setAttribute('nonce', nonce);
        });
        
        // Add nonce to all script elements
        document.querySelectorAll('script:not([nonce])').forEach(script => {
            script.setAttribute('nonce', nonce);
        });
        
        // Handle inline styles
        document.querySelectorAll('[style]').forEach(element => {
            // Get the inline style
            const inlineStyle = element.getAttribute('style');
            
            // Remove the inline style
            element.removeAttribute('style');
            
            // Create a unique class name for this element
            const uniqueClassName = 'csp-fix-' + Math.random().toString(36).substr(2, 9);
            
            // Add the class to the element
            element.classList.add(uniqueClassName);
            
            // Add the style to our dynamic style container
            dynamicStyleContainer.textContent += '.' + uniqueClassName + ' {' + inlineStyle + '}\n';
        });
        
        // Special handling for Livewire styles
        const livewireStylesContainer = document.getElementById('livewire-styles-container');
        if (livewireStylesContainer) {
            const styles = livewireStylesContainer.querySelectorAll('style');
            styles.forEach(style => {
                if (!style.hasAttribute('nonce')) {
                    style.setAttribute('nonce', nonce);
                }
            });
        }
    }
    
    // Override createElement to add nonce to style and script elements
    const originalCreateElement = document.createElement;
    document.createElement = function(tagName) {
        const element = originalCreateElement.apply(document, arguments);
        
        if (tagName.toLowerCase() === 'style' || tagName.toLowerCase() === 'script') {
            element.setAttribute('nonce', nonce);
        }
        
        return element;
    };
    
    // Override insertAdjacentHTML to add nonce to any inserted style or script elements
    const originalInsertAdjacentHTML = Element.prototype.insertAdjacentHTML;
    Element.prototype.insertAdjacentHTML = function(position, html) {
        // Call the original method
        originalInsertAdjacentHTML.call(this, position, html);
        
        // Add nonce to any inserted style or script elements
        const styleElements = this.querySelectorAll('style:not([nonce])');
        const scriptElements = this.querySelectorAll('script:not([nonce])');
        
        styleElements.forEach(style => style.setAttribute('nonce', nonce));
        scriptElements.forEach(script => script.setAttribute('nonce', nonce));
    };
    
    // Override innerHTML to add nonce to any inserted style or script elements
    const originalInnerHTMLDescriptor = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML');
    if (originalInnerHTMLDescriptor && originalInnerHTMLDescriptor.set) {
        Object.defineProperty(Element.prototype, 'innerHTML', {
            set: function(html) {
                // Call the original setter
                originalInnerHTMLDescriptor.set.call(this, html);
                
                // Add nonce to any inserted style or script elements
                const styleElements = this.querySelectorAll('style:not([nonce])');
                const scriptElements = this.querySelectorAll('script:not([nonce])');
                
                styleElements.forEach(style => style.setAttribute('nonce', nonce));
                scriptElements.forEach(script => script.setAttribute('nonce', nonce));
                
                return html;
            },
            get: originalInnerHTMLDescriptor.get,
            configurable: true
        });
    }
    
    // Run immediately
    addNonceToElements();
    
    // Set up a MutationObserver to watch for new elements
    const observer = new MutationObserver(mutations => {
        let needsUpdate = false;
        
        mutations.forEach(mutation => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) {
                        // Check if the node is a style or script element
                        if ((node.tagName === 'STYLE' || node.tagName === 'SCRIPT') && !node.hasAttribute('nonce')) {
                            needsUpdate = true;
                        }
                        
                        // Check if the node has inline style
                        if (node.hasAttribute && node.hasAttribute('style')) {
                            needsUpdate = true;
                        }
                        
                        // Check for style and script elements within the added node
                        if (node.querySelectorAll) {
                            const elements = node.querySelectorAll('style:not([nonce]), script:not([nonce]), [style]');
                            if (elements.length > 0) {
                                needsUpdate = true;
                            }
                        }
                    }
                });
            } else if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                needsUpdate = true;
            }
        });
        
        if (needsUpdate) {
            addNonceToElements();
        }
    });
    
    // Start observing the document with all possible mutation types
    observer.observe(document.documentElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style']
    });
    
    // Special handling for Livewire
    if (typeof window.Livewire !== 'undefined') {
        console.log('Livewire detected, applying comprehensive CSP patches');
        
        // Patch Livewire's style injection
        const originalLivewireStyles = window.Livewire.styles;
        window.Livewire.styles = function() {
            const styles = originalLivewireStyles ? originalLivewireStyles() : '';
            if (styles) {
                const styleElement = document.createElement('style');
                styleElement.textContent = styles;
                document.head.appendChild(styleElement);
                return '';
            }
            return styles;
        };
    }
    
    // Add event listeners for Livewire events
    document.addEventListener('livewire:load', addNonceToElements);
    document.addEventListener('livewire:update', addNonceToElements);
    
    // Run again when DOM is fully loaded
    document.addEventListener('DOMContentLoaded', addNonceToElements);
    
    // Run periodically to catch any missed elements
    setInterval(addNonceToElements, 200);
})();