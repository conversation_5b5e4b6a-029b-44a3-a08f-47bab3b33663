/**
 * CSP Early Patch
 * This script runs before any other scripts to ensure CSP compliance
 */

(function() {
    // Get CSP nonce from meta tag
    const nonceMetaTag = document.querySelector('meta[name="csp-nonce"]');
    const nonce = nonceMetaTag ? nonceMetaTag.getAttribute('content') : null;
    
    if (!nonce) {
        console.warn('CSP nonce not found in meta tag');
        return;
    }
    
    // Override createElement to add nonce to style and script elements
    const originalCreateElement = document.createElement;
    document.createElement = function(tagName) {
        const element = originalCreateElement.apply(document, arguments);
        
        if (tagName.toLowerCase() === 'style' || tagName.toLowerCase() === 'script') {
            element.setAttribute('nonce', nonce);
        }
        
        return element;
    };
    
    // Function to add nonce to all style and script elements
    function addNonceToElements() {
        // Add nonce to style elements
        document.querySelectorAll('style:not([nonce])').forEach(style => {
            style.setAttribute('nonce', nonce);
        });
        
        // Add nonce to script elements
        document.querySelectorAll('script:not([nonce])').forEach(script => {
            script.setAttribute('nonce', nonce);
        });
    }
    
    // Run immediately
    addNonceToElements();
    
    // Set up a MutationObserver to watch for new elements
    const observer = new MutationObserver(mutations => {
        let needsUpdate = false;
        
        mutations.forEach(mutation => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) {
                        // Check if the node is a style or script element
                        if ((node.tagName === 'STYLE' || node.tagName === 'SCRIPT') && !node.hasAttribute('nonce')) {
                            node.setAttribute('nonce', nonce);
                            needsUpdate = true;
                        }
                        
                        // Check for style and script elements within the added node
                        if (node.querySelectorAll) {
                            const elements = node.querySelectorAll('style:not([nonce]), script:not([nonce])');
                            if (elements.length > 0) {
                                elements.forEach(element => element.setAttribute('nonce', nonce));
                                needsUpdate = true;
                            }
                        }
                    }
                });
            }
        });
        
        if (needsUpdate) {
            // Double check for any missed elements
            addNonceToElements();
        }
    });
    
    // Start observing the document with all possible mutation types
    observer.observe(document.documentElement, {
        childList: true,
        subtree: true,
        attributes: true,
        characterData: true
    });
    
    // Run again when DOM is fully loaded
    document.addEventListener('DOMContentLoaded', addNonceToElements);
    
    // Run periodically to catch any missed elements
    setInterval(addNonceToElements, 500);
    
    // Override insertAdjacentHTML to add nonce to any inserted style or script elements
    const originalInsertAdjacentHTML = Element.prototype.insertAdjacentHTML;
    Element.prototype.insertAdjacentHTML = function(position, html) {
        // Call the original method
        originalInsertAdjacentHTML.call(this, position, html);
        
        // Add nonce to any inserted style or script elements
        const styleElements = this.querySelectorAll('style:not([nonce])');
        const scriptElements = this.querySelectorAll('script:not([nonce])');
        
        styleElements.forEach(style => style.setAttribute('nonce', nonce));
        scriptElements.forEach(script => script.setAttribute('nonce', nonce));
    };
    
    // Override innerHTML to add nonce to any inserted style or script elements
    const originalInnerHTMLDescriptor = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML');
    if (originalInnerHTMLDescriptor && originalInnerHTMLDescriptor.set) {
        Object.defineProperty(Element.prototype, 'innerHTML', {
            set: function(html) {
                // Call the original setter
                originalInnerHTMLDescriptor.set.call(this, html);
                
                // Add nonce to any inserted style or script elements
                const styleElements = this.querySelectorAll('style:not([nonce])');
                const scriptElements = this.querySelectorAll('script:not([nonce])');
                
                styleElements.forEach(style => style.setAttribute('nonce', nonce));
                scriptElements.forEach(script => script.setAttribute('nonce', nonce));
                
                return html;
            },
            get: originalInnerHTMLDescriptor.get,
            configurable: true
        });
    }
})();