<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ForceCSP
{
    /**
     * Handle an incoming request.
     * This middleware specifically handles CSP headers to fix production issues
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Comprehensive CSP policy that includes all required domains
        $csp = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://code.jquery.com https://cdn.plyr.io https://buttons.github.io https://www.youtube.com https://youtube.com https://www.youtube-nocookie.com https://maps.googleapis.com",
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.gstatic.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://use.fontawesome.com https://cdn.plyr.io",
            "style-src-elem 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.gstatic.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://use.fontawesome.com https://cdn.plyr.io",
            "font-src 'self' https://fonts.googleapis.com https://fonts.gstatic.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://use.fontawesome.com data:",
            "img-src 'self' data: https:",
            "media-src 'self' data: blob:",
            "connect-src 'self' https://api.ipify.org https://noembed.com https://cdn.plyr.io https://api.github.com https://maps.googleapis.com",
            "frame-src 'self' https://www.youtube.com https://youtube.com https://youtu.be https://www.youtube-nocookie.com",
            "object-src 'none'",
            "base-uri 'self'",
            "form-action 'self'"
        ];

        $cspHeader = implode('; ', $csp);

        // Remove any existing CSP headers that might conflict
        $response->headers->remove('Content-Security-Policy');
        $response->headers->remove('Content-Security-Policy-Report-Only');
        $response->headers->remove('X-Content-Security-Policy');
        $response->headers->remove('X-WebKit-CSP');

        // Set the new CSP header with high priority
        $response->headers->set('Content-Security-Policy', $cspHeader);

        // Log for debugging (only in production)
        if (app()->environment('production')) {
            \Log::info('ForceCSP Middleware Applied', [
                'url' => $request->url(),
                'csp_length' => strlen($cspHeader),
                'timestamp' => now()->toDateTimeString()
            ]);
        }

        return $response;
    }
}
