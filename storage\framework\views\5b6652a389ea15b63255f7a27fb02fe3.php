<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid py-4">
        <div class="row">
            <!-- Left Sidebar - Course Navigation -->
            <div class="col-lg-3">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white py-3">
                        <h5 class="mb-0"><?php echo e($course->name); ?></h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            <div class="accordion" id="courseContentAccordion">
                                <div class="accordion-item border-0">
                                    <h2 class="accordion-header" id="headingLectures">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseLectures" aria-expanded="true" aria-controls="collapseLectures">
                                            <i class="fas fa-graduation-cap me-2"></i> Course Content
                                        </button>
                                    </h2>
                                    <div id="collapseLectures" class="accordion-collapse collapse show" aria-labelledby="headingLectures">
                                        <div class="accordion-body p-0">
                                            <div class="list-group list-group-flush">
                                                <?php $__currentLoopData = $lectures; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lecItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <!-- Lecture Video -->
                                                    <a href="<?php echo e(route('user.lecture.purchased', ['course' => $course->id, 'lecture' => $lecItem->id])); ?>"
                                                        class="list-group-item list-group-item-action d-flex align-items-center ps-4 <?php echo e($lecture->id == $lecItem->id && !$isViewingPdf ? 'active' : ''); ?>">
                                                        <i class="fas fa-play-circle me-2"></i>
                                                        <div class="flex-grow-1">
                                                            <span><?php echo e($lecItem->name); ?></span>
                                                            <small class="d-block text-muted"><?php echo e($lecItem->duration ?? 'N/A'); ?></small>
                                                        </div>
                                                    </a>

                                                    <!-- Lecture PDF (indented) -->
                                                    <?php if($lecItem->pdf_file_path): ?>
                                                    <a href="<?php echo e(route('user.lecture.purchased', ['course' => $course->id, 'lecture' => $lecItem->id])); ?>?view=pdf"
                                                        class="list-group-item list-group-item-action d-flex align-items-center ps-5 <?php echo e($lecture->id == $lecItem->id && $isViewingPdf ? 'active' : ''); ?>">
                                                        <i class="fas fa-file-pdf text-danger me-2"></i>
                                                        <div>
                                                            <span><?php echo e($lecItem->name); ?> PDF</span>
                                                        </div>
                                                    </a>
                                                    <?php endif; ?>

                                                    <!-- Lecture Quizzes (indented) -->
                                                    <?php
                                                        $quizzes = $lecItem->quizzes;
                                                        $userId = auth()->id();
                                                    ?>
                                                    <?php if($quizzes && $quizzes->count() > 0): ?>
                                                        <?php $__currentLoopData = $quizzes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $quiz): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <a href="<?php echo e(route('quiz.show', ['course' => $course->id, 'lecture' => $lecItem->id, 'quiz' => $quiz->id])); ?>"
                                                                class="list-group-item list-group-item-action d-flex align-items-center ps-5">
                                                                <?php if($quiz->isPassed($userId)): ?>
                                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                                <?php else: ?>
                                                                    <i class="fas fa-question-circle text-warning me-2"></i>
                                                                <?php endif; ?>
                                                                <div>
                                                                    <span>Quiz: <?php echo e($quiz->title); ?></span>
                                                                    <?php if($quiz->isPassed($userId)): ?>
                                                                        <small class="d-block text-success">Passed</small>
                                                                    <?php elseif($quiz->hasInProgressAttempt($userId)): ?>
                                                                        <small class="d-block text-primary">In Progress</small>
                                                                    <?php else: ?>
                                                                        <small class="d-block text-muted"><?php echo e($quiz->total_points); ?> points</small>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </a>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php endif; ?>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="col-lg-9">
                <!-- Breadcrumb -->
                <nav aria-label="breadcrumb" class="mb-3">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('user.dashboard')); ?>">My Courses</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('user.course.purchased', $course->id)); ?>"><?php echo e($course->name); ?></a></li>
                        <li class="breadcrumb-item active"><?php echo e($lecture->name); ?></li>
                    </ol>
                </nav>

                <!-- Lecture Player -->
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <h3 class="mb-3"><?php echo e($lecture->name); ?></h3>

                        <?php
                            $viewMode = request()->query('view', 'video');
                            $userProgress = $lecture->getProgressForUser(Auth::id());
                            $progressPercent = $userProgress ? $userProgress->progress_percent : 0;
                            $isCompleted = $userProgress ? $userProgress->completed : false;
                        ?>

                        <!-- Lecture Progress Bar -->
                        <div class="lecture-progress mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span class="small text-muted">Your progress</span>
                                <span class="small fw-bold lecture-progress-display"><?php echo e(number_format($progressPercent, 0)); ?>%</span>
                            </div>
                            <div class="progress lecture-progress-height">
                                <div class="progress-bar lecture-progress-bar <?php echo e($isCompleted ? 'bg-success' : 'bg-primary'); ?>"
                                     role="progressbar"
                                     data-width="<?php echo e($progressPercent); ?>"
                                     aria-valuenow="<?php echo e($progressPercent); ?>"
                                     aria-valuemin="0"
                                     aria-valuemax="100"></div>
                            </div>
                        </div>

                        <?php if($viewMode == 'pdf' && $lecture->pdf_file_path): ?>
                            <!-- PDF Viewer -->
                            <div class="pdf-viewer-height mb-4">
                                <object data="<?php echo e(asset('storage/' . $lecture->pdf_file_path)); ?>" type="application/pdf" width="100%" height="100%">
                                    <div class="p-4 bg-light rounded text-center">
                                        <p class="mb-2">
                                            <i class="fas fa-exclamation-circle text-warning fa-2x mb-3"></i><br>
                                            Your browser doesn't support embedded PDF viewing.
                                        </p>
                                        <a href="<?php echo e(asset('storage/' . $lecture->pdf_file_path)); ?>" class="btn btn-primary mb-2" target="_blank">
                                            <i class="fas fa-external-link-alt me-1"></i> Open PDF in New Tab
                                        </a>
                                        <a href="<?php echo e(asset('storage/' . $lecture->pdf_file_path)); ?>" class="btn btn-outline-primary" download>
                                            <i class="fas fa-download me-1"></i> Download PDF
                                        </a>
                                    </div>
                                </object>
                            </div>

                            <!-- PDF Actions -->
                            <div class="d-flex justify-content-between mb-4">
                                <a href="<?php echo e(route('user.lecture.purchased', ['course' => $course->id, 'lecture' => $lecture->id])); ?>" class="btn btn-outline-primary">
                                    <i class="fas fa-video me-1"></i> Switch to Video
                                </a>
                            </div>
                        <?php else: ?>
                            <!-- Video Player -->
                            <div class="ratio ratio-16x9 mb-4 video-container">
                                <?php if($lecture->youtube_url): ?>
                                    <?php
                                        $youtubeId = null;
                                        if (preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $lecture->youtube_url, $matches)) {
                                            $youtubeId = $matches[1];
                                        }
                                    ?>
                                    <?php if($youtubeId): ?>
                                        <div class="video-player" data-plyr-provider="youtube" data-plyr-embed-id="<?php echo e($youtubeId); ?>"></div>
                                    <?php else: ?>
                                        <div class="d-flex align-items-center justify-content-center bg-dark text-white">
                                            <div class="text-center">
                                                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                                                <h5>Invalid YouTube URL</h5>
                                                <p>The video URL format is not supported.</p>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <div class="d-flex align-items-center justify-content-center bg-dark text-white">
                                        <div class="text-center">
                                            <i class="fas fa-video fa-3x mb-3"></i>
                                            <h5>No Video Available</h5>
                                            <p>This lecture doesn't have a video yet.</p>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Video Actions -->
                            <div class="d-flex justify-content-between mb-4">
                                <?php if($lecture->pdf_file_path): ?>
                                    <a href="<?php echo e(route('user.lecture.purchased', ['course' => $course->id, 'lecture' => $lecture->id])); ?>?view=pdf" class="btn btn-outline-primary">
                                        <i class="fas fa-file-pdf me-1"></i> Switch to PDF
                                    </a>
                                <?php else: ?>
                                    <div></div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <!-- Lecture Description -->
                        <?php if($lecture->description): ?>
                            <div class="lecture-description mb-4">
                                <h5>Description</h5>
                                <div class="bg-light p-3 rounded">
                                    <?php echo nl2br(e($lecture->description)); ?>

                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Lecture Attachments -->
                        <?php if($lecture->attachments && $lecture->attachments->count() > 0): ?>
                            <div class="lecture-attachments mb-4">
                                <h5>Attachments</h5>
                                <div class="row">
                                    <?php $__currentLoopData = $lecture->attachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-md-6 col-lg-4 mb-3">
                                            <div class="card attachment-card">
                                                <div class="card-body text-center">
                                                    <?php
                                                        $extension = pathinfo($attachment->file_path, PATHINFO_EXTENSION);
                                                        $isImage = in_array(strtolower($extension), ['jpg', 'jpeg', 'png', 'gif', 'webp']);
                                                    ?>
                                                    
                                                    <?php if($isImage): ?>
                                                        <img src="<?php echo e(asset('storage/' . $attachment->file_path)); ?>" 
                                                             alt="<?php echo e($attachment->original_name); ?>" 
                                                             class="attachment-img-height mb-2 rounded">
                                                    <?php else: ?>
                                                        <i class="fas fa-file fa-3x text-primary mb-2"></i>
                                                    <?php endif; ?>
                                                    
                                                    <h6 class="card-title"><?php echo e($attachment->original_name); ?></h6>
                                                    <p class="card-text small text-muted"><?php echo e(strtoupper($extension)); ?> File</p>
                                                    <a href="<?php echo e(asset('storage/' . $attachment->file_path)); ?>" 
                                                       class="btn btn-sm btn-primary" 
                                                       download="<?php echo e($attachment->original_name); ?>">
                                                        <i class="fas fa-download me-1"></i> Download
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Lecture Rating Section -->
                        <div class="lecture-rating-section mt-5">
                            <h5>Rate This Lecture</h5>
                            
                            <!-- Average Rating Display -->
                            <div class="lecture-rating-stats mb-4 p-3 bg-light rounded">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center">
                                            <span class="h4 mb-0 me-3" id="average-rating"><?php echo e(number_format($lecture->averageRating(), 1)); ?></span>
                                            <div id="average-star-display">
                                                <?php for($i = 1; $i <= 5; $i++): ?>
                                                    <?php if($i <= $lecture->averageRating()): ?>
                                                        <i class="fas fa-star text-warning"></i>
                                                    <?php elseif($i - 0.5 <= $lecture->averageRating()): ?>
                                                        <i class="fas fa-star-half-alt text-warning"></i>
                                                    <?php else: ?>
                                                        <i class="far fa-star text-warning"></i>
                                                    <?php endif; ?>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                        <small class="text-muted">(<?php echo e($lecture->ratings()->count()); ?> ratings)</small>
                                    </div>
                                </div>
                            </div>

                            <!-- User Rating Form -->
                            <div class="user-rating-form mb-4">
                                <div class="mb-3">
                                    <label class="form-label">Your Rating:</label>
                                    <div class="rating-stars mb-2">
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <i class="far fa-star rating-star" data-value="<?php echo e($i); ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                    <input type="hidden" id="rating-value" value="0">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="rating-comment" class="form-label">Comment (Optional):</label>
                                    <textarea class="form-control" id="rating-comment" rows="3" placeholder="Share your thoughts about this lecture..."></textarea>
                                </div>
                                
                                <button type="button" class="btn btn-primary" id="submit-rating">
                                    <i class="fas fa-star me-2"></i> Submit Rating
                                </button>
                            </div>

                            <!-- Ratings List -->
                            <div id="ratings-container">
                                <!-- Ratings will be loaded here via JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('styles'); ?>
        <link rel="stylesheet" href="https://cdn.plyr.io/3.6.8/plyr.css" />
        <link href="<?php echo e(asset('css/purchased-lecture-detail.css')); ?>" rel="stylesheet">
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('scripts'); ?>
        <script src="https://cdn.plyr.io/3.6.8/plyr.js"></script>
        <script src="<?php echo e(asset('js/purchased-lecture-detail.js')); ?>"></script>
        <script nonce="<?php echo e(csp_nonce()); ?>">
            // Initialize recording warning flag
            let recordingWarningShown = false;
            let plyrPlayer = null;

            // Initialize Plyr player for YouTube videos
            function initializeVideoPlayer() {
                const videoPlayer = document.querySelector('.video-player');
                if (videoPlayer) {
                    const embedId = videoPlayer.getAttribute('data-plyr-embed-id');
                    
                    if (embedId) {
                        plyrPlayer = new Plyr(videoPlayer, {
                            controls: [
                                'play-large', 'play', 'progress', 'current-time', 'duration',
                                'mute', 'volume', 'settings', 'fullscreen'
                            ],
                            settings: ['quality', 'speed'],
                            youtube: {
                                noCookie: true,
                                rel: 0,
                                showinfo: 0,
                                iv_load_policy: 3,
                                modestbranding: 1,
                                origin: window.location.origin
                            },
                            ratio: '16:9',
                            keyboard: { focused: false, global: false }
                        });

                        plyrPlayer.on('ready', () => {
                            console.log('🎬 Plyr player ready');
                            addVideoSecurityMeasures();
                        });

                        plyrPlayer.on('ended', () => {
                            videoEndedRecently = true;
                            setTimeout(() => {
                                videoEndedRecently = false;
                            }, 30000);
                        });
                    }
                }
            }

            function addVideoSecurityMeasures() {
                const videoContainer = document.querySelector('.ratio-16x9');
                if (videoContainer) {
                    videoContainer.addEventListener('contextmenu', (e) => {
                        e.preventDefault();
                        return false;
                    });

                    videoContainer.addEventListener('selectstart', (e) => {
                        e.preventDefault();
                        return false;
                    });

                    videoContainer.addEventListener('dragstart', (e) => {
                        e.preventDefault();
                        return false;
                    });
                }
            }

            async function detectScreenRecording() {
                try {
                    if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
                        const originalGetDisplayMedia = navigator.mediaDevices.getDisplayMedia;

                        navigator.mediaDevices.getDisplayMedia = function(...args) {
                            console.log('🚫 Screen recording attempt detected!');
                            showRecordingWarning();
                            return Promise.reject(new Error('Screen recording is not allowed on this page'));
                        };
                    }
                } catch (error) {
                    console.log('Screen recording detection setup failed:', error);
                }
            }

            let suspiciousActivityScore = 0;
            let lastActivityTime = Date.now();
            let videoEndedRecently = false;

            function monitorSuspiciousActivity() {
                setInterval(() => {
                    const now = Date.now();
                    const timeSinceLastActivity = now - lastActivityTime;

                    if (videoEndedRecently) {
                        console.log('🎬 Video ended recently, skipping suspicious activity detection');
                        return;
                    }

                    if (!document.hidden && timeSinceLastActivity > 60000) {
                        suspiciousActivityScore += 1;
                    }

                    if (timeSinceLastActivity < 10000) {
                        suspiciousActivityScore = Math.max(0, suspiciousActivityScore - 1);
                    }

                    if (suspiciousActivityScore > 8 && !recordingWarningShown && !videoEndedRecently) {
                        console.log('🚨 High suspicious activity score detected:', suspiciousActivityScore);
                        showRecordingWarning();
                        recordingWarningShown = true;
                    }

                    lastActivityTime = now;
                }, 15000);
            }

            ['mousemove', 'keydown', 'click'].forEach(event => {
                document.addEventListener(event, () => {
                    lastActivityTime = Date.now();
                });
            });

            function detectRecordingProcesses() {
                setInterval(() => {
                    if (performance.memory) {
                        const memoryUsage = performance.memory.usedJSHeapSize / performance.memory.totalJSHeapSize;

                        if (memoryUsage > 0.9 && !recordingWarningShown) {
                            suspiciousActivityScore += 2;
                        }
                    }
                }, 15000);
            }

            // Initialize all protection methods
            document.addEventListener('DOMContentLoaded', function() {
                initializeVideoPlayer();
                detectScreenRecording();
                monitorSuspiciousActivity();
                detectRecordingProcesses();

                console.log('🛡️ Advanced screen recording protection activated!');
                console.log('ℹ️ Alt+Tab and normal window switching will not trigger warnings');

                // Initialize progress bars with data attributes
                const progressBars = document.querySelectorAll('[data-width]');
                progressBars.forEach(bar => {
                    const width = bar.getAttribute('data-width');
                    bar.style.width = width + '%';
                });

                // Rating functionality
                const ratingStars = document.querySelectorAll('.rating-star');
                const ratingValue = document.getElementById('rating-value');
                const submitButton = document.getElementById('submit-rating');

                if (ratingStars.length > 0) {
                    loadRatings();

                    ratingStars.forEach(star => {
                        star.addEventListener('mouseover', function() {
                            const value = parseInt(this.dataset.value);
                            highlightStars(value);
                        });

                        star.addEventListener('mouseleave', function() {
                            const selectedValue = parseInt(ratingValue.value);
                            highlightStars(selectedValue);
                        });

                        star.addEventListener('click', function() {
                            const value = parseInt(this.dataset.value);
                            ratingValue.value = value;
                            highlightStars(value);
                        });
                    });
                }

                function highlightStars(count) {
                    ratingStars.forEach(star => {
                        const starValue = parseInt(star.dataset.value);
                        if (starValue <= count) {
                            star.classList.remove('far');
                            star.classList.add('fas', 'text-warning');
                        } else {
                            star.classList.remove('fas', 'text-warning');
                            star.classList.add('far');
                        }
                    });
                }

                if (submitButton) {
                    submitButton.addEventListener('click', function() {
                        const rating = parseInt(ratingValue.value);
                        const comment = document.getElementById('rating-comment').value.trim();

                        if (rating === 0) {
                            alert('Please select a rating before submitting.');
                            return;
                        }

                        submitButton.disabled = true;
                        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Submitting...';

                        const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                        fetch('<?php echo e(route('lectures.rate', ['lecture' => $lecture->id])); ?>', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': token,
                                'Accept': 'application/json'
                            },
                            body: JSON.stringify({
                                rating: rating,
                                comment: comment
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                alert(data.message);
                                document.getElementById('average-rating').textContent = parseFloat(data.average_rating).toFixed(1);
                                document.querySelector('.lecture-rating-stats .text-muted').textContent = `(${data.rating_count} ratings)`;
                                updateAverageStarDisplay(data.average_rating);
                                loadRatings();
                            } else {
                                alert('Error: ' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('An error occurred while submitting your rating.');
                        })
                        .finally(() => {
                            submitButton.disabled = false;
                            submitButton.innerHTML = '<i class="fas fa-star me-2"></i> Submit Rating';
                        });
                    });
                }

                function updateAverageStarDisplay(rating) {
                    const stars = document.querySelectorAll('#average-star-display i');
                    stars.forEach((star, index) => {
                        star.className = '';
                        if (index + 1 <= rating) {
                            star.className = 'fas fa-star text-warning';
                        } else if (index + 0.5 <= rating) {
                            star.className = 'fas fa-star-half-alt text-warning';
                        } else {
                            star.className = 'far fa-star text-warning';
                        }
                    });
                }

                function loadRatings() {
                    const ratingsContainer = document.getElementById('ratings-container');
                    
                    ratingsContainer.innerHTML = `
                        <div class="text-center py-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 text-muted">Loading ratings...</p>
                        </div>
                    `;

                    fetch('<?php echo e(route('lectures.ratings', ['lecture' => $lecture->id])); ?>')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                if (data.ratings.length > 0) {
                                    let html = '<div class="ratings-list mt-4">';
                                    data.ratings.forEach(rating => {
                                        const date = new Date(rating.created_at);
                                        const formattedDate = date.toLocaleDateString([], { year: 'numeric', month: 'short', day: 'numeric' });

                                        html += `
                                            <div class="rating-item mb-3 p-3 border-bottom">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <div>
                                                        <strong>${rating.user.name}</strong>
                                                        <div class="d-inline-block ms-2">`;

                                        for (let i = 1; i <= 5; i++) {
                                            if (i <= rating.rating) {
                                                html += '<i class="fas fa-star text-warning small"></i>';
                                            } else {
                                                html += '<i class="far fa-star text-warning small"></i>';
                                            }
                                        }

                                        html += `
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <small class="text-muted">${formattedDate}</small>
                                                    </div>
                                                </div>`;

                                        if (rating.comment) {
                                            html += `<p class="mb-0 text-muted">${rating.comment}</p>`;
                                        }

                                        html += `</div>`;
                                    });
                                    html += '</div>';
                                    ratingsContainer.innerHTML = html;
                                } else {
                                    ratingsContainer.innerHTML = `
                                        <div class="text-center py-3">
                                            <p class="text-muted">No ratings yet. Be the first to rate this lecture!</p>
                                        </div>
                                    `;
                                }
                            } else {
                                ratingsContainer.innerHTML = `
                                    <div class="alert alert-warning">
                                        Failed to load ratings.
                                    </div>
                                `;
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            ratingsContainer.innerHTML = `
                                <div class="alert alert-danger">
                                    An error occurred while loading ratings.
                                </div>
                            `;
                        });
                }
            });

            console.log('🔒 Comprehensive security system with screen recording protection loaded and active!');
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
                            data.questions.sort((a, b) => {
                                const dateA = new Date(a.created_at.replace(/(\d+)(st|nd|rd|th)/, "$1"));
                                const dateB = new Date(b.created_at.replace(/(\d+)(st|nd|rd|th)/, "$1"));
                                return dateB - dateA; // Newest first
                            });

                            // Generate HTML for questions and answers
                            let html = '';

                            data.questions.forEach(question => {
                                // Create conversation container for this question and its answers
                                html += '<div class="conversation-group mb-4">';

                                // Add status indicator
                                html += `<div class="status-indicator status-${question.status.toLowerCase()}">${formatStatus(question.status)}</div>`;

                                // Add date header
                                const formattedDate = formatDate(question.created_at);
                                html += `<div class="conversation-date">${formattedDate}</div>`;

                                // Prepare attachment previews
                                let attachmentsHtml = '';
                                if (question.attachments && question.attachments.length > 0) {
                                    attachmentsHtml += '<div class="chat-attachments">';

                                    question.attachments.forEach(attachment => {
                                        if (attachment.type === 'image') {
                                            attachmentsHtml += `
                                                <div class="mb-2">
                                                    <a href="${attachment.url}" target="_blank">
                                                        <img src="${attachment.url}" class="img-thumbnail" style="max-height: 150px;">
                                                    </a>
                                                </div>`;
                                        } else if (attachment.type === 'pdf') {
                                            attachmentsHtml += `
                                                <div class="mb-2">
                                                    <a href="${attachment.url}" target="_blank" class="btn btn-sm btn-outline-danger">
                                                        <i class="fas fa-file-pdf me-1"></i> ${attachment.name}
                                                    </a>
                                                </div>`;
                                        } else if (attachment.type === 'voice') {
                                            attachmentsHtml += `
                                                <div class="mb-2">
                                                    <audio controls>
                                                        <source src="${attachment.url}" type="${attachment.mime_type || 'audio/webm'}">
                                                        Your browser does not support the audio element.
                                                    </audio>
                                                </div>`;
                                        }
                                    });

                                    attachmentsHtml += '</div>';
                                }

                                // Create question chat bubble
                                html += `
                                <div class="chat-message question">
                                    <div class="chat-meta">
                                        <span class="user-name">${question.user}</span>
                                        <span class="badge ms-2 bg-${getStatusBadgeColor(question.status)}">${formatStatus(question.status)}</span>
                                    </div>
                                    <div class="chat-bubble">
                                        <p class="mb-0">${question.content}</p>
                                        ${attachmentsHtml}
                                        <div class="chat-time">${formatTime(question.created_at)}</div>
                                    </div>
                                </div>`;

                                // Add answers if any
                                if (question.answers && question.answers.length > 0) {
                                    html += renderAnswers(question.answers);
                                }

                                // Close the conversation group
                                html += '</div>';
                            });

                            container.innerHTML = html;
                        } else {
                            // Show the "no questions" message
                            noQuestionsMsg.style.display = 'block';
                        }
                    } else {
                        console.error('Error loading questions:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            }

            // Helper function to render answers
            function renderAnswers(answers) {
                let html = '';

                // Sort answers to show pinned answers first
                answers.sort((a, b) => {
                    if (a.is_pinned && !b.is_pinned) return -1;
                    if (!a.is_pinned && b.is_pinned) return 1;
                    return 0;
                });

                answers.forEach(answer => {
                    // Prepare attachments for answers if any
                    let attachmentsHtml = '';
                    if (answer.attachments && answer.attachments.length > 0) {
                        attachmentsHtml += '<div class="chat-attachments">';

                        answer.attachments.forEach(attachment => {
                            if (attachment.type === 'image') {
                                attachmentsHtml += `
                                    <div class="mb-2">
                                        <a href="${attachment.url}" target="_blank">
                                            <img src="${attachment.url}" class="img-thumbnail" style="max-height: 150px;">
                                        </a>
                                    </div>`;
                            } else if (attachment.type === 'pdf') {
                                attachmentsHtml += `
                                    <div class="mb-2">
                                        <a href="${attachment.url}" target="_blank" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-file-pdf me-1"></i> ${attachment.name}
                                        </a>
                                    </div>`;
                            } else if (attachment.type === 'voice' || attachment.type === 'audio') {
                                attachmentsHtml += `
                                    <div class="mb-2">
                                        <audio controls>
                                            <source src="${attachment.url}" type="${attachment.mime_type || 'audio/webm'}">
                                            Your browser does not support the audio element.
                                        </audio>
                                    </div>`;
                            }
                        });

                        attachmentsHtml += '</div>';
                    }

                    // Create answer chat bubble
                    html += `
                    <div class="chat-message answer ${answer.is_pinned ? 'pinned' : ''}">
                        <div class="chat-meta">
                            ${answer.is_pinned ? '<span class="badge bg-success me-2"><i class="fas fa-thumbtack me-1"></i> Pinned</span>' : ''}
                            <span class="user-name">${answer.user}</span>
                        </div>
                        <div class="chat-bubble">
                            <p class="mb-0">${answer.content}</p>
                            ${attachmentsHtml}
                            <div class="chat-time">${formatTime(answer.created_at)}</div>
                        </div>
                    </div>`;
                });

                return html;
            }

            // Helper function to get badge color based on status
            function getStatusBadgeColor(status) {
                switch(status) {
                    case 'pending': return 'warning';
                    case 'answered': return 'success';
                    case 'rejected': return 'danger';
                    default: return 'secondary';
                }
            }

            // Helper function to format status text
            function formatStatus(status) {
                return status.charAt(0).toUpperCase() + status.slice(1);
            }

            // Helper function to format time in WhatsApp style
            function formatTime(dateStr) {
                // If the date string is already short (like "2 hours ago"), just return it
                if (dateStr && dateStr.length < 20) {
                    return dateStr;
                }

                try {
                    // Try to parse the date - first remove any ordinal suffixes that might cause issues
                    const cleanStr = dateStr.replace(/(\d+)(st|nd|rd|th)/, "$1");

                    // Create a date object - this works with ISO strings and many other formats
                    const date = new Date(cleanStr);

                    // Check if the date is valid
                    if (isNaN(date.getTime())) {
                        // If we can't parse it, return the original string
                        return dateStr;
                    }

                    // Format time as HH:MM (using locale settings)
                    return date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                } catch (e) {
                    console.log('Date parsing error:', e);
                    // In case of any error, return the original string
                    return dateStr;
                }
            }

            // Helper function to format date in WhatsApp style
            function formatDate(dateStr) {
                // If the date string is already short (like "2 hours ago"), just return it
                if (dateStr && dateStr.length < 20) {
                    return dateStr;
                }

                try {
                    // Try to parse the date - first remove any ordinal suffixes that might cause issues
                    const cleanStr = dateStr.replace(/(\d+)(st|nd|rd|th)/, "$1");

                    // Create a date object - this works with ISO strings and many other formats
                    const date = new Date(cleanStr);

                    // Check if the date is valid
                    if (isNaN(date.getTime())) {
                        // If we can't parse it, return the original string
                        return dateStr;
                    }

                    // Format date as MMM d, yyyy
                    return date.toLocaleDateString([], {month: 'short', day: '2-digit', year: 'numeric'});
                } catch (e) {
                    console.log('Date parsing error:', e);
                    // In case of any error, return the original string
                    return dateStr;
                }
            }

            // Create user watermark
            function createWatermark() {
                const userInfo = {
                    id: '<?php echo e(auth()->id()); ?>',
                    // You can replace this with actual phone number if available in your user model
                    phone: '<?php echo e(auth()->user()->phone ?? "Protected User"); ?>',
                    email: '<?php echo e(auth()->user()->email); ?>'
                };

                // Remove global watermark that shows at the bottom of the page
                // Only keep video-specific watermarks

                // Add specific bouncing watermarks for video area only
                addBounceWatermarksToVideo(userInfo);
            }

            // Add bouncing watermarks to the video container
            function addBounceWatermarksToVideo(userInfo) {
                // Find video container
                const videoContainer = document.querySelector('.video-container') ||
                                      document.querySelector('.ratio-16x9');

                if (!videoContainer) return;

                // Make sure the video container has position relative for absolute positioning
                videoContainer.style.position = 'relative';
                videoContainer.style.overflow = 'hidden';

                // Create email watermark (bouncing like a ball)
                const emailWatermark = document.createElement('div');
                emailWatermark.className = 'video-watermark';
                emailWatermark.innerText = `Email: ${userInfo.email}`;
                videoContainer.appendChild(emailWatermark);

                // Create phone watermark (different bouncing pattern)
                const phoneWatermark = document.createElement('div');
                phoneWatermark.className = 'video-watermark second';
                phoneWatermark.innerText = `ID: ${userInfo.id} | Phone: ${userInfo.phone}`;
                videoContainer.appendChild(phoneWatermark);
            }

            // Initialize watermark
            createWatermark();
        });

        // EXTREME Windows Game Bar blocking
        (function() {
            // Add multiple meta tags to block Game Bar at all costs
            const metaTags = [
                { name: 'xbox-game-bar-allowed', content: 'false' },
                { name: 'gamepadAPI', content: 'false' },
                { name: 'gamepad', content: 'false' }
            ];

            metaTags.forEach(meta => {
                const metaTag = document.createElement('meta');
                metaTag.name = meta.name;
                metaTag.content = meta.content;
                document.head.appendChild(metaTag);
            });

            // Completely block the Windows Game Bar JavaScript API
            // This is the most aggressive approach
            if (window.Windows) {
                try {
                    // Attempt to completely disable the Game Bar API
                    Object.defineProperty(window, 'Windows', {
                        get: function() {
                            console.log('Blocked access to Windows namespace');
                            return undefined;
                        },
                        configurable: false
                    });
                } catch (e) {
                    console.log('Could not override Windows namespace');
                }
            }

            // Moderate keyboard monitoring - non-blocking to video playback
            // This runs every 100ms to check for Windows key combinations
            const gameBarMonitor = setInterval(function() {
                // Check if any Xbox Game Bar related elements exist
                const gameBarElements = document.querySelectorAll(
                    '[class*="xbox"], [class*="game-bar"], [id*="xbox"], [id*="game-bar"], ' +
                    '[class*="gamebar"], [id*="gamebar"], [class*="xboxlive"], [id*="xboxlive"]'
                );

                if (gameBarElements.length > 0) {
                    console.log('Game Bar elements detected!');
                    // Show warning but don't remove elements
                    showGameBarWarning();
                }

                // Non-blocking recording notification
                if (keyStates.windowsPressed && keyStates.alt) {
                    console.log('Windows+Alt combination detected');

                    // Reset Windows key state
                    keyStates.windowsPressed = false;

                    // Show a non-blocking warning notification
                    showGameBarWarning('Windows+Alt combination detected. Recording is prohibited.', true);
                }
            }, 100);

            // Handle focus changes to detect switching to Game Bar
            let lastActiveTime = Date.now();

            document.addEventListener('visibilitychange', function() {
                const timeDiff = Date.now() - lastActiveTime;

                // If page was hidden for a short time (Game Bar appears quickly)
                if (document.hidden === false && timeDiff < 1000 && timeDiff > 100) {
                    console.log('Possible Game Bar activation detected - visibility change');
                    showGameBarWarning();
                }

                lastActiveTime = Date.now();
            });

            // Additional blur/focus detection for Game Bar
            window.addEventListener('blur', function() {
                lastActiveTime = Date.now();
            });

            window.addEventListener('focus', function() {
                const timeDiff = Date.now() - lastActiveTime;

                // If window regained focus quickly (Game Bar appears quickly)
                if (timeDiff < 1000 && timeDiff > 100) {
                    console.log('Possible Game Bar activation detected - focus change');
                    showGameBarWarning();
                }
            });

            // Non-blocking warning message
            function showGameBarWarning(customMessage, isImportant) {
                // Create a temporary overlay warning that won't interfere with video playback
                const warning = document.createElement('div');
                warning.style.cssText = `
                    position: fixed;
                    top: ${isImportant ? '50%' : '20px'};
                    left: ${isImportant ? '50%' : '20px'};
                    transform: ${isImportant ? 'translate(-50%, -50%)' : 'none'};
                    background-color: ${isImportant ? 'rgba(255, 0, 0, 0.9)' : 'rgba(255, 0, 0, 0.8)'};
                    color: white;
                    padding: ${isImportant ? '20px 30px' : '10px 15px'};
                    border-radius: 5px;
                    z-index: 9999;
                    font-weight: bold;
                    text-align: center;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.4);
                    pointer-events: ${isImportant ? 'auto' : 'none'};
                    max-width: ${isImportant ? '400px' : '300px'};
                `;

                if (isImportant) {
                    warning.innerHTML = `
                        <div>${customMessage || 'Recording Attempt Detected'}</div>
                        <div style="font-size: 0.9em; margin-top: 8px;">User ID: <?php echo e(auth()->id()); ?> | <?php echo e(auth()->user()->email); ?></div>
                        <div style="font-size: 0.9em; margin-top: 4px;">This violation has been logged.</div>
                        ${isImportant ? '<button style="margin-top: 15px; padding: 5px 15px; border: none; background: white; color: red; border-radius: 3px; cursor: pointer;">Dismiss</button>' : ''}
                    `;
                } else {
                    warning.textContent = customMessage || 'Possible Game Bar activity detected';
                }

                document.body.appendChild(warning);

                if (isImportant) {
                    warning.querySelector('button').addEventListener('click', function() {
                        if (document.body.contains(warning)) {
                            document.body.removeChild(warning);
                        }
                    });

                    // Auto-remove after 8 seconds for important warnings
                    setTimeout(() => {
                        if (document.body.contains(warning)) {
                            document.body.removeChild(warning);
                        }
                    }, 8000);
                } else {
                    // Auto-remove after 3 seconds for minor warnings
                    setTimeout(() => {
                        if (document.body.contains(warning)) {
                            document.body.removeChild(warning);
                        }
                    }, 3000);
                }
            }
        })();

        // Track key states to detect combinations with Windows key
    </script>
    <?php $__env->stopPush(); ?>
    <script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize rating stars
    const ratingStars = document.querySelectorAll('.rating-star');
    const ratingValue = document.getElementById('rating-value');
    const submitButton = document.getElementById('submit-rating');

    // Load existing ratings
    loadRatings();

    // Handle star rating selection
    ratingStars.forEach(star => {
        star.addEventListener('mouseover', function() {
            const value = parseInt(this.dataset.value);
            highlightStars(value);
        });

        star.addEventListener('mouseleave', function() {
            const selectedValue = parseInt(ratingValue.value);
            highlightStars(selectedValue);
        });

        star.addEventListener('click', function() {
            const value = parseInt(this.dataset.value);
            ratingValue.value = value;
            highlightStars(value);
        });
    });

    // Function to highlight stars
    function highlightStars(count) {
        ratingStars.forEach(star => {
            const starValue = parseInt(star.dataset.value);
            if (starValue <= count) {
                star.classList.remove('far');
                star.classList.add('fas', 'text-warning');
            } else {
                star.classList.remove('fas', 'text-warning');
                star.classList.add('far');
            }
        });
    }

    // Submit rating
    if (submitButton) {
        submitButton.addEventListener('click', function() {
            const rating = parseInt(ratingValue.value);
            const comment = document.getElementById('rating-comment').value.trim();

            if (rating === 0) {
                alert('Please select a rating before submitting.');
                return;
            }

            // Disable button during submission
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Submitting...';

            // Get CSRF token
            const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            // Send rating to server
            fetch('<?php echo e(route('lectures.rate', ['lecture' => $lecture->id])); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': token,
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    rating: rating,
                    comment: comment
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);

                    // Update average rating display
                    const avgRatingEl = document.getElementById('average-rating');
                    avgRatingEl.textContent = parseFloat(data.average_rating).toFixed(1);

                    // Update rating count
                    const ratingCountEl = document.querySelector('.lecture-rating-stats .text-muted');
                    ratingCountEl.textContent = `(${data.rating_count} ratings)`;

                    // Update average stars display
                    updateAverageStarDisplay(data.average_rating);

                    // Reload the list of ratings
                    loadRatings();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while submitting your rating.');
            })
            .finally(() => {
                // Re-enable button
                submitButton.disabled = false;
                submitButton.innerHTML = '<i class="fas fa-star me-2"></i> Submit Rating';
            });
        });
    }

    // Update average star display
    function updateAverageStarDisplay(rating) {
        const stars = document.querySelectorAll('#average-star-display i');
        stars.forEach((star, index) => {
            star.className = ''; // reset
            if (index + 1 <= rating) {
                star.className = 'fas fa-star text-warning';
            } else if (index + 0.5 <= rating) {
                star.className = 'fas fa-star-half-alt text-warning';
            } else {
                star.className = 'far fa-star text-warning';
            }
        });
    }

    function loadRatings() {
        const ratingsContainer = document.getElementById('ratings-container');
        
        ratingsContainer.innerHTML = `
            <div class="text-center py-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Loading ratings...</p>
            </div>
        `;

        fetch('<?php echo e(route('lectures.ratings', ['lecture' => $lecture->id])); ?>')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.ratings.length > 0) {
                        let html = '<div class="ratings-list mt-4">';
                        data.ratings.forEach(rating => {
                            const date = new Date(rating.created_at);
                            const formattedDate = date.toLocaleDateString([], { year: 'numeric', month: 'short', day: 'numeric' });

                            html += `
                                <div class="rating-item mb-3 p-3 border-bottom">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <strong>${rating.user.name}</strong>
                                            <div class="d-inline-block ms-2">`;

                        for (let i = 1; i <= 5; i++) {
                            if (i <= rating.rating) {
                                html += '<i class="fas fa-star text-warning small"></i>';
                            } else {
                                html += '<i class="far fa-star text-warning small"></i>';
                            }
                        }

                        html += `
                                            </div>
                                        </div>
                                        <div>
                                            <small class="text-muted">${formattedDate}</small>
                                        </div>
                                    </div>`;

                        if (rating.comment) {
                            html += `<p class="mb-0 text-muted">${rating.comment}</p>`;
                        }

                        html += `</div>`;
                    });
                    html += '</div>';
                    ratingsContainer.innerHTML = html;
                } else {
                    ratingsContainer.innerHTML = `
                        <div class="text-center py-3">
                            <p class="text-muted">No ratings yet. Be the first to rate this lecture!</p>
                        </div>
                    `;
                }
            } else {
                ratingsContainer.innerHTML = `
                    <div class="alert alert-warning">
                        Failed to load ratings.
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            ratingsContainer.innerHTML = `
                <div class="alert alert-danger">
                    An error occurred while loading ratings.
                </div>
            `;
        });
    }
});
    </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal)): ?>
<?php $attributes = $__attributesOriginal; ?>
<?php unset($__attributesOriginal); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal)): ?>
<?php $component = $__componentOriginal; ?>
<?php unset($__componentOriginal); ?>
<?php endif; ?>
                star.className = 'fas fa-star-half-alt text-warning';
            } else {
                star.className = 'far fa-star text-warning';
            }
        });
    }

    // Load ratings list
    function loadRatings() {
        const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const ratingsContainer = document.getElementById('ratings-container');

        // Loading state
        ratingsContainer.innerHTML = `
            <div class="text-center py-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Loading ratings...</p>
            </div>
        `;

        fetch('<?php echo e(route('lectures.ratings', ['lecture' => $lecture->id])); ?>')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.ratings.length > 0) {
                        let html = '<div class="ratings-list mt-4">';
                        data.ratings.forEach(rating => {
                            const date = new Date(rating.created_at);
                            const formattedDate = date.toLocaleDateString([], { year: 'numeric', month: 'short', day: 'numeric' });

                            html += `
                                <div class="rating-item mb-3 p-3 border-bottom">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <strong>${rating.user.name}</strong>
                                            <div class="d-inline-block ms-2">`;

                            for (let i = 1; i <= 5; i++) {
                                if (i <= rating.rating) {
                                    html += '<i class="fas fa-star text-warning small"></i>';
                                } else {
                                    html += '<i class="far fa-star text-warning small"></i>';
                                }
                            }

                            html += `
                                            </div>
                                        </div>
                                        <div>
                                            <small class="text-muted">${formattedDate}</small>
                                        </div>
                                    </div>`;

                            if (rating.comment) {
                                html += `<p class="mb-0 text-muted">${rating.comment}</p>`;
                            }

                            html += `</div>`;
                        });
                        html += '</div>';
                        ratingsContainer.innerHTML = html;
                    } else {
                        ratingsContainer.innerHTML = `
                            <div class="text-center py-3">
                                <p class="text-muted">No ratings yet. Be the first to rate this lecture!</p>
                            </div>
                        `;
                    }
                } else {
                    ratingsContainer.innerHTML = `
                        <div class="alert alert-warning">
                            Failed to load ratings.
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                ratingsContainer.innerHTML = `
                    <div class="alert alert-danger">
                        An error occurred while loading ratings.
                    </div>
                `;
            });
    }
});
</script>

<script>


// Enhanced Lecture Progress Tracking Script
document.addEventListener('DOMContentLoaded', function() {
    // Constants and variables
    const lectureId = <?php echo e($lecture->id); ?>;
    const courseId = <?php echo e($course->id); ?>;
    let players = [];
    let currentPlayer = null;
    let progressInterval = null;
    let lastProgressUpdate = 0;
    let isProgressUpdating = false;
    let userCompletedLecture = false;
    let playerReadyInterval = null;
    let playerDetectionAttempts = 0;
    const MAX_DETECTION_ATTEMPTS = 40; // Try for 20 seconds (40 * 500ms)

    // Debug mode for more verbose console logging
    const debugMode = true;

    // Add immediate debug info
    console.log('🎬 Progress tracking script loaded');
    console.log('📍 Current page URL:', window.location.href);
    console.log('🔍 Looking for video elements...');
    console.log('📺 Video players found:', document.querySelectorAll('.video-player').length);
    console.log('🎭 Plyr elements found:', document.querySelectorAll('.plyr').length);
    console.log('📹 YouTube iframes found:', document.querySelectorAll('iframe[src*="youtube"]').length);
    console.log('🎯 Lecture ID:', lectureId, 'Course ID:', courseId);

    // Debug logging function
    function debugLog(...args) {
        if (debugMode) {
            console.log('[Progress Tracker]', ...args);
        }
    }

    // Wait for YouTube API to be ready and Plyr to initialize
    function waitForPlayerInitialization() {
        debugLog('Waiting for player initialization...');

        playerReadyInterval = setInterval(() => {
            // Clear interval if we've tried too many times
            if (playerDetectionAttempts >= MAX_DETECTION_ATTEMPTS) {
                clearInterval(playerReadyInterval);
                debugLog('Giving up on player detection after max attempts');
                return;
            }

            playerDetectionAttempts++;

            // Check for Plyr instance of YouTube players (improved detection)
            const youtubePlyrElements = document.querySelectorAll('.plyr--youtube');
            const videoPlayerElements = document.querySelectorAll('.video-player');

            debugLog('Looking for Plyr players...', {
                youtubePlyrElements: youtubePlyrElements.length,
                videoPlayerElements: videoPlayerElements.length,
                windowPlyr: !!window.Plyr,
                plyrInstances: window.Plyr ? window.Plyr.instances : 'No Plyr'
            });

            if (youtubePlyrElements.length > 0 || videoPlayerElements.length > 0) {
                debugLog('Found potential Plyr elements');

                // Find all YouTube players initialized by Plyr
                const plyrInstances = [];

                // Method 1: Check elements for plyr property
                videoPlayerElements.forEach(element => {
                    if (element.plyr) {
                        debugLog('Found Plyr instance on element:', element);
                        plyrInstances.push(element.plyr);
                    }
                });

                // Method 2: Check global Plyr instances
                if (window.Plyr && window.Plyr.instances) {
                    window.Plyr.instances.forEach(instance => {
                        if (instance && instance.elements && instance.elements.container) {
                            debugLog('Found global Plyr instance:', instance);
                            plyrInstances.push(instance);
                        }
                    });
                }

                // Method 3: Check for any Plyr-like objects
                videoPlayerElements.forEach(element => {
                    // Look for any property that might be a Plyr instance
                    for (let prop in element) {
                        if (element[prop] && typeof element[prop] === 'object' &&
                            element[prop].constructor &&
                            element[prop].constructor.name === 'Plyr') {
                            debugLog('Found Plyr instance via property scan:', element[prop]);
                            plyrInstances.push(element[prop]);
                        }
                    }
                });

                // Remove duplicates
                const uniquePlyrInstances = [...new Set(plyrInstances)];

                if (uniquePlyrInstances.length > 0) {
                    debugLog('Found Plyr instances:', uniquePlyrInstances);
                    clearInterval(playerReadyInterval);
                    players = uniquePlyrInstances;
                    initializeProgressTracking();
                    return;
                }
            }

            // Check for YouTube iframe API being ready
            const youtubeIframes = document.querySelectorAll('iframe[src*="youtube"]');
            if (youtubeIframes.length > 0 && window.YT && window.YT.Player) {
                debugLog('Found YouTube iframes with API ready');

                // Get the player instances from the iframes
                const youtubePlayerInstances = Array.from(youtubeIframes).map(iframe => {
                    // Try to get the player instance
                    return iframe.id && window.YT.get(iframe.id);
                }).filter(Boolean);

                if (youtubePlayerInstances.length > 0) {
                    debugLog('Found YouTube player instances:', youtubePlayerInstances);
                    clearInterval(playerReadyInterval);
                    players = youtubePlayerInstances;
                    initializeProgressTracking();
                    return;
                }
            }

            // Check for plain HTML5 video elements
            const videoElements = document.querySelectorAll('video');
            if (videoElements.length > 0) {
                debugLog('Found HTML5 video elements:', videoElements);
                clearInterval(playerReadyInterval);
                players = Array.from(videoElements);
                initializeProgressTracking();
                return;
            }

            debugLog('Still looking for video players... Attempt', playerDetectionAttempts);

            // Fallback: If we've tried many times, try to force initialize with any available elements
            if (playerDetectionAttempts > 15) {
                debugLog('Attempting fallback player detection...');

                // Try to find any video-related elements
                const anyVideoElements = document.querySelectorAll('video, iframe[src*="youtube"], .video-player, .plyr');
                if (anyVideoElements.length > 0) {
                    debugLog('Found fallback video elements:', anyVideoElements);
                    clearInterval(playerReadyInterval);
                    players = Array.from(anyVideoElements);
                    initializeProgressTracking();
                    return;
                }
            }
        }, 500); // Check every 500ms
    }

    // Initialize progress tracking
    function initializeProgressTracking() {
        debugLog('Initializing progress tracking with players:', players);

        if (players.length === 0) {
            debugLog('No video players found');
            return;
        }

        // Set up the first player as current
        currentPlayer = players[0];
        debugLog('Current player set to:', currentPlayer);

        // Set up progress tracking for each player
        players.forEach(player => {
            setupProgressTracking(player);
        });

        // Load initial progress
        loadProgress();
    }

    // Function to set up progress tracking for a player
    function setupProgressTracking(player) {
        debugLog('Setting up tracking for player:', player);

        // Determine player type and set up appropriate event listeners
        if (player instanceof YT.Player) {
            // YouTube iframe API player
            debugLog('YouTube iframe API player detected');

            player.addEventListener('onStateChange', (event) => {
                const state = event.data;

                if (state === YT.PlayerState.PLAYING) {
                    debugLog('YouTube video playing');
                    clearInterval(progressInterval);
                    progressInterval = setInterval(() => updateProgress(), 5000);
                }
                else if (state === YT.PlayerState.PAUSED) {
                    debugLog('YouTube video paused');
                    updateProgress(true);
                    clearInterval(progressInterval);
                }
                else if (state === YT.PlayerState.ENDED) {
                    debugLog('YouTube video ended');
                    markLectureCompleted();
                    clearInterval(progressInterval);
                }
            });
        }
        else if (player.constructor && player.constructor.name === 'Plyr') {
            // Plyr player instance
            debugLog('Plyr player instance detected');

            player.on('play', () => {
                debugLog('Plyr video playing');
                clearInterval(progressInterval);
                progressInterval = setInterval(() => updateProgress(), 5000);

                // Reset recording detection when video starts playing
                videoEndedRecently = false;
                recordingWarningShown = false;
                visibilityChangeCount = 0;
                focusChangeCount = 0;
                suspiciousActivityScore = 0;
                console.log('🎬 Video started - reset recording detection flags');
            });

            player.on('pause', () => {
                debugLog('Plyr video paused');
                updateProgress(true);
                clearInterval(progressInterval);
            });

            player.on('ended', () => {
                debugLog('Plyr video ended');
                markLectureCompleted();
                clearInterval(progressInterval);

                // Disable recording detection for 5 minutes after video ends
                videoEndedRecently = true;
                console.log('🎬 Video ended - disabling recording detection for 5 minutes');
                setTimeout(() => {
                    videoEndedRecently = false;
                    console.log('🎬 Recording detection re-enabled after video end period');
                }, 300000); // 5 minutes
            });
        }
        else if (player instanceof HTMLVideoElement) {
            // Native HTML5 video element
            debugLog('Native HTML5 video element detected');

            player.addEventListener('play', () => {
                debugLog('HTML5 video playing');
                clearInterval(progressInterval);
                progressInterval = setInterval(() => updateProgress(), 5000);
            });

            player.addEventListener('pause', () => {
                debugLog('HTML5 video paused');
                updateProgress(true);
                clearInterval(progressInterval);
            });

            player.addEventListener('ended', () => {
                debugLog('HTML5 video ended');
                markLectureCompleted();
                clearInterval(progressInterval);
            });
        }
        else {
            debugLog('Unknown player type:', player);
            // Try generic approach for unknown player types
            try {
                if (player.on) {
                    // Looks like an event emitter interface
                    player.on('play', () => {
                        debugLog('Generic player playing');
                        clearInterval(progressInterval);
                        progressInterval = setInterval(() => updateProgress(), 5000);

                        // Reset recording detection when video starts playing
                        videoEndedRecently = false;
                        recordingWarningShown = false;
                        visibilityChangeCount = 0;
                        focusChangeCount = 0;
                        suspiciousActivityScore = 0;
                        console.log('🎬 Video started - reset recording detection flags');
                    });

                    player.on('pause', () => {
                        debugLog('Generic player paused');
                        updateProgress(true);
                        clearInterval(progressInterval);
                    });

                    player.on('ended', () => {
                        debugLog('Generic player ended');
                        markLectureCompleted();
                        clearInterval(progressInterval);
                    });
                }
                else if (player.addEventListener) {
                    // Looks like DOM event interface
                    player.addEventListener('play', () => {
                        debugLog('Generic DOM player playing');
                        clearInterval(progressInterval);
                        progressInterval = setInterval(() => updateProgress(), 5000);
                    });

                    player.addEventListener('pause', () => {
                        debugLog('Generic DOM player paused');
                        updateProgress(true);
                        clearInterval(progressInterval);
                    });

                    player.addEventListener('ended', () => {
                        debugLog('Generic DOM player ended');
                        markLectureCompleted();
                        clearInterval(progressInterval);
                    });
                }
            } catch (e) {
                debugLog('Error setting up event listeners for unknown player type:', e);
            }
        }
    }

    // Handle browser unload (tab close, navigation away)
    window.addEventListener('beforeunload', () => {
        updateProgress(true);
    });

    // Function to update progress
    function updateProgress(force = false) {
        // Don't update if another update is in progress
        if (isProgressUpdating) {
            debugLog('Progress update already in progress, skipping');
            return;
        }

        // Don't update too frequently unless forced
        const now = Date.now();
        if (!force && (now - lastProgressUpdate < 3000)) {
            debugLog('Skipping update as too recent');
            return;
        }

        // Don't update if lecture is already completed
        if (userCompletedLecture) {
            debugLog('Lecture is already completed, skipping update');
            return;
        }

        // Try to find a player if we don't have one
        if (!currentPlayer) {
            debugLog('No current player, trying to find one...');

            // Try to find any video element
            const videoElements = document.querySelectorAll('video');
            if (videoElements.length > 0) {
                currentPlayer = videoElements[0];
                debugLog('Found HTML5 video element:', currentPlayer);
            }

            // Try to find Plyr instances
            const plyrElements = document.querySelectorAll('.video-player');
            plyrElements.forEach(element => {
                if (element.plyr) {
                    currentPlayer = element.plyr;
                    debugLog('Found Plyr instance:', currentPlayer);
                }
            });

            // If still no player, skip this update
            if (!currentPlayer) {
                debugLog('Still no current player found, skipping update');
                return;
            }
        }

        try {
            // Get current time and duration based on player type
            let currentTime = 0;
            let duration = 0;

            if (currentPlayer instanceof YT.Player) {
                // YouTube iframe API player
                try {
                    currentTime = Math.floor(currentPlayer.getCurrentTime());
                    duration = Math.floor(currentPlayer.getDuration());
                    debugLog('YouTube API player time:', currentTime, 'duration:', duration);
                } catch (e) {
                    debugLog('Error getting YouTube player time:', e);
                    return;
                }
            }
            else if (currentPlayer.constructor && currentPlayer.constructor.name === 'Plyr') {
                // Plyr player instance
                currentTime = Math.floor(currentPlayer.currentTime);
                duration = Math.floor(currentPlayer.duration);
                debugLog('Plyr player time:', currentTime, 'duration:', duration);
            }
            else if (currentPlayer instanceof HTMLVideoElement) {
                // Native HTML5 video element
                currentTime = Math.floor(currentPlayer.currentTime);
                duration = Math.floor(currentPlayer.duration);
                debugLog('HTML5 video time:', currentTime, 'duration:', duration);
            }
            else {
                // Try generic approaches
                try {
                    if (typeof currentPlayer.getCurrentTime === 'function') {
                        currentTime = Math.floor(currentPlayer.getCurrentTime());
                        duration = Math.floor(currentPlayer.getDuration());
                    } else if (currentPlayer.currentTime !== undefined) {
                        currentTime = Math.floor(currentPlayer.currentTime);
                        duration = Math.floor(currentPlayer.duration);
                    }
                    debugLog('Generic player time:', currentTime, 'duration:', duration);
                } catch (e) {
                    debugLog('Error getting generic player time:', e);
                    return;
                }
            }

            // Skip if we don't have valid time data
            if (isNaN(currentTime) || isNaN(duration) || duration <= 0) {
                debugLog('Invalid time data, skipping update');
                return;
            }

            // Calculate progress percentage
            const progressPercent = (currentTime / duration) * 100;

            // Update UI first (for immediate feedback)
            updateProgressUI(progressPercent);

            // Only send to server if we have meaningful progress
            if (currentTime > 0 && duration > 0) {
                debugLog(`Sending progress to server: ${currentTime}s / ${duration}s (${progressPercent.toFixed(1)}%)`);

                const requestData = {
                    lecture_id: lectureId,
                    current_time: currentTime,
                    duration: duration
                };
                console.log('📤 Sending progress data:', requestData);

                isProgressUpdating = true;
                lastProgressUpdate = now;

                // Get CSRF token
                const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                // Send progress to server
                fetch('<?php echo e(route("lecture-progress.update")); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': token,
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => {
                    console.log('📡 Server response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`Server responded with ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('📊 Server response data:', data);
                    if (data.success) {
                        debugLog('Progress updated successfully:', data);
                        console.log('✅ Progress saved to database successfully!');

                        // Update progress display
                        updateProgressDisplay(data.progress.progress_percent);

                        // Update course progress
                        updateCourseProgress(data.course_progress);

                        // Check if lecture is now marked as completed
                        if (data.lecture_completed && !userCompletedLecture) {
                            userCompletedLecture = true;
                            showCompletionMessage();
                        }
                    } else {
                        console.error('❌ Progress update returned error:', data);
                        debugLog('Progress update returned error:', data);
                    }
                })
                .catch(error => {
                    console.error('❌ Error updating progress:', error);
                    console.error('Full error details:', error.message);
                })
                .finally(() => {
                    isProgressUpdating = false;
                });
            } else {
                debugLog('Not enough meaningful progress to update server');
                isProgressUpdating = false;
            }
        } catch (e) {
            console.error('Error in updateProgress:', e);
            isProgressUpdating = false;
        }
    }

    // Function to update UI with progress
    function updateProgressUI(progressPercent) {
        // Update progress bar
        const progressBar = document.querySelector('.lecture-progress-bar');
        if (progressBar) {
            progressBar.style.width = progressPercent + '%';
            progressBar.setAttribute('aria-valuenow', progressPercent);
        }

        // Update progress text
        const progressDisplay = document.querySelector('.lecture-progress-display');
        if (progressDisplay) {
            progressDisplay.textContent = Math.round(progressPercent) + '%';
        }
    }

    // Function to load existing progress
    function loadProgress() {
        debugLog('Loading existing progress data');

        // Don't load if player isn't ready
        if (!currentPlayer) {
            debugLog('No current player, skipping loading progress');
            return;
        }

        // Get CSRF token
        const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        // Fetch progress from server
        fetch('<?php echo e(route("lecture-progress.get")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': token,
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                lecture_id: lectureId
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Server responded with ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                debugLog('Loaded progress data:', data);

                // If there's saved progress and it's not at the beginning
                if (data.progress && data.progress.current_time > 0) {
                    // Ask user if they want to resume from where they left off
                    const resumeTime = data.progress.current_time;
                    const formattedTime = formatTime(resumeTime);

                    // Create resume dialog
                    const resumeDialog = document.createElement('div');
                    resumeDialog.className = 'resume-dialog';
                    resumeDialog.innerHTML = `
                        <div class="resume-dialog-content bg-light p-3 rounded shadow-sm position-absolute top-50 start-50 translate-middle" style="z-index: 1000; width: 300px;">
                            <p>You were at <strong>${formattedTime}</strong>. Would you like to resume from where you left off?</p>
                            <div class="d-flex justify-content-end gap-2">
                                <button class="btn btn-sm btn-outline-secondary resume-no">No, Start Over</button>
                                <button class="btn btn-sm btn-primary resume-yes">Yes, Resume</button>
                            </div>
                        </div>
                    `;

                    // Find video container and append dialog
                    const videoContainer = document.querySelector('.video-container') ||
                                          document.querySelector('.ratio-16x9');

                    if (videoContainer) {
                        videoContainer.style.position = 'relative';
                        videoContainer.appendChild(resumeDialog);

                        // Handle resume choice
                        resumeDialog.querySelector('.resume-yes').addEventListener('click', function() {
                            // Set time based on player type
                            if (currentPlayer instanceof YT.Player) {
                                currentPlayer.seekTo(resumeTime);
                            }
                            else if (currentPlayer.constructor && currentPlayer.constructor.name === 'Plyr') {
                                currentPlayer.currentTime = resumeTime;
                            }
                            else if (currentPlayer instanceof HTMLVideoElement) {
                                currentPlayer.currentTime = resumeTime;
                            }
                            else {
                                // Try generic approach
                                try {
                                    if (typeof currentPlayer.seekTo === 'function') {
                                        currentPlayer.seekTo(resumeTime);
                                    } else if (currentPlayer.currentTime !== undefined) {
                                        currentPlayer.currentTime = resumeTime;
                                    }
                                } catch (e) {
                                    debugLog('Error seeking to time:', e);
                                }
                            }

                            videoContainer.removeChild(resumeDialog);
                        });

                        resumeDialog.querySelector('.resume-no').addEventListener('click', function() {
                            // Set to beginning based on player type
                            if (currentPlayer instanceof YT.Player) {
                                currentPlayer.seekTo(0);
                            }
                            else if (currentPlayer.constructor && currentPlayer.constructor.name === 'Plyr') {
                                currentPlayer.currentTime = 0;
                            }
                            else if (currentPlayer instanceof HTMLVideoElement) {
                                currentPlayer.currentTime = 0;
                            }
                            else {
                                // Try generic approach
                                try {
                                    if (typeof currentPlayer.seekTo === 'function') {
                                        currentPlayer.seekTo(0);
                                    } else if (currentPlayer.currentTime !== undefined) {
                                        currentPlayer.currentTime = 0;
                                    }
                                } catch (e) {
                                    debugLog('Error seeking to beginning:', e);
                                }
                            }

                            videoContainer.removeChild(resumeDialog);
                        });

                        // Auto-hide after 10 seconds (user didn't choose)
                        setTimeout(() => {
                            if (videoContainer.contains(resumeDialog)) {
                                // Default to resuming if user doesn't choose
                                try {
                                    if (currentPlayer instanceof YT.Player) {
                                        currentPlayer.seekTo(resumeTime);
                                    }
                                    else if (currentPlayer.constructor && currentPlayer.constructor.name === 'Plyr') {
                                        currentPlayer.currentTime = resumeTime;
                                    }
                                    else if (currentPlayer instanceof HTMLVideoElement) {
                                        currentPlayer.currentTime = resumeTime;
                                    }
                                    else {
                                        // Try generic approach
                                        if (typeof currentPlayer.seekTo === 'function') {
                                            currentPlayer.seekTo(resumeTime);
                                        } else if (currentPlayer.currentTime !== undefined) {
                                            currentPlayer.currentTime = resumeTime;
                                        }
                                    }
                                } catch (e) {
                                    debugLog('Error auto-resuming:', e);
                                }

                                videoContainer.removeChild(resumeDialog);
                            }
                        }, 10000);
                    }
                }

                // Update progress display
                const progressToShow = data.progress ? data.progress.progress_percent : 0;
                console.log('📊 Loading progress from database:', progressToShow + '%');
                console.log('📊 Full progress data:', data.progress);
                updateProgressDisplay(progressToShow);

                // Update course progress
                updateCourseProgress(data.course_progress);

                // Check if lecture is already completed
                if (data.lecture_completed) {
                    userCompletedLecture = true;
                }
            } else {
                debugLog('Error loading progress:', data);
            }
        })
        .catch(error => {
            console.error('Error loading progress:', error);
        });
    }

    // Function to mark lecture as completed
    function markLectureCompleted() {
        // Don't mark again if already completed
        if (userCompletedLecture) {
            debugLog('Lecture already completed, skipping markLectureCompleted');
            return;
        }

        debugLog('Marking lecture as completed');

        // Get CSRF token
        const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        // Send completion to server
        fetch('<?php echo e(route("lecture-progress.complete")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': token,
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                lecture_id: lectureId
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Server responded with ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                debugLog('Lecture marked as completed:', data);
                userCompletedLecture = true;

                // Show completion message
                showCompletionMessage();

                // Update course progress
                updateCourseProgress(data.course_progress);
            } else {
                debugLog('Error marking lecture as completed:', data);
            }
        })
        .catch(error => {
            console.error('Error marking lecture as completed:', error);
        });
    }

    // Function to update progress display
    function updateProgressDisplay(percent) {
        console.log('🎯 updateProgressDisplay called with:', percent + '%');
        console.log('🎯 Rounded percentage:', Math.round(percent) + '%');

        // Find progress display elements
        const progressDisplays = document.querySelectorAll('.lecture-progress-display');
        console.log('🎯 Found progress displays:', progressDisplays.length);

        progressDisplays.forEach((display, index) => {
            const newText = `${Math.round(percent)}%`;
            console.log(`🎯 Updating display ${index + 1} to:`, newText);
            display.textContent = newText;
        });

        // Update progress bars
        const progressBars = document.querySelectorAll('.lecture-progress-bar');
        console.log('🎯 Found progress bars:', progressBars.length);

        progressBars.forEach((bar, index) => {
            console.log(`🎯 Updating bar ${index + 1} width to:`, percent + '%');
            bar.style.width = `${percent}%`;
            bar.setAttribute('aria-valuenow', percent);

            // Update color based on completion
            if (percent >= 90) {
                bar.classList.remove('bg-primary');
                bar.classList.add('bg-success');
                console.log(`🎯 Bar ${index + 1} marked as completed (green)`);
            }
        });
    }

    // Function to update course progress
    function updateCourseProgress(courseProgress) {
        // Check if data exists
        if (!courseProgress) {
            debugLog('No course progress data provided');
            return;
        }

        debugLog('Updating course progress UI with:', courseProgress);

        // Update course progress display
        const courseProgressDisplays = document.querySelectorAll('.course-progress-display');
        courseProgressDisplays.forEach(display => {
            display.textContent = `${Math.round(courseProgress.percent)}%`;
        });

        // Update course progress bars
        const courseProgressBars = document.querySelectorAll('.course-progress-bar');
        courseProgressBars.forEach(bar => {
            bar.style.width = `${courseProgress.percent}%`;
            bar.setAttribute('aria-valuenow', courseProgress.percent);

            // Update color based on completion
            if (courseProgress.percent >= 90) {
                bar.classList.remove('bg-info');
                bar.classList.add('bg-success');
            }
        });

        // Update completed lectures count
        const lectureCountDisplays = document.querySelectorAll('.completed-lectures-display');
        lectureCountDisplays.forEach(display => {
            display.textContent = `${courseProgress.completed_lectures}/${courseProgress.total_lectures}`;
        });
    }

    // Function to show completion message
    function showCompletionMessage() {
        debugLog('Showing completion message');

        // Create completion message
        const completionMessage = document.createElement('div');
        completionMessage.className = 'completion-message';
        completionMessage.innerHTML = `
            <div class="alert alert-success alert-dismissible fade show d-flex align-items-center" role="alert">
                <div class="me-3">
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
                <div>
                    <h5 class="mb-0">Congratulations!</h5>
                    <p class="mb-0">You've completed this lecture. Continue to the next one to keep learning!</p>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        // Add to page
        const lectureCard = document.querySelector('.card-body');
        if (lectureCard) {
            lectureCard.insertBefore(completionMessage, lectureCard.firstChild);
        }
    }

    // Helper function to format time in MM:SS
    function formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    // Manual Plyr initialization and progress tracking
    function initializePlyrDirectly() {
        debugLog('🚀 Attempting direct Plyr initialization...');

        // Check if Plyr is available
        if (typeof Plyr === 'undefined') {
            debugLog('❌ Plyr not loaded yet, retrying...');
            setTimeout(initializePlyrDirectly, 1000);
            return;
        }

        debugLog('✅ Plyr is available, looking for video elements...');

        // Find video player elements
        const videoElements = document.querySelectorAll('.video-player');
        debugLog('📺 Found video elements:', videoElements.length);

        if (videoElements.length > 0) {
            videoElements.forEach((element, index) => {
                debugLog(`🎬 Processing video element ${index + 1}:`, element);

                // Check if already has Plyr instance
                if (element.plyr) {
                    debugLog('✅ Element already has Plyr instance:', element.plyr);
                    if (!players.includes(element.plyr)) {
                        players.push(element.plyr);
                        currentPlayer = element.plyr;
                        setupProgressTracking(element.plyr);
                    }
                } else {
                    debugLog('🔧 Creating new Plyr instance for element...');

                    try {
                        // Create new Plyr instance
                        const plyrInstance = new Plyr(element, {
                            controls: ['play-large', 'play', 'progress', 'current-time', 'mute', 'volume', 'fullscreen'],
                            youtube: {
                                noCookie: false,
                                rel: 0,
                                showinfo: 0,
                                iv_load_policy: 3,
                                modestbranding: 1
                            }
                        });

                        debugLog('✅ Plyr instance created:', plyrInstance);

                        // Add to players array
                        players.push(plyrInstance);
                        currentPlayer = plyrInstance;

                        // Set up progress tracking
                        setupProgressTracking(plyrInstance);

                        debugLog('🎯 Progress tracking set up for Plyr instance');

                    } catch (error) {
                        debugLog('❌ Error creating Plyr instance:', error);
                    }
                }
            });
        } else {
            debugLog('❌ No video elements found');
        }
    }

    // Wait a bit to allow page resources to load before initializing
    setTimeout(() => {
        debugLog('🚀 Starting player initialization...');

        // Try direct Plyr initialization first
        initializePlyrDirectly();

        // Also start the original detection method
        waitForPlayerInitialization();

        // Make a direct attempt to update progress every minute regardless of player state
        // This ensures we capture progress even if events aren't firing
        setInterval(() => {
            updateProgress(true);
        }, 60000);

        // Alternative progress tracking - try to get time from any available source
        setInterval(() => {
            if (!currentPlayer) {
                debugLog('🔍 Trying alternative progress detection...');

                // Try to find any video element with time data
                const videoElements = document.querySelectorAll('video');
                const iframes = document.querySelectorAll('iframe[src*="youtube"]');

                videoElements.forEach(video => {
                    if (video.currentTime && video.duration) {
                        debugLog('📹 Found HTML5 video with time data:', {
                            currentTime: video.currentTime,
                            duration: video.duration
                        });

                        // Use this video for progress tracking
                        if (!players.includes(video)) {
                            players.push(video);
                            currentPlayer = video;
                            setupProgressTracking(video);
                        }
                    }
                });

                // Check for Plyr instances in global scope
                if (window.Plyr && window.Plyr.instances) {
                    window.Plyr.instances.forEach(instance => {
                        if (instance && !players.includes(instance)) {
                            debugLog('🎭 Found global Plyr instance:', instance);
                            players.push(instance);
                            currentPlayer = instance;
                            setupProgressTracking(instance);
                        }
                    });
                }
            }
        }, 15000); // Every 15 seconds

        // Fallback: Try to manually detect and track progress every 10 seconds
        setInterval(() => {
            if (!currentPlayer || players.length === 0) {
                debugLog('🔄 Attempting manual player detection...');

                // Try direct Plyr initialization again
                initializePlyrDirectly();

                // Try to find any Plyr instances manually
                const plyrElements = document.querySelectorAll('.plyr--youtube, .video-player');
                plyrElements.forEach(element => {
                    if (element.plyr && !players.includes(element.plyr)) {
                        debugLog('🎯 Found new Plyr instance manually:', element.plyr);
                        players.push(element.plyr);
                        currentPlayer = element.plyr;
                        setupProgressTracking(element.plyr);
                    }
                });

                // Try to find YouTube iframes
                const youtubeIframes = document.querySelectorAll('iframe[src*="youtube"]');
                youtubeIframes.forEach(iframe => {
                    if (iframe.id && window.YT && window.YT.get) {
                        const player = window.YT.get(iframe.id);
                        if (player && !players.includes(player)) {
                            debugLog('🎯 Found new YouTube player manually:', player);
                            players.push(player);
                            currentPlayer = player;
                            setupProgressTracking(player);
                        }
                    }
                });
            } else {
                debugLog('✅ Current player status:', {
                    currentPlayer: !!currentPlayer,
                    playersCount: players.length,
                    playerType: currentPlayer ? currentPlayer.constructor.name : 'none'
                });
            }
        }, 10000); // Every 10 seconds
    }, 2000); // Wait 2 seconds for page to load
});

// ============================================================================
// 🎯 SIMPLE PROGRESS TRACKING (FALLBACK METHOD)
// ============================================================================

// Simple progress tracking that works with any video element
function startSimpleProgressTracking() {
    console.log('🎯 Starting simple progress tracking...');

    setInterval(() => {
        // Try to find any video element and track its progress
        const videoElements = document.querySelectorAll('video');
        const iframes = document.querySelectorAll('iframe[src*="youtube"]');

        let currentTime = 0;
        let duration = 0;
        let foundVideo = false;

        // Check HTML5 video elements
        videoElements.forEach(video => {
            if (video.currentTime && video.duration && video.duration > 0) {
                currentTime = Math.floor(video.currentTime);
                duration = Math.floor(video.duration);
                foundVideo = true;
                console.log('📹 HTML5 Video Progress:', currentTime, '/', duration);
            }
        });

        // Check for Plyr instances
        if (!foundVideo && window.Plyr && window.Plyr.instances) {
            window.Plyr.instances.forEach(instance => {
                if (instance && instance.currentTime && instance.duration && instance.duration > 0) {
                    currentTime = Math.floor(instance.currentTime);
                    duration = Math.floor(instance.duration);
                    foundVideo = true;
                    console.log('🎭 Plyr Progress:', currentTime, '/', duration);
                }
            });
        }

        // Check YouTube API if available
        if (!foundVideo && window.YT) {
            iframes.forEach(iframe => {
                if (iframe.id) {
                    try {
                        const player = window.YT.get(iframe.id);
                        if (player && typeof player.getCurrentTime === 'function') {
                            currentTime = Math.floor(player.getCurrentTime());
                            duration = Math.floor(player.getDuration());
                            if (duration > 0) {
                                foundVideo = true;
                                console.log('📺 YouTube Progress:', currentTime, '/', duration);
                            }
                        }
                    } catch (e) {
                        // Ignore errors
                    }
                }
            });
        }

        // If we found video progress, send it to server
        if (foundVideo && currentTime > 0 && duration > 0) {
            const progressPercent = (currentTime / duration) * 100;

            // Update UI
            const progressBar = document.querySelector('.lecture-progress-bar');
            if (progressBar) {
                progressBar.style.width = progressPercent + '%';
            }

            const progressDisplay = document.querySelector('.lecture-progress-display');
            if (progressDisplay) {
                progressDisplay.textContent = Math.round(progressPercent) + '%';
            }

            // Send to server (throttled)
            if (!window.lastSimpleProgressUpdate || (Date.now() - window.lastSimpleProgressUpdate > 10000)) {
                window.lastSimpleProgressUpdate = Date.now();

                console.log('📤 Sending simple progress to server:', progressPercent.toFixed(1) + '%');

                const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                fetch('<?php echo e(route("lecture-progress.update")); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': token,
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        lecture_id: lectureId,
                        current_time: currentTime,
                        duration: duration
                    })
                })
                .then(response => {
                    console.log('📡 Simple progress response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('📊 Simple progress response data:', data);
                    if (data.success) {
                        console.log('✅ Simple progress updated successfully');
                    } else {
                        console.error('❌ Simple progress update failed:', data);
                    }
                })
                .catch(error => {
                    console.error('❌ Simple progress error:', error);
                })
                .catch(error => {
                    console.log('❌ Simple progress update failed:', error);
                });
            }
        } else {
            console.log('🔍 No video progress found in simple tracking');
        }

    }, 5000); // Check every 5 seconds
}

// Start simple progress tracking after a delay
setTimeout(() => {
    startSimpleProgressTracking();
}, 5000);

// ============================================================================
// 🧪 MANUAL PROGRESS TESTING INTERFACE
// ============================================================================

// Add manual testing interface for debugging
setTimeout(() => {
    // Always show manual tester for debugging (remove this condition later)
    if (true || debugMode) {
        console.log('🧪 Adding manual progress testing interface...');

        const testInterface = document.createElement('div');
        testInterface.innerHTML = `
            <div id="manual-progress-tester" style="
                position: fixed;
                top: 10px;
                left: 10px;
                background: rgba(0,0,0,0.9);
                color: white;
                padding: 15px;
                border-radius: 8px;
                z-index: 9999;
                font-family: monospace;
                font-size: 12px;
                min-width: 300px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.5);
            ">
                <div style="font-weight: bold; margin-bottom: 10px; color: #4CAF50;">📊 Manual Progress Tester</div>
                <div>Lecture ID: ${lectureId}</div>
                <div>Current Time: <span id="manual-time">0</span>s</div>
                <div>Duration: <span id="manual-duration">300</span>s</div>
                <div>Progress: <span id="manual-progress">0</span>%</div>
                <div style="margin: 10px 0;">
                    <button onclick="window.manualProgressTest(30)" style="margin: 2px; padding: 5px 8px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;">+30s</button>
                    <button onclick="window.manualProgressTest(60)" style="margin: 2px; padding: 5px 8px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;">+60s</button>
                    <button onclick="window.manualProgressTest(120)" style="margin: 2px; padding: 5px 8px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;">+2min</button>
                </div>
                <div style="margin: 10px 0;">
                    <button onclick="window.manualProgressSave()" style="margin: 2px; padding: 5px 8px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer;">💾 Save Progress</button>
                    <button onclick="window.manualProgressReset()" style="margin: 2px; padding: 5px 8px; background: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer;">🔄 Reset</button>
                </div>
                <div style="margin: 10px 0;">
                    <button onclick="document.getElementById('manual-progress-tester').style.display='none'" style="margin: 2px; padding: 5px 8px; background: #6c757d; color: white; border: none; border-radius: 3px; cursor: pointer;">❌ Hide</button>
                </div>
            </div>
        `;
        document.body.appendChild(testInterface);

        // Global variables for manual testing
        window.manualCurrentTime = 0;
        window.manualDuration = 300;

        // Manual progress functions
        window.manualProgressTest = function(seconds) {
            window.manualCurrentTime = Math.min(window.manualCurrentTime + seconds, window.manualDuration);
            updateManualDisplay();
            console.log(`🧪 Manual progress: ${window.manualCurrentTime}s / ${window.manualDuration}s`);
        };

        window.manualProgressReset = function() {
            window.manualCurrentTime = 0;
            updateManualDisplay();
            console.log('🔄 Manual progress reset');
        };

        window.manualProgressSave = function() {
            console.log('💾 Saving manual progress...');

            const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            const requestData = {
                lecture_id: lectureId,
                current_time: window.manualCurrentTime,
                duration: window.manualDuration
            };

            console.log('📤 Manual sending data:', requestData);

            fetch('<?php echo e(route("lecture-progress.update")); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': token,
                    'Accept': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                console.log('📡 Manual response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('📊 Manual response data:', data);
                if (data.success) {
                    console.log('✅ Manual progress saved successfully!');

                    // Show success message
                    const successMsg = document.createElement('div');
                    successMsg.style.cssText = 'position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: #28a745; color: white; padding: 20px; border-radius: 8px; z-index: 10000; font-weight: bold;';
                    successMsg.textContent = '✅ Progress Saved Successfully!';
                    document.body.appendChild(successMsg);
                    setTimeout(() => successMsg.remove(), 2000);
                } else {
                    console.error('❌ Manual progress save failed:', data);
                }
            })
            .catch(error => {
                console.error('❌ Manual progress error:', error);
            });
        };

        function updateManualDisplay() {
            const progressPercent = (window.manualCurrentTime / window.manualDuration) * 100;
            document.getElementById('manual-time').textContent = window.manualCurrentTime;
            document.getElementById('manual-progress').textContent = progressPercent.toFixed(1);
        }

        updateManualDisplay();
    }
}, 3000);

// ============================================================================
// 🛡️ ENHANCED PAGE SECURITY WITH RIGHT-CLICK PROTECTION
// ============================================================================

// Comprehensive right-click blocking for entire page
document.addEventListener('contextmenu', function(e) {
    e.preventDefault();
    e.stopPropagation();
    console.log('🚫 Right-click completely disabled on entire page');

    // Show security warning
    showSecurityWarning('Right-click is disabled for content protection');
    return false;
}, true);

// Enhanced keyboard shortcut blocking
document.addEventListener('keydown', function(e) {
    // Block all developer tools and inspection shortcuts
    const blockedKeys = [
        { key: 123, desc: 'F12 (Developer Tools)' },                    // F12
        { ctrl: true, shift: true, key: 73, desc: 'Ctrl+Shift+I' },     // Inspect Element
        { ctrl: true, shift: true, key: 74, desc: 'Ctrl+Shift+J' },     // Console
        { ctrl: true, shift: true, key: 67, desc: 'Ctrl+Shift+C' },     // Element Selector
        { ctrl: true, key: 85, desc: 'Ctrl+U (View Source)' },          // View Source
        { ctrl: true, key: 83, desc: 'Ctrl+S (Save Page)' },            // Save Page
        { ctrl: true, shift: true, key: 83, desc: 'Ctrl+Shift+S' },     // Save As
        { key: 116, desc: 'F5 (Refresh)' },                             // F5 Refresh
        { ctrl: true, key: 116, desc: 'Ctrl+F5' },                      // Hard Refresh
        { ctrl: true, shift: true, key: 116, desc: 'Ctrl+Shift+F5' },   // Hard Refresh
        { ctrl: true, key: 82, desc: 'Ctrl+R (Refresh)' },              // Ctrl+R
        { ctrl: true, shift: true, key: 82, desc: 'Ctrl+Shift+R' },     // Hard Refresh
    ];

    for (let blocked of blockedKeys) {
        if (e.keyCode === blocked.key &&
            (!blocked.ctrl || e.ctrlKey) &&
            (!blocked.shift || e.shiftKey) &&
            (!blocked.alt || e.altKey)) {

            e.preventDefault();
            e.stopPropagation();
            console.log(`🚫 Blocked: ${blocked.desc}`);
            showSecurityWarning(`${blocked.desc} is disabled for content protection`);
            return false;
        }
    }
});

// Block mouse combinations that could access context menus
document.addEventListener('mousedown', function(e) {
    // Block right mouse button (button 2)
    if (e.button === 2) {
        e.preventDefault();
        e.stopPropagation();
        console.log('🚫 Right mouse button blocked');
        return false;
    }

    // Block middle mouse button (button 1) - sometimes used for developer tools
    if (e.button === 1) {
        e.preventDefault();
        e.stopPropagation();
        console.log('🚫 Middle mouse button blocked');
        return false;
    }
});

// Prevent text selection on entire page
document.addEventListener('DOMContentLoaded', function() {
    document.body.style.userSelect = 'none';
    document.body.style.webkitUserSelect = 'none';
    document.body.style.mozUserSelect = 'none';
    document.body.style.msUserSelect = 'none';
    document.body.style.webkitTouchCallout = 'none';
    document.body.style.webkitUserDrag = 'none';
    document.body.style.webkitTapHighlightColor = 'transparent';
});

// Security warning display function
function showSecurityWarning(message) {
    // Remove existing warning if any
    const existingWarning = document.getElementById('security-warning');
    if (existingWarning) {
        existingWarning.remove();
    }

    // Create warning popup
    const warning = document.createElement('div');
    warning.id = 'security-warning';
    warning.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #ff4444, #cc0000);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(255, 68, 68, 0.3);
        z-index: 10000;
        font-family: Arial, sans-serif;
        font-size: 14px;
        font-weight: bold;
        max-width: 300px;
        animation: slideIn 0.3s ease-out;
    `;

    warning.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <span style="font-size: 18px;">🛡️</span>
            <div>
                <div style="font-size: 16px; margin-bottom: 5px;">Security Protection</div>
                <div style="font-size: 12px; opacity: 0.9;">${message}</div>
            </div>
        </div>
    `;

    document.body.appendChild(warning);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (warning && warning.parentNode) {
            warning.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (warning && warning.parentNode) {
                    warning.remove();
                }
            }, 300);
        }
    }, 3000);
}

// Add CSS animations for security warning
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// ============================================================================
// 🚫 ADVANCED SCREEN RECORDING PROTECTION
// ============================================================================

// Screen recording detection and prevention
let isRecordingDetected = false;
let recordingWarningShown = false;

// Method 1: Detect screen recording via media devices
async function detectScreenRecording() {
    try {
        // Check if screen capture is available
        if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
            // Monitor for screen capture attempts
            const originalGetDisplayMedia = navigator.mediaDevices.getDisplayMedia;

            navigator.mediaDevices.getDisplayMedia = function(...args) {
                console.log('🚫 Screen recording attempt detected!');
                showRecordingWarning();

                // Block the screen recording
                return Promise.reject(new Error('Screen recording is not allowed on this page'));
            };
        }
    } catch (error) {
        console.log('Screen recording detection setup failed:', error);
    }
}

// Method 2: Detect screen recording via visibility changes (improved - excludes Alt+Tab)
let visibilityChangeCount = 0;
let lastVisibilityChange = 0;

document.addEventListener('visibilitychange', function() {
    // Skip detection if video ended recently
    if (videoEndedRecently) {
        console.log('🎬 Video ended recently, skipping visibility change detection');
        return;
    }

    const now = Date.now();

    if (document.hidden) {
        // Don't count visibility changes during Alt+Tab
        if (!isAltTabbing) {
            lastVisibilityChange = now;
            visibilityChangeCount++;

            // Only trigger if there are multiple rapid visibility changes (suspicious)
            // AND it's not during Alt+Tab period AND video didn't end recently
            if (visibilityChangeCount > 7 && !recordingWarningShown && !isAltTabbing && !videoEndedRecently) { // Increased threshold from 5 to 7
                setTimeout(() => {
                    if (!document.hidden && !recordingWarningShown && !isAltTabbing && !videoEndedRecently) {
                        console.log('🚨 Multiple visibility changes detected - possible recording');
                        showRecordingWarning();
                        recordingWarningShown = true;
                    }
                }, 5000); // Increased delay from 3 seconds to 5 seconds
            }
        }
    } else {
        // Reset counter after some time of normal usage
        setTimeout(() => {
            visibilityChangeCount = Math.max(0, visibilityChangeCount - 1);
        }, 20000); // Increased reset time from 15 seconds to 20 seconds
    }
});

// Method 3: Improved focus change detection (excludes Alt+Tab completely)
let focusLostTime = 0;
let focusChangeCount = 0;
let isAltTabbing = false;
let altTabStartTime = 0;

// Detect Alt+Tab and other Alt combinations
document.addEventListener('keydown', function(e) {
    if (e.altKey && e.keyCode === 9) { // Alt+Tab
        isAltTabbing = true;
        altTabStartTime = Date.now();
        console.log('🔄 Alt+Tab detected - disabling recording detection');

        // Extended timeout for Alt+Tab session
        setTimeout(() => {
            isAltTabbing = false;
            console.log('🔄 Alt+Tab session ended');
        }, 5000); // Longer timeout for Alt+Tab
    }

    // Also detect other Alt combinations that might cause window switching
    if (e.altKey && (e.keyCode === 27 || e.keyCode === 18)) { // Alt+Esc, Alt key alone
        isAltTabbing = true;
        setTimeout(() => {
            isAltTabbing = false;
        }, 3000);
    }
});

// Also detect when Alt key is released to end Alt+Tab session sooner
document.addEventListener('keyup', function(e) {
    if (e.keyCode === 18 && isAltTabbing) { // Alt key released
        setTimeout(() => {
            isAltTabbing = false;
            console.log('🔄 Alt key released - ending Alt+Tab session');
        }, 1000); // Short delay after Alt release
    }
});

window.addEventListener('blur', function() {
    // Skip detection if video ended recently
    if (videoEndedRecently) {
        console.log('🎬 Video ended recently, skipping blur detection');
        return;
    }

    if (!isAltTabbing) {
        focusLostTime = Date.now();
        focusChangeCount++;
        console.log('🔍 Window blur detected (not Alt+Tab)');
    } else {
        console.log('🔄 Window blur during Alt+Tab - ignoring');
    }
});

window.addEventListener('focus', function() {
    // Skip detection if video ended recently
    if (videoEndedRecently) {
        console.log('🎬 Video ended recently, skipping focus detection');
        return;
    }

    if (focusLostTime > 0 && !isAltTabbing) {
        const timeLost = Date.now() - focusLostTime;

        // Only trigger if:
        // 1. Focus was lost for more than 30 seconds (increased from 15 seconds)
        // 2. Multiple focus changes occurred (more than 5, increased from 3)
        // 3. Not during any Alt+Tab session
        // 4. Video didn't end recently
        if (timeLost > 30000 && focusChangeCount > 5 && !recordingWarningShown && !videoEndedRecently) {
            console.log('🚨 Suspicious focus pattern detected - possible recording');
            showRecordingWarning();
            recordingWarningShown = true;
        } else {
            console.log(`🔍 Focus regained after ${timeLost}ms - normal behavior`);
        }

        // Reset counter periodically
        setTimeout(() => {
            focusChangeCount = Math.max(0, focusChangeCount - 1);
        }, 20000); // Even longer reset time
    } else if (isAltTabbing) {
        console.log('🔄 Focus regained during Alt+Tab - normal window switching');
    }

    focusLostTime = 0;
});

// Method 4: Prevent specific recording shortcuts (improved)
document.addEventListener('keydown', function(e) {
    // Block Windows Game Bar (Win + G) - but allow other Win combinations
    if ((e.metaKey || e.key === 'Meta') && e.keyCode === 71) {
        e.preventDefault();
        showRecordingWarning();
        console.log('🚫 Windows Game Bar shortcut blocked');
        return false;
    }

    // Block specific recording shortcuts (but not Alt+Tab)
    if (e.altKey && e.keyCode === 82 && !e.ctrlKey && !e.shiftKey) { // Alt+R only
        e.preventDefault();
        showRecordingWarning();
        console.log('🚫 Alt+R recording shortcut blocked');
        return false;
    }

    // Block Print Screen
    if (e.keyCode === 44) { // Print Screen
        e.preventDefault();
        showSecurityWarning('Screenshots are disabled for content protection');
        console.log('🚫 Print Screen blocked');
        return false;
    }

    // Block Ctrl+Shift+R (some recording software)
    if (e.ctrlKey && e.shiftKey && e.keyCode === 82) {
        e.preventDefault();
        showRecordingWarning();
        console.log('🚫 Ctrl+Shift+R recording shortcut blocked');
        return false;
    }
});

// Screen recording warning function
function showRecordingWarning() {
    // Remove existing warning if any
    const existingWarning = document.getElementById('recording-warning');
    if (existingWarning) {
        existingWarning.remove();
    }

    // Create recording warning popup
    const warning = document.createElement('div');
    warning.id = 'recording-warning';
    warning.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #ff0000, #cc0000);
        color: white;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(255, 0, 0, 0.5);
        z-index: 20000;
        font-family: Arial, sans-serif;
        text-align: center;
        max-width: 400px;
        animation: slideIn 0.5s ease-out;
        border: 3px solid #ffffff;
    `;

    warning.innerHTML = `
        <div style="font-size: 48px; margin-bottom: 20px;">⚠️</div>
        <h2 style="margin-bottom: 15px; color: white;">Content Protection Notice</h2>
        <p style="margin-bottom: 20px; font-size: 16px;">
            Our system detected unusual activity that may indicate screen recording.
        </p>
        <p style="margin-bottom: 25px; font-size: 14px; opacity: 0.9;">
            If you're not recording, this might be a false positive. Please refresh to continue watching.
        </p>
        <div style="margin-bottom: 20px;">
            <button onclick="location.reload()" style="
                background: white;
                color: #ff0000;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-weight: bold;
                cursor: pointer;
                font-size: 16px;
                transition: all 0.3s ease;
                margin-right: 10px;
            " onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='white'">
                🔄 Refresh Page
            </button>
            <button onclick="document.getElementById('recording-warning').remove(); document.querySelector('.ratio-16x9').style.filter='none'; document.querySelector('.ratio-16x9').style.pointerEvents='auto';" style="
                background: transparent;
                color: white;
                border: 2px solid white;
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: bold;
                cursor: pointer;
                font-size: 14px;
                transition: all 0.3s ease;
            " onmouseover="this.style.background='rgba(255,255,255,0.1)'" onmouseout="this.style.background='transparent'">
                Continue Anyway
            </button>
        </div>
        <p style="font-size: 12px; opacity: 0.7; margin: 0;">
            Note: Recording copyrighted content may violate terms of service
        </p>
    `;

    document.body.appendChild(warning);

    // Also blur the video content
    const videoContainer = document.querySelector('.ratio-16x9');
    if (videoContainer) {
        videoContainer.style.filter = 'blur(20px)';
        videoContainer.style.pointerEvents = 'none';
    }

    // Auto-dismiss warning after 30 seconds if user doesn't interact
    setTimeout(() => {
        const warningElement = document.getElementById('recording-warning');
        if (warningElement) {
            console.log('⏰ Auto-dismissing recording warning after 30 seconds');
            warningElement.remove();
            if (videoContainer) {
                videoContainer.style.filter = 'none';
                videoContainer.style.pointerEvents = 'auto';
            }
            // Reset the warning flag so it can trigger again if needed
            recordingWarningShown = false;
        }
    }, 30000); // 30 seconds
}

// Method 5: Advanced recording detection via user behavior patterns (improved)
let suspiciousActivityScore = 0;
let lastActivityTime = Date.now();
let videoEndedRecently = false;

function monitorSuspiciousActivity() {
    // Monitor for patterns that indicate recording software
    setInterval(() => {
        const now = Date.now();
        const timeSinceLastActivity = now - lastActivityTime;

        // Don't trigger if video ended recently (user might be inactive after video ends)
        if (videoEndedRecently) {
            console.log('🎬 Video ended recently, skipping suspicious activity detection');
            return;
        }

        // If user is inactive for long periods but page is still focused (suspicious)
        // But only if it's been a while since video ended
        if (!document.hidden && timeSinceLastActivity > 60000) { // Increased from 30 seconds to 60 seconds
            suspiciousActivityScore += 1;
        }

        // Reset score if user is actively using the page
        if (timeSinceLastActivity < 10000) { // Increased from 5 seconds to 10 seconds
            suspiciousActivityScore = Math.max(0, suspiciousActivityScore - 1);
        }

        // Trigger warning only if score is very high and no recent video activity
        if (suspiciousActivityScore > 8 && !recordingWarningShown && !videoEndedRecently) { // Increased threshold from 5 to 8
            console.log('🚨 High suspicious activity score detected:', suspiciousActivityScore);
            showRecordingWarning();
            recordingWarningShown = true;
        }

        lastActivityTime = now;
    }, 15000); // Check every 15 seconds (increased from 10 seconds)
}

// Track user activity to reset suspicious activity
document.addEventListener('mousemove', () => {
    lastActivityTime = Date.now();
});

document.addEventListener('keydown', () => {
    lastActivityTime = Date.now();
});

document.addEventListener('click', () => {
    lastActivityTime = Date.now();
});

// Method 6: Detect recording software by checking for specific processes (limited browser capability)
function detectRecordingProcesses() {
    // This is a placeholder for more advanced detection
    // In a real implementation, you might use browser APIs to detect
    // performance changes that indicate recording software

    setInterval(() => {
        // Check for performance degradation that might indicate recording
        if (performance.memory) {
            const memoryUsage = performance.memory.usedJSHeapSize / performance.memory.totalJSHeapSize;

            // If memory usage is consistently high, might indicate recording
            if (memoryUsage > 0.9 && !recordingWarningShown) {
                suspiciousActivityScore += 2;
            }
        }
    }, 15000);
}

// Initialize all protection methods
document.addEventListener('DOMContentLoaded', function() {
    detectScreenRecording();
    monitorSuspiciousActivity();
    detectRecordingProcesses();

    console.log('🛡️ Advanced screen recording protection activated!');
    console.log('ℹ️ Alt+Tab and normal window switching will not trigger warnings');
});

console.log('🔒 Comprehensive security system with screen recording protection loaded and active!');
</script>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal)): ?>
<?php $attributes = $__attributesOriginal; ?>
<?php unset($__attributesOriginal); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal)): ?>
<?php $component = $__componentOriginal; ?>
<?php unset($__componentOriginal); ?>
<?php endif; ?>
<?php /**PATH D:\laravel\iec-courses-app\resources\views/course/purchased-lecture-detail.blade.php ENDPATH**/ ?>