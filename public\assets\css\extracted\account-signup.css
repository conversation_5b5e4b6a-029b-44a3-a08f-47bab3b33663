/* Account Signup Page Styles */

/* Background image with high specificity */
.oblique .oblique-image.signup-bg-image,
.position-absolute.oblique-image.signup-bg-image,
div.oblique-image.signup-bg-image {
    background-image: url('../../../assets/img/image-sign-up.jpg') !important;
    background-size: cover !important;
    background-position: center center !important;
    background-repeat: no-repeat !important;
    background-attachment: scroll !important;
}

/* Device badge */
.device-badge {
    max-width: 400px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Form icons */
.form-icon {
    color: #667eea;
}

/* Form inputs */
.signup-input {
    border-color: #e3e6f0;
    font-size: 1rem;
}

/* Password input with button */
.password-input {
    border-color: #e3e6f0;
    font-size: 1rem;
    padding-right: 45px;
    height: 50px;
}

/* Password toggle button */
.password-toggle-btn {
    border: none;
    background: transparent;
    z-index: 10;
    width: 40px;
    height: 40px;
}

/* Password eye icon */
.password-eye-icon {
    font-size: 1rem;
    color: #6c757d;
}

/* Terms link */
.terms-link {
    color: #667eea;
}

/* Captcha container */
.captcha-container {
    background: #f8f9fa;
    border: 2px solid #e3e6f0;
    border-radius: 8px;
    padding: 10px;
    font-family: monospace;
    font-size: 1.2rem;
    font-weight: bold;
    letter-spacing: 3px;
    color: #667eea;
    min-width: 120px;
    text-align: center;
}

/* Captcha input */
.captcha-input {
    border-color: #e3e6f0;
    font-size: 1rem;
    max-width: 150px;
}

/* Primary gradient button */
.btn-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    color: white !important;
}

.btn-gradient-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3) !important;
    color: white !important;
}

.btn-gradient-primary:focus,
.btn-gradient-primary:active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
}

/* Modal header */
.modal-header-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Device icon container */
.device-icon-container {
    width: 80px;
    height: 80px;
}

/* Device icon */
.device-icon {
    font-size: 2rem;
    color: #667eea;
}

/* Device type text */
.device-type-text {
    color: #667eea;
}

/* Security icon */
.security-icon {
    color: #667eea;
}

/* Additional form styles */
.form-input {
    border-color: #e3e6f0;
    font-size: 1rem;
}

.gradient-btn {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
    border: none;
    color: white !important;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 14px 0 rgba(79, 70, 229, 0.39);
    transition: all 0.3s ease;
}

.gradient-btn:hover {
    background: linear-gradient(135deg, #4338ca 0%, #6d28d9 50%, #db2777 100%);
    transform: translateY(-1px);
    box-shadow: 0 6px 20px 0 rgba(79, 70, 229, 0.5);
    color: white !important;
}

.gradient-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px 0 rgba(79, 70, 229, 0.3);
}

.security-text-icon {
    color: #667eea;
}

.modal-device-icon {
    font-size: 2rem;
    color: #667eea;
}

.modal-device-type {
    color: #667eea;
}

/* Captcha refresh button */
.captcha-refresh-btn {
    border: 1px solid #e3e6f0;
    background: #f8f9fa;
    color: #667eea;
}

.captcha-refresh-btn:hover {
    background: #e9ecef;
    border-color: #667eea;
}

/* Form validation styles */
.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Modal styles */
.modal-content {
    border-radius: 15px;
}

.modal-header {
    border-radius: 15px 15px 0 0;
}



/* Form field focus effects */
.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Terms checkbox styling */
.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

/* Alert styling */
.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

/* Card styling */
.card-plain {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: none;
    border-radius: 15px;
}

/* Background overlay for better readability */
.signup-bg-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
    z-index: 1;
}

.signup-bg-image > * {
    position: relative;
    z-index: 2;
}

/* Fallback purple gradient if image doesn't load */
.signup-bg-image {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}



/* Additional styling for the oblique image container */
.oblique-image.signup-bg-image {
    background-image: url('../../../assets/img/image-sign-up.jpg'), linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
}