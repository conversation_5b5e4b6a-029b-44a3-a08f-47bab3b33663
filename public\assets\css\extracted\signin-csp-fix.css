/* Sign-in Page CSP Fix Styles */

/* Fix for inline styles in the sign-in page */
.auth-bg-image {
    background-image: url('../../../assets/img/image-sign-in.jpg') !important;
    background-size: cover !important;
    background-position: center !important;
}

.blur {
    backdrop-filter: blur(5px) !important;
    background-color: rgba(255, 255, 255, 0.8) !important;
}

.position-absolute.w-40.top-0.end-0.h-100 {
    width: 40% !important;
}

.oblique-image.position-absolute.fixed-top.ms-auto.h-100.z-index-0.bg-cover.ms-n8 {
    margin-left: -8rem !important;
}

/* Fix for auth form elements */
.auth-input {
    border-color: #e3e6f0 !important;
    font-size: 1rem !important;
    height: 50px !important;
}

.auth-input-password {
    border-color: #e3e6f0 !important;
    font-size: 1rem !important;
    padding-right: 45px !important;
    height: 50px !important;
}

.auth-password-toggle {
    border: none !important;
    background: transparent !important;
    z-index: 10 !important;
    width: 40px !important;
    height: 40px !important;
}

/* Fix for captcha container */
.captcha-container {
    background: #f8f9fa !important;
    border: 2px solid #e3e6f0 !important;
    border-radius: 8px !important;
    padding: 10px !important;
    font-family: monospace !important;
    font-size: 1.2rem !important;
    font-weight: bold !important;
    letter-spacing: 3px !important;
    color: #4285f4 !important;
    min-width: 120px !important;
    text-align: center !important;
}

.auth-captcha-input {
    border-color: #e3e6f0 !important;
    font-size: 1rem !important;
    max-width: 150px !important;
    height: 50px !important;
}

/* Fix for auth buttons */
.auth-submit-btn {
    background: linear-gradient(135deg, #4285f4 0%, #1976d2 100%) !important;
    border: none !important;
}

.auth-signup-btn {
    background: linear-gradient(135deg, #4285f4 0%, #1976d2 100%) !important;
    border: none !important;
}

/* Fix for auth icons */
.auth-icon-primary {
    color: #4285f4 !important;
}

.auth-icon-secondary {
    color: #6c757d !important;
}

/* Fix for auth support link */
.auth-support-link {
    color: #667eea !important;
}