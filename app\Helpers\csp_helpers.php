<?php

use App\Services\ContentSecurityPolicy\NonceGenerator;

if (!function_exists('csp_nonce')) {
    /**
     * Get the CSP nonce value.
     *
     * @return string
     */
    function csp_nonce(): string
    {
        return NonceGenerator::getNonce();
    }
}

if (!function_exists('nonce_attr')) {
    /**
     * Get the CSP nonce attribute for HTML tags.
     *
     * @return string
     */
    function nonce_attr(): string
    {
        return NonceGenerator::getNonceAttribute();
    }
}

if (!function_exists('script_tag')) {
    /**
     * Generate a script tag with CSP nonce.
     *
     * @param string $src The script source URL
     * @param bool $defer Whether to defer script loading
     * @param bool $async Whether to load script asynchronously
     * @return string
     */
    function script_tag(string $src, bool $defer = false, bool $async = false): string
    {
        $attributes = [
            'src="' . $src . '"',
            nonce_attr(),
        ];

        if ($defer) {
            $attributes[] = 'defer';
        }

        if ($async) {
            $attributes[] = 'async';
        }

        return '<script ' . implode(' ', $attributes) . '></script>';
    }
}

if (!function_exists('style_tag')) {
    /**
     * Generate a link tag for stylesheets with CSP nonce.
     *
     * @param string $href The stylesheet URL
     * @return string
     */
    function style_tag(string $href): string
    {
        return '<link rel="stylesheet" href="' . $href . '" ' . nonce_attr() . '>';
    }
}