<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\UserLectureProgress;
use App\Models\Lecture;
use App\Models\Course;
use Illuminate\Support\Facades\Auth;

class LectureProgressController extends Controller
{
    /**
     * Update the user's progress for a specific lecture.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateProgress(Request $request)
    {
        try {
            // Validate the request
            $validated = $request->validate([
                'lecture_id' => 'required|exists:lectures,id',
                'current_time' => 'required|integer|min:0',
                'duration' => 'required|integer|min:0',
            ]);

            $user = Auth::user();
            $lectureId = $validated['lecture_id'];
            $currentTime = $validated['current_time'];
            $duration = $validated['duration'];

            // Log the incoming data for debugging
            \Log::info('Progress update request', [
                'user_id' => $user->id,
                'lecture_id' => $lectureId,
                'current_time' => $currentTime,
                'duration' => $duration
            ]);

            // Get the lecture to retrieve the course_id
            $lecture = Lecture::findOrFail($lectureId);
            $courseId = $lecture->course_id;

            // Find or create progress record
            $progress = UserLectureProgress::firstOrNew([
                'user_id' => $user->id,
                'lecture_id' => $lectureId,
            ]);

            // Set course_id if this is a new record
            if (!$progress->exists) {
                $progress->course_id = $courseId;
                \Log::info('Creating new progress record', [
                    'user_id' => $user->id,
                    'lecture_id' => $lectureId,
                    'course_id' => $courseId
                ]);
            }

            // Update progress data
            $progress->updateProgress($currentTime, $duration);
            $saved = $progress->save();

            \Log::info('Progress save result', [
                'saved' => $saved,
                'progress_id' => $progress->id,
                'progress_percent' => $progress->progress_percent
            ]);

            // Return updated progress data
            return response()->json([
                'success' => true,
                'progress' => [
                    'current_time' => $progress->current_time,
                    'duration' => $progress->duration,
                    'progress_percent' => $progress->progress_percent,
                    'completed' => $progress->completed,
                    'formatted_progress' => $progress->formatted_progress
                ],
                'lecture_completed' => $progress->completed,
                // Include course progress data
                'course_progress' => [
                    'percent' => $user->getCourseProgress($courseId),
                    'completed_lectures' => $lecture->course->getCompletedLectureCountForUser($user->id),
                    'total_lectures' => $lecture->course->total_lecture_count
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Error updating progress', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to update progress: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get the user's progress for a specific lecture.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProgress(Request $request)
    {
        // Validate the request
        $validated = $request->validate([
            'lecture_id' => 'required|exists:lectures,id',
        ]);

        $user = Auth::user();
        $lectureId = $validated['lecture_id'];

        // Get the lecture
        $lecture = Lecture::findOrFail($lectureId);
        $courseId = $lecture->course_id;

        // Find progress record (if exists)
        $progress = UserLectureProgress::where([
            'user_id' => $user->id,
            'lecture_id' => $lectureId,
        ])->first();

        if (!$progress) {
            return response()->json([
                'success' => true,
                'progress' => [
                    'current_time' => 0,
                    'duration' => 0,
                    'progress_percent' => 0,
                    'completed' => false,
                    'formatted_progress' => '0%'
                ],
                'lecture_completed' => false,
                'course_progress' => [
                    'percent' => $user->getCourseProgress($courseId),
                    'completed_lectures' => $lecture->course->getCompletedLectureCountForUser($user->id),
                    'total_lectures' => $lecture->course->total_lecture_count
                ]
            ]);
        }

        // Return progress data
        return response()->json([
            'success' => true,
            'progress' => [
                'current_time' => $progress->current_time,
                'duration' => $progress->duration,
                'progress_percent' => $progress->progress_percent,
                'completed' => $progress->completed,
                'formatted_progress' => $progress->formatted_progress
            ],
            'lecture_completed' => $progress->completed,
            'course_progress' => [
                'percent' => $user->getCourseProgress($courseId),
                'completed_lectures' => $lecture->course->getCompletedLectureCountForUser($user->id),
                'total_lectures' => $lecture->course->total_lecture_count
            ]
        ]);
    }

    /**
     * Mark a lecture as completed for the user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function markCompleted(Request $request)
    {
        // Validate the request
        $validated = $request->validate([
            'lecture_id' => 'required|exists:lectures,id',
        ]);

        $user = Auth::user();
        $lectureId = $validated['lecture_id'];

        // Get the lecture
        $lecture = Lecture::findOrFail($lectureId);
        $courseId = $lecture->course_id;

        // Find or create progress record
        $progress = UserLectureProgress::firstOrNew([
            'user_id' => $user->id,
            'lecture_id' => $lectureId,
        ]);

        // Set course_id if this is a new record
        if (!$progress->exists) {
            $progress->course_id = $courseId;
        }

        // Mark as completed
        $progress->progress_percent = 100;
        $progress->completed = true;
        $progress->last_watched_at = now();
        $progress->save();

        // Return updated progress data
        return response()->json([
            'success' => true,
            'message' => 'Lecture marked as completed',
            'course_progress' => [
                'percent' => $user->getCourseProgress($courseId),
                'completed_lectures' => $lecture->course->getCompletedLectureCountForUser($user->id),
                'total_lectures' => $lecture->course->total_lecture_count
            ]
        ]);
    }
}
