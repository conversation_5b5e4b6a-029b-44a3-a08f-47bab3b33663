<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Course;
use App\Models\Lecture;
use App\Models\User;
use App\Models\Role;
use App\Models\Coupon;
use App\Models\Order;
use App\Models\AdminUserAssignment;
use App\Models\Question;
use App\Models\AdminPermission;
use App\Models\Quiz;
use App\Models\Certificate;
use App\Models\CertificateRequest;
use App\Models\InstructorProfile;
use App\Models\QuizAnswer;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;


class AdminController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'check.role:Admin,Super Admin']);
    }

    /**
     * Admin dashboard
     */
    public function index()
    {
        $user = Auth::user();

        // If super admin, show all stats
        if ($user->isSuperAdmin()) {
            $stats = [
                'courses' => Course::count(),
                'lectures' => Lecture::count(),
                'users' => User::count(),
                'roles' => Role::count(),
                'coupons' => Coupon::count(),
                'pending_orders' => Order::where('status', 'pending')->count(),
                'completed_orders' => Order::where('status', 'completed')->count(),
                'rejected_orders' => Order::where('status', 'rejected')->count(),
                'total_revenue' => Order::where('status', 'completed')->sum('final_total'),
                'total_orders' => Order::where('status', 'completed')->count(),
                'orders' => Order::count(),
                'quizzes' => Quiz::count(),
                'certificate_requests' => CertificateRequest::count(),
                'pending_certificate_requests' => CertificateRequest::where('status', 'pending')->count(),
                'approved_certificate_requests' => CertificateRequest::where('status', 'approved')->count(),
                'certificates' => Certificate::count(),
                'instructor_profiles' => \App\Models\InstructorProfile::count(),
                'pending_quiz_reviews' => QuizAnswer::where('review_status', 'pending_review')->count(),
                'total_quiz_answers' => QuizAnswer::count(),
            ];
        } else {
            // Regular admin only sees stats from their assigned users
            $assignedUserIds = AdminUserAssignment::where('admin_id', $user->id)
                ->pluck('user_id')
                ->toArray();

            // Count pending, answered, and rejected questions from assigned users
            $pendingQuestions = Question::whereIn('user_id', $assignedUserIds)
                ->where('status', 'pending')
                ->count();

            $answeredQuestions = Question::whereIn('user_id', $assignedUserIds)
                ->where('status', 'answered')
                ->count();

            $rejectedQuestions = Question::whereIn('user_id', $assignedUserIds)
                ->where('status', 'rejected')
                ->count();

            // Count pending quiz reviews from assigned users
            $pendingQuizReviews = QuizAnswer::where('review_status', 'pending_review')
                ->whereHas('attempt', function($query) use ($assignedUserIds) {
                    $query->whereIn('user_id', $assignedUserIds);
                })
                ->count();

            $stats = [
                'assigned_users' => count($assignedUserIds),
                'pending_questions' => $pendingQuestions,
                'answered_questions' => $answeredQuestions,
                'rejected_questions' => $rejectedQuestions,
                'total_questions' => $pendingQuestions + $answeredQuestions + $rejectedQuestions,
                'pending_quiz_reviews' => $pendingQuizReviews,
            ];
        }

        return view('admin.dashboard', compact('stats'));
    }

    /**
     * Manage courses
     */
    public function courses()
    {
        $courses = Course::with('category')->paginate(10);
        return view('admin.courses.index', compact('courses'));
    }

    /**
     * Manage lectures
     */
    public function lectures()
    {
        $lectures = Lecture::with('course')->paginate(10);
        return view('admin.lectures.index', compact('lectures'));
    }

    /**
     * Show lecture details
     */
    public function showLecture(Lecture $lecture)
    {
        $lecture->load(['course', 'materials']);
        return view('admin.lectures.show', compact('lecture'));
    }

    /**
     * Show course details
     */
    public function showCourse(Course $course)
    {
        $course->load(['lectures', 'category']);
        return view('admin.courses.show', compact('course'));
    }

    /**
     * Manage roles
     */
    public function roles()
    {
        $roles = Role::paginate(10);
        return view('admin.roles.index', compact('roles'));
    }

    /**
     * Manage users
     */
    public function users()
    {
        $users = User::with('roles')->paginate(10);
        return view('admin.users.index', compact('users'));
    }

    /**
     * Verify a user (email and phone)
     * Only accessible by Super Admin
     */
    public function verifyUser(User $user)
    {
        // Check if current user is Super Admin
        if (!auth()->user()->isSuperAdmin()) {
            return redirect()->back()->with('error', 'Only Super Admins can verify users.');
        }

        // Update user verification status
        $user->update([
            'email_verified_at' => now(),
            'phone_verified_at' => now()
        ]);

        return redirect()->back()->with('success', "User '{$user->name}' has been verified successfully!");
    }

    /**
     * Manage coupons
     */
    public function coupons()
    {
        return view('admin.coupons.index');
    }
    /**
     * Manage orders
     */
    public function orders()
    {
        $orders = Order::with('user')->orderBy('created_at', 'desc')->paginate(10);
        return view('admin.orders.index', compact('orders'));
    }

    /**
     * Manage quizzes
     */
    public function quizzes()
    {
        $user = Auth::user();

        // If super admin, show all quizzes
        if ($user->isSuperAdmin()) {
            $quizzes = Quiz::with(['lecture.course'])->paginate(10);
        } else {
            // Regular admin only sees stats from their assigned users
            $assignedUserIds = AdminUserAssignment::where('admin_id', $user->id)
                ->pluck('user_id')
                ->toArray();

            // Show quizzes that are related to courses with assigned users
            $quizzes = Quiz::whereHas('lecture.course.users', function($query) use ($assignedUserIds) {
                $query->whereIn('users.id', $assignedUserIds);
            })->with(['lecture.course'])->paginate(10);
        }

        return view('admin.quizzes.index', compact('quizzes'));
    }

    /**
     * Manage instructor profiles
     */
    public function instructorProfiles()
    {
        $instructorProfiles = InstructorProfile::orderBy('name')->paginate(10);
        return view('admin.instructor-profiles.index', compact('instructorProfiles'));
    }

    /**
     * Show instructor profile form for creating a new profile
     */
    public function createInstructorProfile()
    {
        return view('admin.instructor-profiles.create');
    }

    /**
     * Store a new instructor profile
     */
    public function storeInstructorProfile(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'bio' => 'nullable|string',
            'title' => 'nullable|string|max:255',
            'expertise' => 'nullable|string',
            'skills' => 'nullable|string',
            'social_linkedin' => 'nullable|url',
            'social_twitter' => 'nullable|url',
            'social_website' => 'nullable|url',
            'image_path' => 'nullable|image|max:2048',
            'is_active' => 'boolean',
        ]);

        // Handle image upload
        if ($request->hasFile('image_path')) {
            $validatedData['image_path'] = $request->file('image_path')->store('instructors', 'public');
        }

        InstructorProfile::create($validatedData);

        return redirect()->route('admin.instructor-profiles')
            ->with('success', 'Instructor profile created successfully.');
    }

    /**
     * Show instructor profile form for editing
     */
    public function editInstructorProfile(InstructorProfile $instructorProfile)
    {
        return view('admin.instructor-profiles.edit', compact('instructorProfile'));
    }

    /**
     * Update instructor profile
     */
    public function updateInstructorProfile(Request $request, InstructorProfile $instructorProfile)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'bio' => 'nullable|string',
            'title' => 'nullable|string|max:255',
            'expertise' => 'nullable|string',
            'skills' => 'nullable|string',
            'social_linkedin' => 'nullable|url',
            'social_twitter' => 'nullable|url',
            'social_website' => 'nullable|url',
            'image_path' => 'nullable|image|max:2048',
            'is_active' => 'boolean',
        ]);

        // Handle image upload
        if ($request->hasFile('image_path')) {
            // Delete old image if exists
            if ($instructorProfile->image_path && Storage::disk('public')->exists($instructorProfile->image_path)) {
                Storage::disk('public')->delete($instructorProfile->image_path);
            }
            $validatedData['image_path'] = $request->file('image_path')->store('instructors', 'public');
        }

        $instructorProfile->update($validatedData);

        return redirect()->route('admin.instructor-profiles')
            ->with('success', 'Instructor profile updated successfully.');
    }

    /**
     * Delete instructor profile
     */
    public function destroyInstructorProfile(InstructorProfile $instructorProfile)
    {
        // Delete image if exists
        if ($instructorProfile->image_path && Storage::disk('public')->exists($instructorProfile->image_path)) {
            Storage::disk('public')->delete($instructorProfile->image_path);
        }

        $instructorProfile->delete();

        return redirect()->route('admin.instructor-profiles')
            ->with('success', 'Instructor profile deleted successfully.');
    }

    /**
     * Manage admin permissions
     */
    public function permissions()
    {
        $user = Auth::user();

        // Make sure only Super Admins can access this page
        if (!$user->isSuperAdmin()) {
            return redirect()->route('admin.dashboard')
                ->with('error', 'Only Super Admins can manage permissions.');
        }

        // Get all admin users except super admins
        $adminRoleId = Role::where('name', 'Admin')->first()->id;
        $admins = User::whereHas('roles', function($query) use ($adminRoleId) {
            $query->where('role_id', $adminRoleId);
        })->get();

        // Available pages for permission management
        $availablePages = [
            'courses' => 'Courses Management',
            'lectures' => 'Lectures Management',
            'users' => 'Users Management',
            'coupons' => 'Coupons Management',
            'orders' => 'Orders Management',
            'questions' => 'Questions Management',
            'roles' => 'Roles Management',
            'payment_methods' => 'Payment Methods Management',
            'quizzes' => 'Quizzes Management',
            'instructor_profiles' => 'Instructor Profiles Management',
        ];

        return view('admin.permissions.index', compact('admins', 'availablePages'));
    }

    /**
     * Update admin permissions
     */
    public function updatePermissions(Request $request)
    {
        $user = Auth::user();

        // Make sure only Super Admins can update permissions
        if (!$user->isSuperAdmin()) {
            return redirect()->route('admin.dashboard')
                ->with('error', 'Only Super Admins can manage permissions.');
        }

        // Log the incoming request data for debugging
        \Log::info('Permission Update Request', [
            'admin_id' => $request->admin_id,
            'permissions' => $request->permissions,
            'all_data' => $request->all()
        ]);

        $request->validate([
            'admin_id' => 'required|exists:users,id',
            'permissions' => 'required|array',
            'permissions.*' => 'boolean',
        ]);

        $adminId = $request->admin_id;

        // Make sure the target user is an admin (not a super admin)
        $user = User::find($adminId);
        if ($user->isSuperAdmin()) {
            return redirect()->back()->with('error', 'Cannot modify permissions for Super Admin.');
        }

        // Update permissions
        foreach ($request->permissions as $page => $isAllowed) {
            // Cast to boolean to ensure proper value is stored
            $isAllowed = ($isAllowed == '1' || $isAllowed === true || $isAllowed === 'true') ? true : false;

            // Log each permission update
            \Log::info('Updating permission', [
                'admin_id' => $adminId,
                'page' => $page,
                'is_allowed' => $isAllowed,
                'raw_value' => $request->permissions[$page]
            ]);

            AdminPermission::updateOrCreate(
                ['admin_user_id' => $adminId, 'page' => $page],
                ['is_allowed' => $isAllowed]
            );
        }

        // Log all permissions after update
        $updatedPermissions = AdminPermission::where('admin_user_id', $adminId)->get();
        \Log::info('Updated permissions', [
            'admin_id' => $adminId,
            'permissions' => $updatedPermissions->toArray()
        ]);

        return redirect()->back()->with('success', 'Admin permissions updated successfully.');
    }

    /**
     * Debug user roles and permissions
     */
    public function debugUser()
    {
        $user = Auth::user();
        $data = [
            'user_id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'is_super_admin' => $user->isSuperAdmin(),
            'is_admin' => $user->isAdmin(),
            'roles' => $user->roles()->pluck('name')->toArray(),
            'permissions' => $user->permissions()->get()->toArray()
        ];

        return response()->json($data);
    }
}
