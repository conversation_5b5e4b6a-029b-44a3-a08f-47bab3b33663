<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube CSP Test - Laravel App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .video-container {
            margin: 20px 0;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 8px;
        }
        .status-indicator {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="mb-4">YouTube CSP Compliance Test</h1>
        
        <div class="status-indicator info">
            <strong>Environment:</strong> {{ app()->environment() }}<br>
            <strong>CSP Status:</strong> {{ app()->environment('local') ? 'Disabled (Local Development)' : 'Enabled (Production)' }}
        </div>

        <div class="video-container">
            <h3>Test 1: Standard YouTube Embed</h3>
            <p class="text-muted">Testing regular YouTube iframe embed</p>
            <div class="ratio ratio-16x9">
                <iframe 
                    src="https://www.youtube.com/embed/dQw4w9WgXcQ?rel=0&modestbranding=1&showinfo=0" 
                    frameborder="0" 
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" 
                    allowfullscreen>
                </iframe>
            </div>
        </div>

        <div class="video-container">
            <h3>Test 2: YouTube Nocookie Embed</h3>
            <p class="text-muted">Testing privacy-enhanced YouTube embed</p>
            <div class="ratio ratio-16x9">
                <iframe 
                    src="https://www.youtube-nocookie.com/embed/dQw4w9WgXcQ?rel=0&modestbranding=1&showinfo=0" 
                    frameborder="0" 
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" 
                    allowfullscreen>
                </iframe>
            </div>
        </div>

        <div class="video-container">
            <h3>Test 3: Your App's YouTube Implementation</h3>
            <p class="text-muted">Testing the same format used in your lecture pages</p>
            <div class="ratio ratio-16x9">
                <div class="youtube-container w-100 h-100">
                    <iframe 
                        id="youtube-iframe-test"
                        src="https://www.youtube.com/embed/dQw4w9WgXcQ?rel=0&modestbranding=1&showinfo=0&enablejsapi=1" 
                        frameborder="0" 
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" 
                        allowfullscreen
                        class="w-100 h-100"
                        style="border: none;">
                    </iframe>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <h3>Test Results</h3>
            <div id="test-results">
                <div class="status-indicator info">
                    <strong>Status:</strong> Running tests...
                </div>
            </div>
        </div>

        <div class="mt-4">
            <h3>Console Errors</h3>
            <div id="console-errors">
                <div class="status-indicator info">
                    Monitoring for console errors...
                </div>
            </div>
        </div>

        <div class="mt-4">
            <h3>CSP Violations</h3>
            <div id="csp-violations">
                <div class="status-indicator info">
                    Monitoring for CSP violations...
                </div>
            </div>
        </div>
    </div>

    <script>
        // Monitor console errors
        const originalError = console.error;
        const errorContainer = document.getElementById('console-errors');
        let errorCount = 0;

        console.error = function(...args) {
            errorCount++;
            const errorDiv = document.createElement('div');
            errorDiv.className = 'status-indicator error';
            errorDiv.innerHTML = `<strong>Console Error ${errorCount}:</strong> ${args.join(' ')}`;
            errorContainer.appendChild(errorDiv);
            originalError.apply(console, args);
        };

        // Monitor CSP violations
        const cspContainer = document.getElementById('csp-violations');
        let cspViolationCount = 0;

        document.addEventListener('securitypolicyviolation', function(e) {
            cspViolationCount++;
            const violationDiv = document.createElement('div');
            violationDiv.className = 'status-indicator error';
            violationDiv.innerHTML = `
                <strong>CSP Violation ${cspViolationCount}:</strong><br>
                <strong>Directive:</strong> ${e.violatedDirective}<br>
                <strong>Blocked URI:</strong> ${e.blockedURI}<br>
                <strong>Source:</strong> ${e.sourceFile}:${e.lineNumber}
            `;
            cspContainer.appendChild(violationDiv);
        });

        // Test iframe loading
        setTimeout(() => {
            const iframes = document.querySelectorAll('iframe');
            const resultsContainer = document.getElementById('test-results');
            let loadedCount = 0;
            let totalIframes = iframes.length;

            iframes.forEach((iframe, index) => {
                iframe.onload = function() {
                    loadedCount++;
                    console.log(`Iframe ${index + 1} loaded successfully`);
                };
                
                iframe.onerror = function() {
                    console.error(`Iframe ${index + 1} failed to load`);
                };
            });

            // Final results after 5 seconds
            setTimeout(() => {
                let resultClass = 'success';
                let resultText = '';

                if (errorCount === 0 && cspViolationCount === 0) {
                    resultText = `✅ All tests passed! ${loadedCount}/${totalIframes} iframes loaded successfully.`;
                } else {
                    resultClass = 'error';
                    resultText = `❌ Tests failed. Console errors: ${errorCount}, CSP violations: ${cspViolationCount}`;
                }

                resultsContainer.innerHTML = `<div class="status-indicator ${resultClass}">${resultText}</div>`;

                if (errorCount === 0) {
                    errorContainer.innerHTML = '<div class="status-indicator success">✅ No console errors detected!</div>';
                }

                if (cspViolationCount === 0) {
                    cspContainer.innerHTML = '<div class="status-indicator success">✅ No CSP violations detected!</div>';
                }
            }, 5000);
        }, 1000);
    </script>
</body>
</html>
