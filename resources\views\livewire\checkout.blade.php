<div>

        <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">


            <style>
                .checkout-container {
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 2rem;
                }

                .payment-method {
                    border: 1px solid #e9ecef;
                    border-radius: 0.5rem;
                    padding: 1rem;
                    margin-bottom: 1rem;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }

                .payment-method:hover {
                    border-color: #0d6efd;
                    background-color: #f8f9fa;
                }

                .payment-method.selected {
                    border-color: #0d6efd;
                    background-color: #e7f1ff;
                }

                .payment-icon {
                    width: 40px;
                    height: 40px;
                    object-fit: contain;
                }

                .form-control:focus {
                    border-color: #0d6efd;
                    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
                }

                .order-summary {
                    background-color: #f8f9fa;
                    border-radius: 0.5rem;
                    padding: 1.5rem;
                }

                .order-item {
                    border-bottom: 1px solid #e9ecef;
                    padding-bottom: 1rem;
                    margin-bottom: 1rem;
                }

                .security-badge {
                    background-color: #e7f1ff;
                    border-radius: 0.5rem;
                    padding: 1rem;
                    margin-top: 1rem;
                }
            </style>

            <div class="checkout-container">
                <div class="row">
                    <!-- Order Summary -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Order Summary</h5>
                            </div>
                            <div class="card-body">
                                <div class="order-summary">
                                    @foreach($cartItems as $item)
                                        <div class="order-item">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-0">{{ $item['name'] }}</h6>
                                                    <small class="text-muted">{{ $item['type'] }}</small>
                                                    @if(isset($item['discount_amount']) && $item['discount_amount'] > 0)
                                                        <span class="badge bg-success ms-2">Discounted</span>
                                                    @endif
                                                </div>
                                                <div class="text-end">
                                                    @if(isset($item['discount_amount']) && $item['discount_amount'] > 0)
                                                        <p class="mb-0 text-decoration-line-through text-muted small">${{ number_format($item['original_price'], 2) }}</p>
                                                        <p class="text-success mb-0 small">-${{ number_format($item['discount_amount'], 2) }} discount</p>
                                                    @endif
                                                    <p class="mb-0">${{ number_format($item['price'], 2) }}</p>
                                                    <small class="text-muted">Qty: {{ $item['quantity'] }}</small>
                                                </div>
                                            </div>
                                            @if(isset($item['discount_reason']) && $item['discount_reason'])
                                                <div class="mt-2">
                                                    <div class="alert alert-success py-1 px-2 mb-0 cart-discount-alert">
                                                        {{ $item['discount_reason'] }}
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    @endforeach
                                    <div class="d-flex justify-content-between align-items-center mt-3">
                                        <h5 class="mb-0">Total</h5>
                                        <h5 class="mb-0">${{ number_format($total, 2) }}</h5>
                                    </div>
                                    @if($discount > 0)
                                        <div class="d-flex justify-content-between align-items-center mt-2">
                                            <span class="text-success">Discount</span>
                                            <span class="text-success">-${{ number_format($discount, 2) }}</span>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center mt-2">
                                            <h5 class="mb-0">Final Total</h5>
                                            <h5 class="mb-0">${{ number_format($total - $discount, 2) }}</h5>
                                        </div>
                                    @endif
                                    <div class="mt-3">
                                        <div class="input-group">
                                            <input type="text" class="form-control" wire:model="couponCode" placeholder="Enter coupon code">
                                            <button class="btn btn-outline-primary" wire:click="applyCoupon" wire:loading.attr="disabled">
                                                <span wire:loading.remove>Apply</span>
                                                <span wire:loading>Applying...</span>
                                            </button>
                                        </div>
                                        @if($couponError)
                                            <div class="text-danger mt-2">{{ $couponError }}</div>
                                        @endif
                                        @if($couponSuccess)
                                            <div class="text-success mt-2">{{ $couponSuccess }}</div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Checkout Form -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Checkout Information</h5>
                            </div>
                            <div class="card-body">
                                <form wire:submit.prevent="createOrder">
                                    <!-- User Information -->
                                    <div class="mb-4">
                                        <h6 class="mb-3">Contact Information</h6>
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">First Name</label>
                                                <input type="text" class="form-control" wire:model="firstName" required>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Last Name</label>
                                                <input type="text" class="form-control" wire:model="lastName" required>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Email</label>
                                            <input type="email" class="form-control" wire:model="email" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Phone Number</label>
                                            <input type="tel" class="form-control" wire:model="phone" required>
                                        </div>
                                    </div>

                                    <!-- Billing Address -->
                                    <div class="mb-4">
                                        <h6 class="mb-3">Address</h6>

                                        <div class="row">
                                            <div class="col-md-4 mb-3">
                                                <label class="form-label">Country</label>
                                                <input type="text" class="form-control" wire:model="country" id="country" required>
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <label class="form-label">State/Province</label>
                                                <input type="text" class="form-control" wire:model="state" id="state" required>
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <label class="form-label">City</label>
                                                <input type="text" class="form-control" wire:model="city" id="city" required>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Postal Code</label>
                                                <input type="text" class="form-control" wire:model="postalCode" id="postal-code" required>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Street Address</label>
                                                <input type="text" class="form-control" wire:model="address" required>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Payment Methods -->
                                    <div class="mb-4">
                                        <h6 class="mb-3">Payment Method</h6>
                                        <div class="row">
                                            @foreach($paymentMethods as $method)
                                            <div class="col-md-6 mb-3">
                                                <div class="payment-method {{ $paymentMethod === $method->key ? 'selected' : '' }}" wire:click="$set('paymentMethod', '{{ $method->key }}')">
                                                    <div class="d-flex align-items-center">
                                                        @if(Str::startsWith($method->icon, 'fas '))
                                                            <i class="{{ $method->icon }} payment-icon me-3 {{ $method->details['color'] ?? '' }}"></i>
                                                        @else
                                                            <img src="{{ asset($method->icon) }}" alt="{{ $method->name }}" class="payment-icon me-3">
                                                        @endif
                                                        <div>
                                                            <h6 class="mb-0">{{ $method->name }}</h6>
                                                            <small class="text-muted">{{ $method->description }}</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            @endforeach
                                        </div>

                                        @if($paymentMethod)
                                            @php
                                                $selectedMethod = $paymentMethods->firstWhere('key', $paymentMethod);
                                            @endphp

                                            @if($selectedMethod)
                                                <div class="mt-3">
                                                    <div class="alert alert-info">
                                                        <h6 class="mb-2"><i class="fas fa-info-circle me-2"></i>Payment Instructions</h6>
                                                        <p class="mb-0">{!! nl2br(e($selectedMethod->instructions)) !!}</p>
                                                    </div>
                                                </div>

                                                @if($paymentMethod !== 'card')
                                                    <div class="mt-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="confirmPayment" wire:model.live="confirmPayment" required>
                                                            <label class="form-check-label fw-bold" for="confirmPayment">
                                                                I confirm that I will pay using the {{ $selectedMethod->name }} payment method
                                                            </label>
                                                        </div>
                                                    </div>
                                                @endif
                                            @endif
                                        @endif

                                    </div>

                                    <!-- Security Badge -->
                                    <div class="security-badge">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-lock text-primary me-2"></i>
                                            <div>
                                                <h6 class="mb-0">Secure Checkout</h6>
                                                <small class="text-muted">Your information is encrypted and secure</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Submit Button -->
                                    <div class="mt-4">
                                        <button type="submit" class="btn btn-primary w-100" wire:loading.attr="disabled" {{ ($paymentMethod === 'card') ? '' : (!$confirmPayment ? 'disabled' : '') }}>
                                            <span wire:loading.remove>Complete Purchase</span>
                                            <span wire:loading>Processing...</span>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </main>

</div>

@push('scripts')
<!-- Google Maps API for address autocomplete -->
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCsjWtO62BuIs1D5uBDjJRbcJmNhdOUZoo&libraries=places"></script>
<script>
    // Initialize Google Places autocomplete when the DOM is fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        initializeGooglePlacesAutocomplete();
    });

    // Also initialize when Livewire updates the DOM
    document.addEventListener('livewire:navigated', function() {
        initializeGooglePlacesAutocomplete();
    });

    // Additional initialization for Livewire
    window.addEventListener('livewire:load', function() {
        initializeGooglePlacesAutocomplete();
    });

    function initializeGooglePlacesAutocomplete() {
        const addressInput = document.getElementById('google-address-input');
        if (!addressInput) {
            console.log('Address input field not found');
            return;
        }

        console.log('Initializing Google Places Autocomplete');

        // Initialize Google Places Autocomplete
        const autocomplete = new google.maps.places.Autocomplete(addressInput, {
            types: ['address'],
            fields: ['address_components', 'formatted_address']
        });

        autocomplete.addListener('place_changed', function() {
            const place = autocomplete.getPlace();
            if (!place || !place.address_components) {
                console.log('No place details available');
                return;
            }

            console.log('Place selected:', place);

            // Extract address components
            let addressComponents = {
                street_number: '',
                route: '',
                locality: '',         // city
                administrative_area_level_1: '', // state
                country: '',
                postal_code: ''
            };

            for (const component of place.address_components) {
                const componentType = component.types[0];
                console.log('Component:', componentType, component.long_name);
                if (componentType in addressComponents) {
                    addressComponents[componentType] = component.long_name;
                }
            }

            // Set values on form
            const formattedAddress = `${addressComponents.street_number} ${addressComponents.route}`.trim();
            document.getElementById('formatted-address').value = formattedAddress;
            document.getElementById('city').value = addressComponents.locality;
            document.getElementById('state').value = addressComponents.administrative_area_level_1;
            document.getElementById('country').value = addressComponents.country;
            document.getElementById('postal-code').value = addressComponents.postal_code;

            console.log('Setting address:', formattedAddress);
            console.log('Setting city:', addressComponents.locality);
            console.log('Setting state:', addressComponents.administrative_area_level_1);
            console.log('Setting country:', addressComponents.country);
            console.log('Setting postal code:', addressComponents.postal_code);

            // Update Livewire component with values
            if (window.Livewire) {
                window.Livewire.dispatch('address-selected', {
                    address: formattedAddress,
                    city: addressComponents.locality,
                    state: addressComponents.administrative_area_level_1,
                    country: addressComponents.country,
                    postalCode: addressComponents.postal_code
                });
            }

            // Also trigger input events to update Livewire model
            ['formatted-address', 'city', 'state', 'country', 'postal-code'].forEach(id => {
                const el = document.getElementById(id);
                if (el) {
                    el.dispatchEvent(new Event('input', { bubbles: true }));
                }
            });
        });
    }
</script>
@endpush
