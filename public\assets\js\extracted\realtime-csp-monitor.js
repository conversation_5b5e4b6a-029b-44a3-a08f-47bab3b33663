/**
 * Real-time CSP Monitor and Fixer
 * This script monitors the DOM in real-time and fixes CSP violations as they occur
 */

(function() {
    'use strict';
    
    // Get CSP nonce
    const nonceMetaTag = document.querySelector('meta[name="csp-nonce"]');
    const nonce = nonceMetaTag ? nonceMetaTag.getAttribute('content') : null;
    
    if (!nonce) {
        console.warn('CSP nonce not found');
        return;
    }
    
    // Counter for unique class names
    let classCounter = 0;
    
    // Function to create a unique class name
    function getUniqueClassName() {
        return 'csp-fix-' + Date.now() + '-' + (++classCounter);
    }
    
    // Function to create a style element with nonce
    function createNoncedStyle(css) {
        const styleElement = document.createElement('style');
        styleElement.setAttribute('nonce', nonce);
        styleElement.textContent = css;
        document.head.appendChild(styleElement);
        return styleElement;
    }
    
    // Function to fix inline styles
    function fixInlineStyle(element) {
        if (!element.hasAttribute('style')) return;
        
        const inlineStyle = element.getAttribute('style');
        if (!inlineStyle.trim()) return;
        
        // Remove the inline style
        element.removeAttribute('style');
        
        // Create a unique class
        const uniqueClass = getUniqueClassName();
        element.classList.add(uniqueClass);
        
        // Create a style element with the nonce
        createNoncedStyle('.' + uniqueClass + ' { ' + inlineStyle + ' }');
        
        console.log('Fixed inline style:', inlineStyle);
    }
    
    // Function to fix inline event handlers
    function fixInlineEventHandler(element, eventType) {
        const handlerAttr = 'on' + eventType;
        if (!element.hasAttribute(handlerAttr)) return;
        
        const handlerCode = element.getAttribute(handlerAttr);
        if (!handlerCode.trim()) return;
        
        // Remove the inline handler
        element.removeAttribute(handlerAttr);
        
        // Add proper event listener
        element.addEventListener(eventType, function(event) {
            try {
                const func = new Function('event', handlerCode);
                func.call(this, event);
            } catch (error) {
                console.error('Error executing ' + handlerAttr + ' handler:', error);
            }
        });
        
        console.log('Fixed inline event handler:', handlerAttr, handlerCode);
    }
    
    // Function to fix inline scripts
    function fixInlineScript(script) {
        if (script.hasAttribute('src') || script.hasAttribute('nonce')) return;
        
        // Add nonce to inline scripts
        script.setAttribute('nonce', nonce);
        console.log('Added nonce to inline script');
    }
    
    // Function to fix all CSP violations in an element
    function fixElement(element) {
        if (element.nodeType !== Node.ELEMENT_NODE) return;
        
        // Fix inline styles
        fixInlineStyle(element);
        
        // Fix inline event handlers
        const eventTypes = ['click', 'change', 'submit', 'load', 'error', 'mouseover', 'mouseout', 'focus', 'blur'];
        eventTypes.forEach(eventType => {
            fixInlineEventHandler(element, eventType);
        });
        
        // Fix inline scripts
        if (element.tagName === 'SCRIPT') {
            fixInlineScript(element);
        }
        
        // Fix child elements
        const children = element.querySelectorAll('*');
        children.forEach(child => {
            fixInlineStyle(child);
            eventTypes.forEach(eventType => {
                fixInlineEventHandler(child, eventType);
            });
            if (child.tagName === 'SCRIPT') {
                fixInlineScript(child);
            }
        });
    }
    
    // Function to scan and fix the entire document
    function scanAndFixDocument() {
        // Fix all elements with inline styles
        const elementsWithStyle = document.querySelectorAll('[style]');
        elementsWithStyle.forEach(fixInlineStyle);
        
        // Fix all elements with inline event handlers
        const eventTypes = ['click', 'change', 'submit', 'load', 'error', 'mouseover', 'mouseout', 'focus', 'blur'];
        eventTypes.forEach(eventType => {
            const elements = document.querySelectorAll('[on' + eventType + ']');
            elements.forEach(element => {
                fixInlineEventHandler(element, eventType);
            });
        });
        
        // Fix all inline scripts without nonce
        const inlineScripts = document.querySelectorAll('script:not([src]):not([nonce])');
        inlineScripts.forEach(fixInlineScript);
        
        // Fix Livewire styles
        const livewireStyles = document.querySelectorAll('style:not([nonce])');
        livewireStyles.forEach(style => {
            if (!style.hasAttribute('nonce')) {
                style.setAttribute('nonce', nonce);
                console.log('Added nonce to Livewire style');
            }
        });
    }
    
    // Initial scan when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', scanAndFixDocument);
    } else {
        scanAndFixDocument();
    }
    
    // Set up MutationObserver to catch dynamically added content
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        fixElement(node);
                    }
                });
            } else if (mutation.type === 'attributes') {
                if (mutation.attributeName === 'style' || mutation.attributeName.startsWith('on')) {
                    fixElement(mutation.target);
                }
            }
        });
    });
    
    // Start observing
    observer.observe(document.documentElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'onclick', 'onchange', 'onsubmit', 'onload', 'onerror', 'onmouseover', 'onmouseout', 'onfocus', 'onblur']
    });
    
    // Periodic scan to catch anything missed
    setInterval(scanAndFixDocument, 500);
    
    // Override common methods that might add inline styles
    const originalSetAttribute = Element.prototype.setAttribute;
    Element.prototype.setAttribute = function(name, value) {
        if (name === 'style' && value) {
            // Instead of setting inline style, create a class
            const uniqueClass = getUniqueClassName();
            this.classList.add(uniqueClass);
            createNoncedStyle('.' + uniqueClass + ' { ' + value + ' }');
            console.log('Intercepted setAttribute for style:', value);
            return;
        }
        return originalSetAttribute.call(this, name, value);
    };
    
    // Override style property setter
    const originalStyleDescriptor = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'style');
    if (originalStyleDescriptor && originalStyleDescriptor.set) {
        Object.defineProperty(HTMLElement.prototype, 'style', {
            set: function(value) {
                if (typeof value === 'string' && value) {
                    const uniqueClass = getUniqueClassName();
                    this.classList.add(uniqueClass);
                    createNoncedStyle('.' + uniqueClass + ' { ' + value + ' }');
                    console.log('Intercepted style property setter:', value);
                    return;
                }
                return originalStyleDescriptor.set.call(this, value);
            },
            get: originalStyleDescriptor.get
        });
    }
    
    console.log('Real-time CSP monitor initialized');
    
})();