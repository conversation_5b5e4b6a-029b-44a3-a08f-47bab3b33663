/**
 * Livewire CSP Patch
 * This script provides a comprehensive fix for Livewire CSP issues
 */

(function() {
    // Execute immediately to patch before DOM content loaded
    // Get CSP nonce from meta tag
    const nonceMetaTag = document.querySelector('meta[name="csp-nonce"]');
    const nonce = nonceMetaTag ? nonceMetaTag.getAttribute('content') : null;
    
    if (!nonce) {
        console.warn('CSP nonce not found in meta tag');
        return;
    }
    
    // Override createElement to add nonce to style elements
    const originalCreateElement = document.createElement;
    document.createElement = function(tagName) {
        const element = originalCreateElement.apply(document, arguments);
        
        if (tagName.toLowerCase() === 'style') {
            element.setAttribute('nonce', nonce);
        }
        
        return element;
    };
    
    // Function to add nonce to all style elements
    function addNonceToAllStyles() {
        document.querySelectorAll('style:not([nonce])').forEach(style => {
            style.setAttribute('nonce', nonce);
        });
    }
    
    // Run immediately
    addNonceToAllStyles();
    
    // Set up a MutationObserver to watch for new style elements
    const observer = new MutationObserver(mutations => {
        let needsUpdate = false;
        
        mutations.forEach(mutation => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) {
                        // Check if the node is a style element
                        if (node.tagName === 'STYLE' && !node.hasAttribute('nonce')) {
                            node.setAttribute('nonce', nonce);
                            needsUpdate = true;
                        }
                        
                        // Check for style elements within the added node
                        if (node.querySelectorAll) {
                            const styles = node.querySelectorAll('style:not([nonce])');
                            if (styles.length > 0) {
                                styles.forEach(style => style.setAttribute('nonce', nonce));
                                needsUpdate = true;
                            }
                        }
                    }
                });
            }
        });
        
        if (needsUpdate) {
            // Double check for any missed styles
            addNonceToAllStyles();
        }
    });
    
    // Start observing the document with all possible mutation types
    observer.observe(document.documentElement, {
        childList: true,
        subtree: true,
        attributes: true,
        characterData: true
    });
    
    // Run again when DOM is fully loaded
    document.addEventListener('DOMContentLoaded', addNonceToAllStyles);
    
    // Special handling for Livewire
    if (typeof window.Livewire !== 'undefined') {
        console.log('Livewire detected, applying CSP patches');
        
        // Patch Livewire's style injection
        const originalLivewireStyles = window.Livewire.styles;
        window.Livewire.styles = function() {
            const styles = originalLivewireStyles ? originalLivewireStyles() : '';
            if (styles) {
                const styleEl = document.createElement('style');
                styleEl.textContent = styles;
                document.head.appendChild(styleEl);
                return '';
            }
            return styles;
        };
    }
    
    // Add event listeners for Livewire events
    document.addEventListener('livewire:load', addNonceToAllStyles);
    document.addEventListener('livewire:update', addNonceToAllStyles);
    
    // Run periodically to catch any missed styles
    setInterval(addNonceToAllStyles, 1000);
})();