<div>
<div class="container">
    <h1>Manage Courses & Lectures</h1>

    {{-- Manage Courses --}}
    <div class="card mb-5">
        <div class="card-header">Manage Courses</div>
        <div class="card-body">
            <form wire:submit.prevent="saveCourse">
                <input type="hidden" wire:model="courseId">

                {{-- Course Name --}}
                <div class="mb-3">
                    <label for="courseName" class="form-label">Course Name</label>
                    <input type="text" id="courseName" wire:model="courseName" class="form-control" placeholder="Enter course name">
                </div>

                {{-- Course Instructor --}}
                <div class="mb-3">
                    <label for="courseInstructor" class="form-label">Instructor</label>
                    <select id="courseInstructor" wire:model="courseInstructor" class="form-control">
                        <option value="">Select an instructor</option>
                        @foreach ($instructors as $instructor)
                            <option value="{{ $instructor['name'] }}">{{ $instructor['name'] }} ({{ $instructor['title'] }})</option>
                        @endforeach
                    </select>
                </div>

                {{-- Course Description --}}
                <div class="mb-3">
                    <label for="courseDescription" class="form-label">Course Description</label>
                    <textarea id="courseDescription" wire:model.debounce.500ms="courseDescription" class="form-control editor"></textarea>
                </div>

                {{-- Course Weekly Price --}}
                <div class="mb-3">
                    <label for="courseWeeklyPrice" class="form-label">Weekly Price</label>
                    <input type="number" step="0.01" id="courseWeeklyPrice" wire:model="courseWeeklyPrice" class="form-control" placeholder="Enter weekly price">
                </div>

                {{-- Course Monthly Price --}}
                <div class="mb-3">
                    <label for="courseMonthlyPrice" class="form-label">Monthly Price</label>
                    <input type="number" step="0.01" id="courseMonthlyPrice" wire:model="courseMonthlyPrice" class="form-control" placeholder="Enter monthly price">
                </div>

                {{-- Course Image Path --}}
                {{-- Course Image Path --}}
<div class="mb-3">
    <label for="courseImagePath" class="form-label">Upload Image</label>
    <input type="file" id="courseImagePath" wire:model="courseImagePath" class="form-control">
    @error('courseImagePath') <span class="text-danger">{{ $message }}</span> @enderror
    <div wire:loading wire:target="courseImagePath">Uploading...</div>
</div>

                {{-- Course Intro Video URL --}}
                <div class="mb-3">
                    <label for="courseIntroVideoUrl" class="form-label">Introduction Video URL (YouTube)</label>
                    <input type="url" id="courseIntroVideoUrl" wire:model="courseIntroVideoUrl" class="form-control" placeholder="https://www.youtube.com/watch?v=example">
                    <small class="text-muted">Enter a YouTube URL for the course introduction video</small>
                </div>

                <button type="submit" class="btn btn-primary">{{ $isEditMode ? 'Update Course' : 'Add Course' }}</button>
            </form>

            @if($isEditMode && $courseId)
                {{-- Course Features Management --}}
                <div class="mt-4 pt-3 border-top">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4>Course Features</h4>
                        <button type="button" class="btn btn-sm btn-primary" wire:click.prevent="openCourseFeatureModal">
                            <i class="fas fa-plus"></i> Add Feature
                        </button>
                    </div>

                    @if(count($courseFeatures) > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Feature Type</th>
                                        <th>Feature Text</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($courseFeatures as $feature)
                                        <tr>
                                            <td>
                                                <span class="badge bg-{{ $feature['feature_type'] === 'learn' ? 'primary' : ($feature['feature_type'] === 'requirement' ? 'info' : 'success') }}">
                                                    {{ ucfirst($feature['feature_type']) }}
                                                </span>
                                            </td>
                                            <td>{{ $feature['feature_text'] }}</td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-warning" wire:click.prevent="editCourseFeature({{ $feature['id'] }})" wire:key="edit-course-feature-{{ $feature['id'] }}">
                                                    <i class="fas fa-edit"></i> Edit
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" wire:click.prevent="deleteCourseFeature({{ $feature['id'] }})" onclick="return confirm('Are you sure you want to delete this feature?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">No features added to this course yet.</div>
                    @endif
                </div>
            @endif

            {{-- List Courses --}}
            <h3 class="mt-4">Course List</h3>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($courses as $course)
                        <tr>
                            <td>{{ $course->name }}</td>
                            <td>
                                <button wire:click="editCourse({{ $course->id }})" class="btn btn-sm btn-warning">Edit</button>
                                <button wire:click="deleteCourse({{ $course->id }})" class="btn btn-sm btn-danger">Delete</button>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="2">No courses available.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    {{-- Manage Lectures --}}
    <div class="card">
        <div class="card-header">Manage Lectures</div>
        <div class="card-body">
            <form wire:submit.prevent="saveLecture">
                {{-- Select Course --}}
                <div class="mb-3">
                    <label for="selectedCourse" class="form-label">Select Course</label>
                    <select id="selectedCourse" wire:model="selectedCourse" class="form-control">
                        <option value="">Select a course</option>
                        @foreach ($courses as $course)
                            <option value="{{ $course->id }}">{{ $course->name }}</option>
                        @endforeach
                    </select>
                </div>

                {{-- Lecture Name --}}
                <div class="mb-3">
                    <label for="lectureName" class="form-label">Lecture Name</label>
                    <input type="text" id="lectureName" wire:model="lectureName" class="form-control" placeholder="Enter lecture name">
                </div>

                {{-- Lecture Instructor --}}
                <div class="mb-3">
                    <label for="lectureInstructor" class="form-label">Instructor</label>
                    <select id="lectureInstructor" wire:model="lectureInstructor" class="form-control">
                        <option value="">Select an instructor</option>
                        @foreach ($instructors as $instructor)
                            <option value="{{ $instructor['name'] }}">{{ $instructor['name'] }} ({{ $instructor['title'] }})</option>
                        @endforeach
                    </select>
                    <small class="text-muted">Leave empty to use course instructor</small>
                </div>

                {{-- Lecture Description --}}
                <div class="mb-3">
                    <label for="lectureDescription" class="form-label">Lecture Description</label>
                    <textarea id="lectureDescription" wire:model.debounce.500ms="lectureDescription" class="form-control editor"></textarea>
                </div>

                {{-- Lecture Intro Video URL --}}
                <div class="mb-3">
                    <label for="lectureIntroVideoUrl" class="form-label">Introduction Video URL (YouTube)</label>
                    <input type="url" id="lectureIntroVideoUrl" wire:model="lectureIntroVideoUrl" class="form-control" placeholder="https://www.youtube.com/watch?v=example">
                    <small class="text-muted">Enter a YouTube URL for the lecture introduction video (different from the main lecture content)</small>
                </div>

                {{-- Lecture Weekly Price --}}
                <div class="mb-3">
                    <label for="lectureWeeklyPrice" class="form-label">Weekly Price</label>
                    <input type="number" step="0.01" id="lectureWeeklyPrice" wire:model="lectureWeeklyPrice" class="form-control" placeholder="Enter weekly price">
                </div>

                {{-- Lecture Monthly Price --}}
                <div class="mb-3">
                    <label for="lectureMonthlyPrice" class="form-label">Monthly Price</label>
                    <input type="number" step="0.01" id="lectureMonthlyPrice" wire:model="lectureMonthlyPrice" class="form-control" placeholder="Enter monthly price">
                </div>

                {{-- Lecture YouTube URL --}}
                <div class="mb-3">
                    <label for="lectureYoutubeUrl" class="form-label">YouTube URL</label>
                    <div class="input-group">
                        <input type="text" id="lectureYoutubeUrl" wire:model.lazy="lectureYoutubeUrl" class="form-control" placeholder="Enter YouTube URL">
                        <button type="button" class="btn btn-outline-secondary" wire:click="fetchYoutubeDuration" wire:loading.attr="disabled">
                            <span wire:loading.remove wire:target="fetchYoutubeDuration">
                                <i class="fas fa-sync"></i> Get Duration
                            </span>
                            <span wire:loading wire:target="fetchYoutubeDuration">
                                <i class="fas fa-spinner fa-spin"></i> Loading...
                            </span>
                        </button>
                    </div>
                    @if($isDurationLoading)
                        <div class="mt-2 text-info">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <small class="ms-2">Fetching video duration...</small>
                        </div>
                    @elseif($durationError)
                        <div class="mt-2 text-danger">
                            <small><i class="fas fa-exclamation-circle"></i> {{ $durationError }}</small>
                        </div>
                    @elseif($lectureDuration)
                        <div class="mt-2 text-success">
                            <small><i class="fas fa-check-circle"></i> Auto-detected Duration: <span class="font-weight-bold">{{ $lectureDuration }}</span></small>
                        </div>
                    @endif
                </div>

                {{-- Lecture Duration - Always visible --}}
                <div class="mb-3">
                    <label for="lectureDuration" class="form-label">Video Duration (HH:MM:SS)</label>
                    <input type="text" id="lectureDuration" wire:model="lectureDuration" class="form-control" placeholder="Enter duration (e.g., 00:15:30)">
                    @error('lectureDuration') <span class="text-danger">{{ $message }}</span> @enderror
                    <small class="text-muted">This will be auto-filled when a valid YouTube URL is entered, but you can modify it manually if needed.</small>
                </div>

                {{-- Lecture Image Path --}}
                <div class="mb-3">
    <label for="lectureImagePath" class="form-label">Upload Image</label>
    <input type="file" id="lectureImagePath" wire:model="lectureImagePath" class="form-control">
    @error('lectureImagePath') <span class="text-danger">{{ $message }}</span> @enderror
    <div wire:loading wire:target="lectureImagePath">Uploading...</div>
</div>

                {{-- Lecture PDF File --}}
                <div class="mb-3">
                    <label for="lecturePdfFile" class="form-label">Upload PDF File</label>
                    <input type="file" id="lecturePdfFile" wire:model="lecturePdfFile" class="form-control" accept=".pdf">
                    @error('lecturePdfFile') <span class="text-danger">{{ $message }}</span> @enderror
                    <div wire:loading wire:target="lecturePdfFile">Uploading...</div>
                    @if($isEditMode && $lectureId)
                        @php
                            $lecture = App\Models\Lecture::find($lectureId);
                        @endphp
                        @if($lecture && $lecture->pdf_file_path)
                            <div class="mt-2">
                                <span class="text-success">Current PDF: </span>
                                <a href="{{ asset('storage/' . $lecture->pdf_file_path) }}" target="_blank">View PDF</a>
                            </div>
                        @endif
                    @endif
                </div>

                <button type="submit" class="btn btn-primary">{{ $isEditMode ? 'Update Lecture' : 'Add Lecture' }}</button>
            </form>

            @if($isEditMode && $lectureId)
                {{-- Lecture Features Management --}}
                <div class="mt-4 pt-3 border-top">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4>Lecture Features</h4>
                        <button type="button" class="btn btn-sm btn-primary" wire:click.prevent="openLectureFeatureModal">
                            <i class="fas fa-plus"></i> Add Feature
                        </button>
                    </div>

                    @if(count($lectureFeatures) > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Feature Type</th>
                                        <th>Feature Text</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($lectureFeatures as $feature)
                                        <tr>
                                            <td>
                                                <span class="badge bg-{{ $feature['feature_type'] === 'learn' ? 'primary' : 'info' }}">
                                                    {{ ucfirst($feature['feature_type']) }}
                                                </span>
                                            </td>
                                            <td>{{ $feature['feature_text'] }}</td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-warning" wire:click.prevent="editLectureFeature({{ $feature['id'] }})" wire:key="edit-lecture-feature-{{ $feature['id'] }}">
                                                    <i class="fas fa-edit"></i> Edit
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" wire:click.prevent="deleteLectureFeature({{ $feature['id'] }})" onclick="return confirm('Are you sure you want to delete this feature?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">No features added to this lecture yet.</div>
                    @endif
                </div>
            @endif

            {{-- List Lectures --}}
            <h3 class="mt-4">Lecture List</h3>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($lectures as $lecture)
                        <tr>
                            <td>{{ $lecture->name }}</td>
                            <td>
                                <button wire:click="editLecture({{ $lecture->id }})" class="btn btn-sm btn-warning">Edit</button>
                                <button wire:click="deleteLecture({{ $lecture->id }})" class="btn btn-sm btn-danger">Delete</button>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>

{{-- Course Features Modal --}}
<div class="modal fade" id="courseFeatureModal" tabindex="-1" aria-hidden="true" wire:ignore.self>
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ $courseFeatureId ? 'Edit' : 'Add' }} Course Feature</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form wire:submit="saveCourseFeature">
                    <div class="mb-3">
                        <label for="courseFeatureType" class="form-label">Feature Type</label>
                        <select id="courseFeatureType" wire:model="courseFeatureType" class="form-control">
                            <option value="learn">What You'll Learn</option>
                            <option value="requirement">Requirements</option>
                            <option value="includes">This Course Includes</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="courseFeatureText" class="form-label">Feature Text</label>
                        <textarea id="courseFeatureText" wire:model="courseFeatureText" class="form-control" rows="3" placeholder="Enter feature text"></textarea>
                        @error('courseFeatureText') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>
                    {{-- Course Feature Modal Buttons --}}
                    <div class="d-flex justify-content-end">
                        <button type="button" class="btn btn-secondary me-2" wire:click="closeCourseFeatureModal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{{-- Lecture Features Modal --}}
<div class="modal fade" id="lectureFeatureModal" tabindex="-1" aria-hidden="true" wire:ignore.self>
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ $lectureFeatureId ? 'Edit' : 'Add' }} Lecture Feature</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form wire:submit="saveLectureFeature">
                    <div class="mb-3">
                        <label for="lectureFeatureType" class="form-label">Feature Type</label>
                        <select id="lectureFeatureType" wire:model="lectureFeatureType" class="form-control">
                            <option value="learn">What You'll Learn</option>
                            <option value="requirement">Requirements</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="lectureFeatureText" class="form-label">Feature Text</label>
                        <textarea id="lectureFeatureText" wire:model="lectureFeatureText" class="form-control" rows="3" placeholder="Enter feature text"></textarea>
                        @error('lectureFeatureText') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>
                    {{-- Lecture Feature Modal Buttons --}}
                    <div class="d-flex justify-content-end">
                        <button type="button" class="btn btn-secondary me-2" wire:click="closeLectureFeatureModal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{{-- JavaScript for Modal Initialization --}}
<script>
    document.addEventListener('livewire:initialized', () => {
        // Initialize all modals
        const courseFeatureModal = new bootstrap.Modal(document.getElementById('courseFeatureModal'));
        const lectureFeatureModal = new bootstrap.Modal(document.getElementById('lectureFeatureModal'));

        // Listen for Livewire events to open/close modals
        Livewire.on('openCourseFeatureModal', () => {
            console.log('Opening course feature modal'); // Debug log
            courseFeatureModal.show();
        });

        Livewire.on('closeCourseFeatureModal', () => {
            console.log('Closing course feature modal'); // Debug log
            courseFeatureModal.hide();
        });

        Livewire.on('openLectureFeatureModal', () => {
            console.log('Opening lecture feature modal'); // Debug log
            lectureFeatureModal.show();
        });

        Livewire.on('closeLectureFeatureModal', () => {
            console.log('Closing lecture feature modal'); // Debug log
            lectureFeatureModal.hide();
        });
    });
</script>
</div>

