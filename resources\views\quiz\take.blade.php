@section('styles')
    <link rel="stylesheet" href="{{ asset('css/quiz-components.css') }}">
@endsection

<x-app-layout>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-lg-10 mx-auto">
                <!-- Breadcrumb -->
                <nav aria-label="breadcrumb" class="mb-3">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('user.dashboard') }}">My Courses</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('user.course.purchased', $course->id) }}">{{ $course->name }}</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('user.lecture.purchased', ['course' => $course->id, 'lecture' => $lecture->id]) }}">{{ $lecture->name }}</a></li>
                        <li class="breadcrumb-item active">Quiz: {{ $quiz->title }}</li>
                    </ol>
                </nav>

                <!-- Quiz Header -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="mb-0">{{ $quiz->title }}</h4>
                            @if($quiz->time_limit)
                                <div class="quiz-timer" data-time-remaining="{{ $attempt->time_remaining ?? 0 }}">
                                    <span class="badge bg-warning text-dark p-2">
                                        <i class="fas fa-clock me-2"></i> <span id="timer-display">--:--</span>
                                    </span>
                                </div>
                            @endif
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="quiz-info mb-4">
                            @if($quiz->description)
                                <div class="mb-3">
                                    <h5>Instructions</h5>
                                    <p>{{ $quiz->description }}</p>
                                </div>
                            @endif
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-2">
                                        <strong>Total Points:</strong> {{ $quiz->total_points }}
                                    </div>
                                    <div class="mb-2">
                                        <strong>Passing Score:</strong> {{ $quiz->passing_score }}%
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    @if($quiz->time_limit)
                                        <div class="mb-2">
                                            <strong>Time Limit:</strong> {{ $quiz->time_limit }} minutes
                                        </div>
                                    @endif
                                    <div class="mb-2">
                                        <strong>Questions:</strong> {{ $quiz->questions->count() }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <!-- Progress Bar -->
                        @php
                            $totalQuestions = $quiz->questions->count();
                            $answeredCount = count($answeredQuestionIds);
                            $progressPercent = $totalQuestions > 0 ? ($answeredCount / $totalQuestions * 100) : 0;
                        @endphp
                        <div class="progress-container mb-4">
                            <div class="d-flex justify-content-between mb-1">
                                <span>Progress</span>
                                <span id="progress-text">{{ $answeredCount }}/{{ $totalQuestions }} questions</span>
                            </div>
                            <div class="progress progress-small">
                                <div class="progress-bar bg-success dynamic-progress-bar" id="progress-bar" role="progressbar" data-width="{{ $progressPercent }}"
                                    aria-valuenow="{{ $progressPercent }}" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Questions -->
                <div id="questions-container">
                    @foreach($quiz->questions as $index => $question)
                        <div class="question-card card shadow-sm mb-4" id="question-{{ $question->id }}" data-question-id="{{ $question->id }}">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Question {{ $index + 1 }} <small class="text-muted">({{ $question->points }} points)</small></h5>
                            </div>
                            <div class="card-body">
                                <div class="question-text mb-3">
                                    <p class="fw-bold">{{ $question->question_text }}</p>
                                </div>

                                <div class="question-answers">
                                    @if($question->isMultipleChoice())
                                        <!-- Multiple Choice Question -->
                                        <div class="multiple-choice-options">
                                            @foreach($question->options as $option)
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="radio" name="question-{{ $question->id }}"
                                                        id="option-{{ $option->id }}" value="{{ $option->id }}"
                                                        @if(in_array($question->id, $answeredQuestionIds)) disabled @endif>
                                                    <label class="form-check-label" for="option-{{ $option->id }}">
                                                        {{ $option->option_text }}
                                                    </label>
                                                </div>
                                            @endforeach
                                        </div>
                                    @else
                                        <!-- Open Ended Question -->
                                        <div class="open-ended-answer">
                                            <textarea class="form-control" id="answer-text-{{ $question->id }}" rows="3"
                                                placeholder="Type your answer here..." @if(in_array($question->id, $answeredQuestionIds)) disabled @endif></textarea>
                                        </div>
                                    @endif
                                </div>

                                <div class="d-flex justify-content-between mt-4">
                                    <div class="answer-status" id="answer-status-{{ $question->id }}">
                                        @if(in_array($question->id, $answeredQuestionIds))
                                            <span class="badge bg-success">Answered</span>
                                        @endif
                                    </div>
                                    <button type="button" class="btn btn-primary submit-answer"
                                        data-question-id="{{ $question->id }}"
                                        data-question-type="{{ $question->question_type }}"
                                        data-attempt-id="{{ $attempt->id }}"
                                        @if(in_array($question->id, $answeredQuestionIds)) disabled @endif>
                                        <i class="fas fa-check me-1"></i> Submit Answer
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Complete Quiz Button -->
                <div class="card shadow-sm mb-4">
                    <div class="card-body d-flex justify-content-between align-items-center">
                        <a href="{{ route('user.lecture.purchased', ['course' => $course->id, 'lecture' => $lecture->id]) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> Back to Lecture
                        </a>
                        <form action="{{ route('quiz.complete', $attempt->id) }}" method="POST" id="complete-form">
                            @csrf
                            <button type="submit" class="btn btn-success" id="complete-quiz">
                                <i class="fas fa-flag-checkered me-1"></i> Finish Quiz
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize timer if time limit exists
            if (document.querySelector('.quiz-timer')) {
                initializeTimer();
            }

            // Handle answer submissions
            document.querySelectorAll('.submit-answer').forEach(button => {
                button.addEventListener('click', function() {
                    submitAnswer(this);
                });
            });

            // Confirm before completing quiz
            document.getElementById('complete-form').addEventListener('submit', function(e) {
                if (!confirm('Are you sure you want to finish this quiz? All unanswered questions will be marked as incorrect.')) {
                    e.preventDefault();
                }
            });

            // Function to initialize timer
            function initializeTimer() {
                const timerElement = document.querySelector('.quiz-timer');
                let timeRemaining = parseInt(timerElement.dataset.timeRemaining);
                const timerDisplay = document.getElementById('timer-display');

                // Update timer every second
                const timerInterval = setInterval(function() {
                    timeRemaining--;

                    if (timeRemaining <= 0) {
                        clearInterval(timerInterval);
                        document.getElementById('complete-form').submit();
                        return;
                    }

                    // Calculate minutes and seconds
                    const minutes = Math.floor(timeRemaining / 60);
                    const seconds = timeRemaining % 60;

                    // Display timer with leading zeros if needed
                    timerDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

                    // Add visual indication when time is running out
                    if (timeRemaining < 60) {
                        timerDisplay.parentElement.classList.remove('bg-warning');
                        timerDisplay.parentElement.classList.add('bg-danger', 'text-white');
                    }
                }, 1000);
            }

            // Function to submit an answer
            function submitAnswer(button) {
                const questionId = button.dataset.questionId;
                const questionType = button.dataset.questionType;
                const attemptId = button.dataset.attemptId;
                let answer;

                // Disable the button while submitting
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Submitting...';

                // Get answer based on question type
                if (questionType === 'multiple_choice') {
                    const selectedOption = document.querySelector(`input[name="question-${questionId}"]:checked`);
                    if (!selectedOption) {
                        alert('Please select an answer.');
                        button.disabled = false;
                        button.innerHTML = '<i class="fas fa-check me-1"></i> Submit Answer';
                        return;
                    }
                    answer = selectedOption.value;
                } else {
                    const answerText = document.getElementById(`answer-text-${questionId}`).value.trim();
                    if (!answerText) {
                        alert('Please enter your answer.');
                        button.disabled = false;
                        button.innerHTML = '<i class="fas fa-check me-1"></i> Submit Answer';
                        return;
                    }
                    answer = answerText;
                }

                // Get CSRF token
                const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                // Submit answer via AJAX
                fetch(`/quiz/attempts/${attemptId}/submit-answer`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': token,
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        question_id: questionId,
                        question_type: questionType,
                        option_id: questionType === 'multiple_choice' ? answer : null,
                        answer_text: questionType === 'open_ended' ? answer : null
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update question status based on answer type
                        const statusElement = document.getElementById(`answer-status-${questionId}`);

                        if (data.is_correct === null) {
                            // Pending review for text answers
                            statusElement.innerHTML = '<span class="badge bg-warning">Pending Review</span>';
                        } else {
                            // Auto-graded answers
                            statusElement.innerHTML = '<span class="badge bg-success">Answered</span>';
                        }

                        // Disable the question inputs
                        if (questionType === 'multiple_choice') {
                            document.querySelectorAll(`input[name="question-${questionId}"]`).forEach(input => {
                                input.disabled = true;
                            });
                        } else {
                            document.getElementById(`answer-text-${questionId}`).disabled = true;
                        }

                        // Update progress bar
                        updateProgressBar();

                        // Reset button state
                        button.disabled = false;
                        button.innerHTML = '<i class="fas fa-check me-1"></i> Submit Answer';

                        // If all questions are answered, suggest completing the quiz
                        if (data.is_complete) {
                            alert('All questions have been answered. You can now finish the quiz.');
                        }
                    } else {
                        alert('Error: ' + data.message);
                        button.disabled = false;
                        button.innerHTML = '<i class="fas fa-check me-1"></i> Submit Answer';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while submitting your answer.');
                    button.disabled = false;
                    button.innerHTML = '<i class="fas fa-check me-1"></i> Submit Answer';
                });
            }

            // Function to update progress bar
            function updateProgressBar() {
                const totalQuestions = {{ $totalQuestions }};
                const answeredElements = document.querySelectorAll('.answer-status .badge-success').length;
                const progressPercent = (answeredElements / totalQuestions) * 100;

                document.getElementById('progress-bar').style.width = progressPercent + '%';
                document.getElementById('progress-bar').setAttribute('aria-valuenow', progressPercent);
                document.getElementById('progress-text').textContent = `${answeredElements}/${totalQuestions} questions`;
            }
        });
    </script>
    @endpush
</x-app-layout>
