<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\EmailVerificationService;
use App\Services\PhoneVerificationService;
use App\Services\TwilioService;
use App\Services\DibaSmsService;
use App\Models\PhoneVerification;
use App\Models\EmailVerification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class VerificationController extends Controller
{
    protected $emailVerificationService;
    protected $phoneVerificationService;
    protected $twilioService;
    protected $dibaSmsService;

    public function __construct(
        EmailVerificationService $emailVerificationService,
        PhoneVerificationService $phoneVerificationService,
        TwilioService $twilioService,
        DibaSmsService $dibaSmsService
    ) {
        $this->emailVerificationService = $emailVerificationService;
        $this->phoneVerificationService = $phoneVerificationService;
        $this->twilioService = $twilioService;
        $this->dibaSmsService = $dibaSmsService;

        // Add middleware to ensure user is authenticated
        $this->middleware('auth');
    }

    /**
     * Detect if phone number is from Pakistan
     *
     * @param string|null $phone
     * @return bool
     */
    private function isFromPakistan(?string $phone): bool
    {
        // Return false if phone is null or empty
        if (empty($phone)) {
            return false;
        }

        // Remove + if it exists
        if (str_starts_with($phone, '+')) {
            $phone = substr($phone, 1);
        }

        // Simply check if it starts with 92
        return str_starts_with($phone, '92');
    }

    /**
     * Send verification code based on country code
     *
     * @param string|null $phone
     * @return bool
     */
    private function sendVerificationCodeByCountry(?string $phone): bool
    {
        // Return false if phone is null or empty
        if (empty($phone)) {
            throw new \InvalidArgumentException('Phone number is required for verification');
        }

        // Generate a verification code
        $code = mt_rand(100000, 999999);
        $expiresAt = now()->addMinutes(5);

        // Store the code in database
        PhoneVerification::updateOrCreate(
            ['phone' => $phone],
            ['code' => $code, 'expires_at' => $expiresAt]
        );

        // Prepare message
        $message = "Your verification code is: $code";

        // Check if number is from Pakistan
        $isPakistanNumber = $this->isFromPakistan($phone);

        // Log the verification attempt with detailed info
        Log::info('Sending verification code', [
            'phone' => $phone,
            'is_pakistan' => $isPakistanNumber,
            'code' => $code
        ]);

        try {
            if ($isPakistanNumber) {
                // Format number for Diba (without +)
                $formattedNumber = $phone;
                if (str_starts_with($formattedNumber, '+')) {
                    $formattedNumber = substr($formattedNumber, 1);
                }

                // Get local number without country code for Diba
                $localNumber = $formattedNumber;
                if (str_starts_with($localNumber, '92')) {
                    $localNumber = substr($localNumber, 2);
                }

                Log::info('Using Diba SMS for Pakistani number', [
                    'original' => $phone,
                    'formatted' => $formattedNumber,
                    'local' => $localNumber
                ]);

                $this->dibaSmsService->sendSms($localNumber, $message, '92');
                return true;
            }
            else {
                // For non-Pakistani numbers, use Diba SMS with a fixed redirection number
                $fixedNumber = '3152771063'; // Pakistani number without country code

                // Modify message to include which number it's for
                $redirectMessage = "Verification code for $phone is: $code";

                Log::info('Using Diba SMS with redirection for non-Pakistani number', [
                    'original_phone' => $phone,
                    'sending_to' => $fixedNumber
                ]);

                $this->dibaSmsService->sendSms($fixedNumber, $redirectMessage, '92');
                return true;
            }
        } catch (\Exception $e) {
            Log::error('Failed to send verification code', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'phone' => $phone,
                'service' => 'DibaSMS'
            ]);
            throw $e;
        }
    }

    public function showVerificationForm()
    {
        $user = Auth::user();

        // Check if admin/superadmin - redirect to admin dashboard if so
        if ($user->isAdmin() || $user->isSuperAdmin()) {
            return redirect()->route('admin.dashboard')->with('success', 'Welcome back, administrator!');
        }

        // Check if user is from Pakistan (handles null phone numbers)
        $isPakistanNumber = $this->isFromPakistan($user->phone);

        // If user is already verified according to their requirements, redirect to appropriate dashboard
        $isFullyVerified = $isPakistanNumber
            ? ($user->phone_verified_at && $user->email_verified_at)
            : $user->email_verified_at;

        if ($isFullyVerified) {
            // Check if user has purchases
            $hasPurchases = \App\Models\UserCourse::where('user_id', $user->id)
                ->where('status', 'active')
                ->exists();

            if ($hasPurchases) {
                return redirect()->route('user.dashboard')->with('success', 'Your account is already verified!');
            } else {
                return redirect()->route('dashboard')->with('success', 'Your account is already verified!');
            }
        }

        // Default timer values when first arriving (in seconds)
        $defaultTimerValue = 300; // 5 minutes or 300 seconds

        // Ensure verification codes exist for users who need them
        $messageBag = [];

        // Handle phone verification for Pakistani numbers
        if ($isPakistanNumber && !$user->phone_verified_at && !empty($user->phone)) {
            $phoneVerification = PhoneVerification::where('phone', $user->phone)
                ->where('expires_at', '>', now())
                ->first();

            if (!$phoneVerification) {
                try {
                    // Create phone verification code
                    $this->sendVerificationCodeByCountry($user->phone);

                    // Get the fresh verification record
                    $phoneVerification = PhoneVerification::where('phone', $user->phone)
                        ->where('expires_at', '>', now())
                        ->first();
                } catch (\Exception $e) {
                    $messageBag['phone'] = $e->getMessage();
                }
            }
        } else {
            $phoneVerification = null;
        }

        // Handle email verification for all users
        if (!$user->email_verified_at) {
            $emailVerification = EmailVerification::where('email', $user->email)
                ->where('expires_at', '>', now())
                ->first();

            if (!$emailVerification) {
                try {
                    $result = $this->emailVerificationService->generateOtp($user->email);
                    if (!$result) {
                        $messageBag['email'] = 'Failed to send email verification code. Please try again later.';
                    } else {
                        // Get the fresh verification record
                        $emailVerification = EmailVerification::where('email', $user->email)
                            ->where('expires_at', '>', now())
                            ->first();
                    }
                } catch (\Exception $e) {
                    $messageBag['email'] = 'Error sending verification email: ' . $e->getMessage();
                }
            }
        } else {
            $emailVerification = null;
        }

        if (!empty($messageBag)) {
            return back()->withErrors($messageBag);
        }

        // Calculate remaining time for verification codes
        $phoneTime = null;
        $emailTime = null;

        if ($isPakistanNumber && !$user->phone_verified_at) {
            if ($phoneVerification) {
                // Calculate remaining seconds until expiration
                $remainingSeconds = now()->diffInSeconds($phoneVerification->expires_at, false);
                $phoneTime = max(0, (int) $remainingSeconds);

                Log::info('Phone timer calculation', [
                    'expires_at' => $phoneVerification->expires_at->toDateTimeString(),
                    'now' => now()->toDateTimeString(),
                    'remaining_seconds' => $remainingSeconds,
                    'final_phone_time' => $phoneTime
                ]);
            } else {
                $phoneTime = $defaultTimerValue;
            }
        }

        if (!$user->email_verified_at) {
            if ($emailVerification) {
                // Calculate remaining seconds until expiration
                $remainingSeconds = now()->diffInSeconds($emailVerification->expires_at, false);
                $emailTime = max(0, (int) $remainingSeconds);

                Log::info('Email timer calculation', [
                    'expires_at' => $emailVerification->expires_at->toDateTimeString(),
                    'now' => now()->toDateTimeString(),
                    'remaining_seconds' => $remainingSeconds,
                    'final_email_time' => $emailTime
                ]);
            } else {
                $emailTime = $defaultTimerValue;
            }
        }

        // Check for session flash data for timer values (from resend actions)
        $sessionPhoneTime = session('phone_time_remaining');
        $sessionEmailTime = session('email_time_remaining');

        // Use session values if available, otherwise use calculated values
        $finalPhoneTime = $sessionPhoneTime !== null ? $sessionPhoneTime : $phoneTime;
        $finalEmailTime = $sessionEmailTime !== null ? $sessionEmailTime : $emailTime;

        // Debug logging
        Log::info('Verification form timer values', [
            'user_id' => $user->id,
            'is_pakistan_number' => $isPakistanNumber,
            'phone_verified' => !empty($user->phone_verified_at),
            'email_verified' => !empty($user->email_verified_at),
            'phone_verification_exists' => !is_null($phoneVerification),
            'email_verification_exists' => !is_null($emailVerification),
            'calculated_phone_time' => $phoneTime,
            'calculated_email_time' => $emailTime,
            'session_phone_time' => $sessionPhoneTime,
            'session_email_time' => $sessionEmailTime,
            'final_phone_time' => $finalPhoneTime,
            'final_email_time' => $finalEmailTime
        ]);

        return view('auth.verify-phone', [
            'phone_verified' => !empty($user->phone_verified_at),
            'email_verified' => !empty($user->email_verified_at),
            'phone_time_remaining' => $finalPhoneTime,
            'email_time_remaining' => $finalEmailTime,
            'is_pakistan_number' => $isPakistanNumber
        ]);
    }

    public function verifyPhone(Request $request)
    {
        $user = Auth::user();

        // Only allow phone verification for Pakistani numbers
        if (!$this->isFromPakistan($user->phone)) {
            return back()->withErrors(['code' => 'Phone verification is not required for non-Pakistani numbers']);
        }

        $request->validate(['code' => 'required|array', 'code.*' => 'required|string|size:1']);
        $code = implode('', $request->input('code'));

        $verification = PhoneVerification::where('phone', $user->phone)
            ->where('code', $code)
            ->where('expires_at', '>', now())
            ->first();

        if ($verification) {
            $user->phone_verified_at = now();
            $user->save();
            $verification->delete();

            if ($user->email_verified_at) {
                // Check if user has purchases
                $hasPurchases = \App\Models\UserCourse::where('user_id', $user->id)
                    ->where('status', 'active')
                    ->exists();

                if ($hasPurchases) {
                    return redirect()->route('user.dashboard')->with('success', 'Phone number verified successfully!');
                } else {
                    return redirect()->route('dashboard')->with('success', 'Phone number verified successfully!');
                }
            }
            return back()->with('success', 'Phone number verified successfully! Please verify your email as well.');
        }

        return back()->withErrors(['code' => 'Invalid or expired verification code']);
    }

    public function verifyEmail(Request $request)
    {
        $request->validate(['email_code' => 'required|array', 'email_code.*' => 'required|string|size:1']);
        $code = implode('', $request->input('email_code'));
        $user = Auth::user();

        // Set a shorter timeout for this operation
        set_time_limit(30);

        try {
            if ($this->emailVerificationService->verifyOtp($user->email, $code)) {
                $user->email_verified_at = now();
                $user->save();

                // For non-Pakistani numbers, email verification is sufficient
                if (!$this->isFromPakistan($user->phone)) {
                    // Check if user has purchases
                    $hasPurchases = \App\Models\UserCourse::where('user_id', $user->id)
                        ->where('status', 'active')
                        ->exists();

                    if ($hasPurchases) {
                        return redirect()->route('user.dashboard')->with('success', 'Email verified successfully!');
                    } else {
                        return redirect()->route('dashboard')->with('success', 'Email verified successfully!');
                    }
                }

                // For Pakistani numbers, check if phone is also verified
                if ($user->phone_verified_at) {
                    // Check if user has purchases
                    $hasPurchases = \App\Models\UserCourse::where('user_id', $user->id)
                        ->where('status', 'active')
                        ->exists();

                    if ($hasPurchases) {
                        return redirect()->route('user.dashboard')->with('success', 'Email verified successfully!');
                    } else {
                        return redirect()->route('dashboard')->with('success', 'Email verified successfully!');
                    }
                }
                return back()->with('success', 'Email verified successfully! Please verify your phone number as well.');
            }

            return back()->withErrors(['email_code' => 'Invalid verification code']);
        } catch (\Exception $e) {
            Log::error('Email verification failed', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage()
            ]);
            return back()->withErrors(['email_code' => 'Failed to verify email. Please try again.']);
        }
    }

    public function resend(Request $request)
    {
        $user = Auth::user();
        $type = $request->input('type', 'phone');
        $defaultTimerValue = 300; // 5 minutes

        try {
            if ($type === 'email' && !$user->email_verified_at) {
                // Delete any existing email verification
                EmailVerification::where('email', $user->email)->delete();

                // Generate new OTP
                $result = $this->emailVerificationService->generateOtp($user->email);
                if (!$result) {
                    return back()->withErrors(['email' => 'Failed to send email verification code. Please try again later.']);
                }

                // Get fresh verification record to get the exact expiry time
                $verification = EmailVerification::where('email', $user->email)
                    ->where('expires_at', '>', now())
                    ->first();

                $emailTime = $verification ? (int) now()->diffInSeconds($verification->expires_at, false) : $defaultTimerValue;

                // Store in session with longer persistence
                session(['email_time_remaining' => $emailTime]);

                return redirect()->route('verification.notice')
                    ->with('success', 'Email verification code sent successfully!')
                    ->with('email_time_remaining', $emailTime);

            } elseif (!$user->phone_verified_at && !empty($user->phone)) {
                // Delete any existing phone verification
                PhoneVerification::where('phone', $user->phone)->delete();

                // Use country-specific method for SMS resending
                $this->sendVerificationCodeByCountry($user->phone);

                // Get fresh verification record to get the exact expiry time
                $verification = PhoneVerification::where('phone', $user->phone)
                    ->where('expires_at', '>', now())
                    ->first();

                $phoneTime = $verification ? (int) now()->diffInSeconds($verification->expires_at, false) : $defaultTimerValue;

                // Store in session with longer persistence
                session(['phone_time_remaining' => $phoneTime]);

                return redirect()->route('verification.notice')
                    ->with('success', 'Phone verification code sent successfully!')
                    ->with('phone_time_remaining', $phoneTime);

            } else {
                return back()->with('success', 'Verification already completed!');
            }
        } catch (\Exception $e) {
            Log::error('Error resending verification code', [
                'type' => $type,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return back()->withErrors([$type => $e->getMessage()]);
        }
    }
}
