/* User Dashboard Styles */

/* Course cards */
.course-card {
    border-radius: 8px;
}

.course-card-img {
    height: 180px;
    object-fit: cover;
}

/* Progress bars */
.progress-thin {
    height: 4px;
}

.progress-mini {
    height: 6px;
    width: 100px;
}

/* Dynamic width classes for progress bars */
.progress-width-0 { width: 0%; }
.progress-width-5 { width: 5%; }
.progress-width-10 { width: 10%; }
.progress-width-15 { width: 15%; }
.progress-width-20 { width: 20%; }
.progress-width-25 { width: 25%; }
.progress-width-30 { width: 30%; }
.progress-width-35 { width: 35%; }
.progress-width-40 { width: 40%; }
.progress-width-45 { width: 45%; }
.progress-width-50 { width: 50%; }
.progress-width-55 { width: 55%; }
.progress-width-60 { width: 60%; }
.progress-width-65 { width: 65%; }
.progress-width-70 { width: 70%; }
.progress-width-75 { width: 75%; }
.progress-width-80 { width: 80%; }
.progress-width-85 { width: 85%; }
.progress-width-90 { width: 90%; }
.progress-width-95 { width: 95%; }
.progress-width-100 { width: 100%; }
