/* Additional CSS fixes for GitHub buttons and other inline styles */

/* Fix for GitHub buttons inline styles */
.btn-github {
    display: inline-block;
    margin: 0.5rem 0;
    background-color: #24292e;
    color: #fff;
    border-radius: 3px;
    padding: 5px 10px;
    font-size: 12px;
    text-decoration: none;
}

.btn-github:hover {
    background-color: #0366d6;
    color: #fff;
}

/* Fix for inline styles in signin page */
.auth-bg-image {
    background-image: url('../../../assets/img/image-sign-in.jpg');
    background-size: cover;
    background-position: center;
}

.blur {
    backdrop-filter: blur(5px);
    background-color: rgba(255, 255, 255, 0.8);
}

/* Additional styles for auth page elements */
.auth-icon-primary {
    color: #4285f4;
}

.auth-icon-secondary {
    color: #6c757d;
}

.auth-submit-btn {
    background: linear-gradient(135deg, #4285f4 0%, #1976d2 100%);
    border: none;
}

.auth-signup-btn {
    background: linear-gradient(135deg, #4285f4 0%, #1976d2 100%);
    border: none;
}

/* Fix for any other inline styles that might be causing issues */
.position-absolute.w-40.top-0.end-0.h-100 {
    width: 40%;
}

.oblique-image.position-absolute.fixed-top.ms-auto.h-100.z-index-0.bg-cover.ms-n8 {
    margin-left: -8rem;
}