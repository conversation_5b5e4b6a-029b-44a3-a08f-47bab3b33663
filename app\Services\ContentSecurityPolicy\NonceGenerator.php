<?php

namespace App\Services\ContentSecurityPolicy;

class NonceGenerator
{
    /**
     * The generated nonce value.
     *
     * @var string|null
     */
    protected static $nonce = null;

    /**
     * Generate a cryptographically secure nonce value.
     *
     * @return string
     */
    public static function generate(): string
    {
        if (static::$nonce === null) {
            // Generate a cryptographically secure random value
            $random = random_bytes(16);
            
            // Convert to base64 and remove any non-alphanumeric characters
            static::$nonce = rtrim(strtr(base64_encode($random), '+/', '-_'), '=');
        }

        return static::$nonce;
    }

    /**
     * Get the current nonce value, generating one if it doesn't exist.
     *
     * @return string
     */
    public static function getNonce(): string
    {
        return static::generate();
    }

    /**
     * Get the nonce attribute for HTML tags.
     *
     * @return string
     */
    public static function getNonceAttribute(): string
    {
        return 'nonce="' . static::getNonce() . '"';
    }

    /**
     * Reset the nonce value (useful for testing).
     *
     * @return void
     */
    public static function reset(): void
    {
        static::$nonce = null;
    }
}