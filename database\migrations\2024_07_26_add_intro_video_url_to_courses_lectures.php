<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            $table->string('intro_video_url')->nullable()->after('image_path')->comment('YouTube URL for course introduction video');
        });

        Schema::table('lectures', function (Blueprint $table) {
            $table->string('intro_video_url')->nullable()->after('image_path')->comment('YouTube URL for lecture introduction video');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            $table->dropColumn('intro_video_url');
        });

        Schema::table('lectures', function (Blueprint $table) {
            $table->dropColumn('intro_video_url');
        });
    }
}; 