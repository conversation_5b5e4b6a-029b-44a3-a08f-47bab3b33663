/**
 * Livewire CSP Fix
 * This script helps Livewire work with strict Content Security Policy
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get CSP nonce from meta tag
    const nonceMetaTag = document.querySelector('meta[name="csp-nonce"]');
    const nonce = nonceMetaTag ? nonceMetaTag.getAttribute('content') : null;
    
    if (!nonce) {
        console.warn('CSP nonce not found in meta tag');
        return;
    }
    
    // Override document.createElement to add nonce to style elements
    const originalCreateElement = document.createElement;
    document.createElement = function() {
        const element = originalCreateElement.apply(document, arguments);
        
        if (arguments[0].toLowerCase() === 'style') {
            element.setAttribute('nonce', nonce);
        }
        
        return element;
    };
    
    // Fix existing style tags without nonce
    function fixExistingStyleTags() {
        const styleTagsWithoutNonce = document.querySelectorAll('style:not([nonce])');
        
        styleTagsWithoutNonce.forEach(styleTag => {
            styleTag.setAttribute('nonce', nonce);
        });
    }
    
    // Fix existing style tags
    fixExistingStyleTags();
    
    // Set up a MutationObserver to watch for new style tags
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1 && node.tagName === 'STYLE' && !node.hasAttribute('nonce')) {
                        node.setAttribute('nonce', nonce);
                    }
                });
            }
        });
    });
    
    // Start observing the document
    observer.observe(document.documentElement, {
        childList: true,
        subtree: true
    });
    
    // Special handling for Livewire
    if (window.Livewire) {
        console.log('Livewire detected, applying CSP fixes');
        
        // Hook into Livewire's style injection if possible
        if (window.Livewire.hook) {
            window.Livewire.hook('message.processed', (message, component) => {
                // Fix any style tags that might have been added by Livewire
                fixExistingStyleTags();
            });
        }
    }
});