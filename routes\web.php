<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Auth\ResetPasswordController;
use App\Http\Controllers\Auth\ForgotPasswordController;
use App\Http\Controllers\CourseController;
use App\Livewire\Shoppingcart;
use App\Livewire\Checkout;
use App\Http\Controllers\PaymentController;
use App\Livewire\CourseName;
use App\Livewire\ManageCoursesLectures;
use App\Http\Controllers\GuestController;
use App\Http\Controllers\VerificationController;
use App\Http\Controllers\DashboardController;
use App\Services\TwilioService;
use App\Models\PhoneVerification;
use Illuminate\Http\Request; // Ensure this import is correct
use App\Http\Controllers\SmsController;
use App\Livewire\CoursesList;
use App\Livewire\CourseDetail;
use App\Livewire\LectureDetail;
use App\Livewire\CourseForm;
use App\Http\Controllers\CourseViewController;
use App\Http\Controllers\QuestionController;
use App\Http\Controllers\Admin\AnswerController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\VideoProxyController;
use App\Http\Controllers\RatingController;
use App\Http\Controllers\QuizController;
use App\Http\Controllers\LectureProgressController;
use App\Http\Controllers\User\CertificateController;
use Illuminate\Support\Facades\Config;
use App\Http\Controllers\DeviceController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::resource('users',UserController::class);

// Test route without middleware to check basic functionality
Route::get('/test-basic', function () {
    return 'Basic routing works! Environment: ' . app()->environment();
})->withoutMiddleware(['web']);

// Debug route to test dashboard access
Route::get('/test-dashboard', function () {
    try {
        return app(\App\Http\Controllers\DashboardController::class)->index();
    } catch (\Exception $e) {
        return 'Dashboard error: ' . $e->getMessage();
    }
});

Route::get('/', function () {
    try {
        // Check if user is authenticated and redirect accordingly
        if (Auth::check()) {
            $user = Auth::user();

            // Redirect super admin and admin to admin dashboard
            if ($user->isSuperAdmin() || $user->isAdmin()) {
                return redirect('/admin');
            }

            // Check if user has purchases
            $hasPurchases = \App\Models\UserCourse::where('user_id', $user->id)
                ->where('status', 'active')
                ->exists();

            if ($hasPurchases) {
                return redirect()->route('user.dashboard');
            }
        }

        // For guests or users without purchases, redirect to dashboard
        return redirect()->route('dashboard');
    } catch (\Exception $e) {
        // If there's any error, log it and redirect to dashboard
        \Log::error('Root route error: ' . $e->getMessage());
        return redirect()->route('dashboard');
    }
})->name('home');

// Public dashboard route
Route::get('/dashboard', [DashboardController::class, 'index'])
    ->name('dashboard');

// Protected dashboard features for authenticated users
Route::middleware(['auth', 'verified', 'device.restriction'])->group(function() {
    Route::get('/my-dashboard', [DashboardController::class, 'authenticatedIndex'])
        ->name('authenticated.dashboard');
});

Route::get('/tables', function () {
    return view('tables');
})->name('tables')->middleware(['auth', 'verified']);

// Wallet route removed - example page deleted

Route::get('/RTL', function () {
    return view('RTL');
})->name('RTL')->middleware(['auth', 'verified']);

Route::get('/profile', function () {
    return view('account-pages.profile');
})->name('profile')->middleware(['auth', 'verified']);

Route::get('/signin', function () {
    return view('account-pages.signin');
})->name('signin');

Route::post('/signin', [LoginController::class, 'store'])
    ->middleware('guest');

Route::get('/signup', function () {
    return view('account-pages.signup');
})->name('signup')->middleware('guest');

Route::post('/signup', [RegisterController::class, 'store'])
    ->middleware(['guest', 'password.strength', 'XSS']);

Route::get('/sign-up', [RegisterController::class, 'create'])
    ->middleware('guest')
    ->name('sign-up');

Route::post('/sign-up', [RegisterController::class, 'store'])
    ->middleware(['guest', 'password.strength', 'XSS']);

Route::get('/sign-in', [LoginController::class, 'create'])
    ->middleware('guest')
    ->name('sign-in');

Route::post('/sign-in', [LoginController::class, 'store'])
    ->middleware(['guest', 'XSS', 'throttle:5,1']);

Route::get('/device-restriction-error', function () {
    return view('auth.device-restriction-error');
})->name('device.restriction.error');

Route::post('/logout', [LoginController::class, 'destroy'])
    ->middleware('auth')
    ->name('logout');

Route::get('/forgot-password', [ForgotPasswordController::class, 'create'])
    ->middleware('guest')
    ->name('password.request');

Route::post('/forgot-password', [ForgotPasswordController::class, 'store'])
    ->middleware('guest')
    ->name('password.email');

Route::get('/reset-password/{token}', [ResetPasswordController::class, 'create'])
    ->middleware('guest')
    ->name('password.reset');

Route::post('/reset-password', [ResetPasswordController::class, 'store'])
    ->middleware('guest');

// Commented out guest login since we want users to sign up or sign in
// Route::get('/guest-login', [GuestController::class, 'login'])->name('guest.login');



// Debug route to check CSP headers (remove after testing)
Route::get('/debug-csp', function () {
    $response = response()->json([
        'message' => 'CSP headers test',
        'environment' => app()->environment(),
        'timestamp' => now()->toDateTimeString()
    ]);

    // Get the nonce
    $nonce = csp_nonce();
    
    // Manually set CSP header for testing
    $cspHeader = "default-src 'self'; " .
                "script-src 'self' https://cdn.jsdelivr.net https://code.jquery.com 'unsafe-eval' 'nonce-{$nonce}'; " .
                "script-src-elem 'self' https://cdn.jsdelivr.net https://code.jquery.com https://cdn.plyr.io https://buttons.github.io https://www.youtube.com https://youtube.com https://www.youtube-nocookie.com https://maps.googleapis.com 'nonce-{$nonce}'; " .
                "style-src 'self' https://fonts.googleapis.com https://fonts.gstatic.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://use.fontawesome.com 'nonce-{$nonce}'; " .
                "style-src-elem 'self' https://fonts.googleapis.com https://fonts.gstatic.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://use.fontawesome.com https://cdn.plyr.io https://buttons.github.io 'nonce-{$nonce}'; " .
                "font-src 'self' https://fonts.googleapis.com https://fonts.gstatic.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://use.fontawesome.com data:; " .
                "img-src 'self' data: https:; " .
                "object-src 'none';";

    $response->headers->set('Content-Security-Policy', $cspHeader);
    return $response;
});

// Test page with Font Awesome to verify CSP
Route::get('/test-fontawesome', function () {
    return '<!DOCTYPE html>
<html>
<head>
    <title>Font Awesome CSP Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <h1>Font Awesome CSP Test</h1>
    <i class="fas fa-home"></i> Home Icon
    <i class="fas fa-user"></i> User Icon
    <i class="fas fa-cog"></i> Settings Icon
    <p>If you see icons above, CSP is working correctly!</p>
</body>
</html>';
});
Route::get('/test-email-api', function () {
    $apiUrl = config('services.custom_email.api_url');
    $secretToken = config('services.custom_email.secret_token');
    $fromEmail = config('services.custom_email.from_email');
    $replyToEmail = config('services.custom_email.reply_to_email');

    // Improved fallback logic for $toEmail
    $toEmail = request('to')
        ?: config('services.custom_email.test_email')
        ?: env('EMAIL_TEST_TO', null); // Final fallback from .env if available

    // If all fallbacks fail
    if (!$toEmail) {
        return response()->json([
            'status' => '❌ Error',
            'message' => 'Recipient email address is missing. Pass ?to=<EMAIL> or set services.custom_email.test_email in config.',
        ], 400);
    }

    // Validate other config values
    if (!$apiUrl || !$secretToken || !$fromEmail || !$replyToEmail) {
        return response()->json([
            'status' => '❌ Error',
            'message' => 'Missing required configuration values.',
            'details' => compact('apiUrl', 'secretToken', 'fromEmail', 'replyToEmail'),
        ], 500);
    }

    $payload = [
        'secret_token' => $secretToken,
        'from'         => $fromEmail,
        'repto'        => $replyToEmail,
        'to'           => $toEmail,
        'subject'      => 'Testing Email from Laravel',
        'body'         => '<span>This is a <b>test</b> email to check the email API.</span>',
        'ctype'        => 'html',
    ];

    // ✅ Log the request payload before sending
    Log::info('Sending Email with parameters:', $payload);

    try {
        $response = Http::asForm()->post($apiUrl, $payload);

        Log::info('Email API Response', [
            'status' => $response->status(),
            'body' => $response->body()
        ]);

        if ($response->successful()) {
            return '✅ Email sent successfully to ' . $toEmail;
        } else {
            Log::error('Email API Error', ['response' => $response->body()]);
            return response()->json([
                'status' => '❌ Failed',
                'error_code' => $response->status(),
                'message' => 'Failed to send email',
                'response_body' => $response->body()
            ], $response->status());
        }
    } catch (\Exception $e) {
        Log::error('Exception while sending email', ['exception' => $e]);
        return response()->json([
            'status' => '❌ Exception',
            'message' => $e->getMessage()
        ], 500);
    }
});

Route::get('/my-ip', function () {
    $ip = Http::get('https://ipinfo.io/ip')->body();
    return 'Your public IP is: ' . $ip;
});

Route::get('/test-sms-diba', function () {
    $apiUrl = Config::get('services.diba_sms.api_url');
    $secretKey = Config::get('services.diba_sms.secret_key');
    $username = Config::get('services.diba_sms.username');

    // Test parameters
    $countryCode = '92';
    $mobile = '3152771063'; // Only the local part; do not include country code again
    $message = 'This is a test message from Laravel';

    $response = Http::post($apiUrl, [
        'secret_token' => $secretKey,
        'country_code' => $countryCode,
        'mobile' => $countryCode . $mobile, // Full number like 923452692785
        'message' => $message,
    ]);

    return [
        'sent_to' => $countryCode . $mobile,
        'status' => $response->status(),
        'response_body' => $response->json(),
    ];
});

Route::get('/test-change-secret', function () {
    $username = Config::get('services.diba_sms.username');
    $newSecretToken = 'A5bTa8UUFIZUa6Ym0F84sH7RPrxnW0eo3TUZVY8xnoAhDMBU7PtM1NWVAgyU'; // Example new token

    $response = Http::post('https://smsg.dibaadm.com/api/modernized-secret', [
        'username' => $username,
        'secret_token' => $newSecretToken,
    ]);

    return [
        'username' => $username,
        'status' => $response->status(),
        'response_body' => $response->json(),
    ];
});

Route::get('/account/profile', [ProfileController::class, 'index'])->name('users.profile')->middleware(['auth', 'verified']);
Route::put('/account/profile/update', [ProfileController::class, 'update'])->name('users.update')->middleware(['auth', 'verified']);
Route::get('/account/users', [UserController::class, 'index'])->name('users-management')->middleware('verified');
Route::get('/courses/create', [CourseController::class, 'create'])->name('courses.create')->middleware(['auth', 'verified']);
Route::post('/courses/store', [CourseController::class, 'store'])->name('courses.store')->middleware(['auth', 'verified']);
Route::get('/courses', [CourseController::class, 'index'])->name('courses.index');
Route::get('/shopping-cart', Shoppingcart::class)
    ->name('shopping-cart')
    ->middleware(['auth', 'verified', 'device.restriction']);
Route::get('/payment/success/{order}', [PaymentController::class, 'success'])->name('payment.success')->middleware(['auth', 'verified']);
Route::get('/payment/cancel/{order}', [PaymentController::class, 'cancel'])->name('payment.cancel')->middleware(['auth', 'verified']);
Route::get('/payment/pending/{order}', [PaymentController::class, 'pending'])->name('payment.pending')->middleware(['auth', 'verified']);

Route::get('/checkout', Checkout::class)
    ->name('checkout')
    ->middleware(['auth', 'verified', 'device.restriction']);
Route::get('/course-name', CourseName::class)->middleware(['auth', 'verified']);
Route::get('/manage-courses-lectures', ManageCoursesLectures::class)->name('manage.courses.lectures')->middleware(['auth', 'verified']);

Route::get('register', [RegisterController::class, 'create'])->name('register');
Route::post('register', [RegisterController::class, 'store']);

// Authentication and Verification Routes
Route::middleware(['auth'])->group(function () {
    // Show verification form
    Route::get('/verify-phone', [App\Http\Controllers\Auth\VerificationController::class, 'showVerificationForm'])->name('verification.notice');
    Route::get('/verify', [App\Http\Controllers\Auth\VerificationController::class, 'showVerificationForm'])->name('verification.form');

    // Handle verification
    Route::post('/verify/phone', [App\Http\Controllers\Auth\VerificationController::class, 'verifyPhone'])->name('phone.verify');
    Route::post('/verify/email', [App\Http\Controllers\Auth\VerificationController::class, 'verifyEmail'])->name('email.verify');
    Route::post('/verify/resend', [App\Http\Controllers\Auth\VerificationController::class, 'resend'])->name('verification.resend');
});

// Protected Routes (require authentication and verification)
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/coupons', function () {
        return view('coupons');
    })->name('coupons');

    // Video Security Routes
    Route::post('/api/video/token', [App\Http\Controllers\VideoTokenController::class, 'generateVideoToken'])->name('video.token');
    Route::post('/api/video/data', [App\Http\Controllers\VideoTokenController::class, 'getVideoData'])->name('video.data');

});

// Removed test and debug routes for CSP compliance

// Ultra-secure video token generation route - video ID never exposed to client
Route::get('/api/secure-video-token/test-video', function(Request $request) {
    // Video ID is stored server-side only - never sent from client
    $videoId = 'ZbqPuFUXvfA'; // This is the test video ID, stored securely on server

    // In production, you would get this from database based on user permissions
    // Example: $videoId = getUserAuthorizedVideo($request->user()->id, $courseId, $lectureId);

    // Create encrypted token that expires in 1 hour
    $data = [
        'video_id' => $videoId,
        'expires_at' => time() + 3600, // 1 hour
        'ip' => $request->ip(),
        'user_agent' => $request->userAgent(),
        'timestamp' => time()
    ];

    // Encrypt the data using Laravel's encryption
    $token = encrypt(json_encode($data));

    return response()->json(['token' => $token]);
})->name('secure.video.token.test');

// Legacy route for backward compatibility (if needed)
Route::post('/api/secure-video-token', function(Request $request) {
    return response()->json(['error' => 'Direct video ID submission not allowed for security'], 403);
})->name('secure.video.token.legacy');

// Secure video proxy route using encrypted tokens
Route::get('/secure-video-proxy/{token}', function($token) {
    try {
        // Decrypt the token
        $data = json_decode(decrypt($token), true);

        // Validate token hasn't expired
        if ($data['expires_at'] < time()) {
            abort(403, 'Token expired');
        }

        // Additional security: validate IP and user agent
        if ($data['ip'] !== request()->ip() || $data['user_agent'] !== request()->userAgent()) {
            abort(403, 'Invalid token');
        }

        $videoId = $data['video_id'];

        // Return a view that embeds the YouTube video
        $embedHtml = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Secure Video Player</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            overflow: hidden;
        }
        iframe {
            width: 100vw;
            height: 100vh;
            border: none;
        }
    </style>
</head>
<body>
    <iframe
        src="https://www.youtube.com/embed/' . $videoId . '?autoplay=0&controls=1&rel=0&showinfo=0&modestbranding=1&iv_load_policy=3&origin=' . urlencode(request()->getSchemeAndHttpHost()) . '"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowfullscreen>
    </iframe>
</body>
</html>';

        return response($embedHtml)->header('Content-Type', 'text/html');

    } catch (Exception $e) {
        abort(404, 'Invalid token');
    }
})->name('secure.video.proxy');

// Continue with other protected routes
Route::middleware(['auth', 'verified'])->group(function () {

    // User Dashboard Routes
    Route::get('/my-courses', [App\Http\Controllers\UserDashboardController::class, 'index'])->name('user.dashboard');
    Route::get('/my-quiz-results', [App\Http\Controllers\UserDashboardController::class, 'quizResults'])->name('user.quiz-results');
    Route::get('/my-courses/{course}', [App\Http\Controllers\CourseController::class, 'userCourseDetail'])
        ->name('user.course.detail');

    // Course Lecture View - for enrolled users
    Route::get('/my-course/{course}', [CourseViewController::class, 'showCourse'])->name('course.view');

    // Purchased Course Detail Page with sidebar
    Route::get('/my-course-detail/{course}', [CourseViewController::class, 'showPurchasedCourse'])->name('user.course.purchased');

    // Purchased Lecture Detail Page
    Route::get('/my-lecture-detail/{course}/{lecture}', [CourseViewController::class, 'showPurchasedLecture'])->name('user.lecture.purchased');

    // AJAX route for fetching lecture content
    Route::get('/my-course/{course}/lecture-content/{lecture}', [CourseViewController::class, 'getLectureContent'])->name('lecture.content');

    // Question and Answer routes for users
    Route::post('/questions', [App\Http\Controllers\QuestionController::class, 'store'])
        ->name('questions.store');
    Route::post('/questions/get', [App\Http\Controllers\QuestionController::class, 'getQuestions'])
        ->name('questions.get');

    // Rating routes
    Route::post('/courses/{course}/rate', [App\Http\Controllers\RatingController::class, 'storeCourseRating'])
        ->name('courses.rate');
    Route::post('/lectures/{lecture}/rate', [App\Http\Controllers\RatingController::class, 'storeLectureRating'])
        ->name('lectures.rate');
    Route::get('/courses/{course}/ratings', [App\Http\Controllers\RatingController::class, 'getCourseRatings'])
        ->name('courses.ratings');
    Route::get('/lectures/{lecture}/ratings', [App\Http\Controllers\RatingController::class, 'getLectureRatings'])
        ->name('lectures.ratings');

    // User Quiz Routes
    Route::get('/courses/{course}/lectures/{lecture}/quizzes/{quiz}', [QuizController::class, 'show'])
        ->name('quiz.show');
    Route::post('/quiz/attempts/{attempt}/submit-answer', [QuizController::class, 'submitAnswer'])
        ->name('quiz.submit-answer');
    Route::post('/quiz/attempts/{attempt}/complete', [QuizController::class, 'complete'])
        ->name('quiz.complete');
    Route::get('/courses/{course}/lectures/{lecture}/quizzes/{quiz}/attempts/{attempt}/result', [QuizController::class, 'result'])
        ->name('quiz.result');

    // Lecture progress tracking routes
    Route::post('/lecture-progress/update', [LectureProgressController::class, 'updateProgress'])
        ->name('lecture-progress.update');
    Route::post('/lecture-progress/get', [LectureProgressController::class, 'getProgress'])
        ->name('lecture-progress.get');
    Route::post('/lecture-progress/complete', [LectureProgressController::class, 'markCompleted'])
        ->name('lecture-progress.complete');

    // Removed test route for progress tracking for CSP compliance

    // Certificate routes for users
    Route::get('/certificates', [CertificateController::class, 'index'])
        ->name('user.certificates');
    Route::post('/courses/{course}/request-certificate', [CertificateController::class, 'requestCertificate'])
        ->name('user.request-certificate');
    Route::get('/certificates/{certificate}/view', [CertificateController::class, 'view'])
        ->name('user.certificate.view');
    Route::get('/certificates/{certificate}/download', [CertificateController::class, 'download'])
        ->name('user.certificate.download');
});

Route::get('/test-sms', [SmsController::class, 'sendTestSms']);

// Test YouTube embed route (for debugging CSP issues)
Route::get('/test-youtube', function() {
    return view('test-youtube');
});



// Courses and Lectures Public Routes
Route::get('/courses', CoursesList::class)->name('courses');
Route::get('/course/{id}', CourseDetail::class)->name('course.detail');
Route::get('/course/{course}/lecture/{lecture}', LectureDetail::class)->name('lecture.detail');

// Admin Routes
Route::middleware(['auth', 'check.role:Admin,Super Admin', 'super.admin.bypass'])->prefix('admin')->group(function () {
    // Admin Dashboard
    Route::resource('users',UserController::class);

    Route::get('/', [App\Http\Controllers\Admin\AdminController::class, 'index'])->name('admin.dashboard');

    // Device management routes
    Route::get('/devices', [DeviceController::class, 'index'])->name('devices.index');
    Route::delete('/devices/{device}', [DeviceController::class, 'destroy'])->name('devices.destroy');
    Route::post('/devices/reset-user/{user}', [DeviceController::class, 'resetUserDevices'])->name('devices.reset.user');

    // Instructor Profiles management
    Route::get('/instructor-profiles', [App\Http\Controllers\Admin\AdminController::class, 'instructorProfiles'])
        ->middleware('admin.permission:instructor_profiles')
        ->name('admin.instructor-profiles');
    Route::get('/instructor-profiles/create', [App\Http\Controllers\Admin\AdminController::class, 'createInstructorProfile'])
        ->middleware('admin.permission:instructor_profiles')
        ->name('admin.instructor-profiles.create');
    Route::post('/instructor-profiles', [App\Http\Controllers\Admin\AdminController::class, 'storeInstructorProfile'])
        ->middleware('admin.permission:instructor_profiles')
        ->name('admin.instructor-profiles.store');
    Route::get('/instructor-profiles/{instructorProfile}/edit', [App\Http\Controllers\Admin\AdminController::class, 'editInstructorProfile'])
        ->middleware('admin.permission:instructor_profiles')
        ->name('admin.instructor-profiles.edit');
    Route::put('/instructor-profiles/{instructorProfile}', [App\Http\Controllers\Admin\AdminController::class, 'updateInstructorProfile'])
        ->middleware('admin.permission:instructor_profiles')
        ->name('admin.instructor-profiles.update');
    Route::delete('/instructor-profiles/{instructorProfile}', [App\Http\Controllers\Admin\AdminController::class, 'destroyInstructorProfile'])
        ->middleware('admin.permission:instructor_profiles')
        ->name('admin.instructor-profiles.destroy');

    // Payment Methods management
    Route::get('/payment-methods', [App\Http\Controllers\Admin\PaymentMethodController::class, 'index'])
        ->middleware('admin.permission:payment_methods')
        ->name('admin.payment-methods.index');
    Route::get('/payment-methods/{paymentMethod}/edit', [App\Http\Controllers\Admin\PaymentMethodController::class, 'edit'])
        ->middleware('admin.permission:payment_methods')
        ->name('admin.payment-methods.edit');
    Route::put('/payment-methods/{paymentMethod}', [App\Http\Controllers\Admin\PaymentMethodController::class, 'update'])
        ->middleware('admin.permission:payment_methods')
        ->name('admin.payment-methods.update');
    Route::post('/payment-methods/{paymentMethod}/toggle-status', [App\Http\Controllers\Admin\PaymentMethodController::class, 'toggleStatus'])
        ->middleware('admin.permission:payment_methods')
        ->name('admin.payment-methods.toggle-status');
    Route::post('/payment-methods/update-order', [App\Http\Controllers\Admin\PaymentMethodController::class, 'updateOrder'])
        ->middleware('admin.permission:payment_methods')
        ->name('admin.payment-methods.update-order');

    // Courses management
    Route::get('/courses', [App\Http\Controllers\Admin\AdminController::class, 'courses'])
        ->middleware('admin.permission:courses')
        ->name('admin.courses');
    Route::get('/course/create', [CourseController::class, 'create'])
        ->middleware('admin.permission:courses')
        ->name('admin.course.create');
    Route::post('/course/store', [CourseController::class, 'store'])
        ->middleware('admin.permission:courses')
        ->name('admin.course.store');
    Route::get('/courses/{course}', [App\Http\Controllers\Admin\AdminController::class, 'showCourse'])
        ->middleware('admin.permission:courses')
        ->name('admin.courses.show');

    // Lectures management
    Route::get('/lectures', [App\Http\Controllers\Admin\AdminController::class, 'lectures'])
        ->middleware('admin.permission:lectures')
        ->name('admin.lectures');
    Route::get('/lectures/{lecture}', [App\Http\Controllers\Admin\AdminController::class, 'showLecture'])
        ->middleware('admin.permission:lectures')
        ->name('admin.lectures.show');

    // Roles management
    Route::get('/roles', [App\Http\Controllers\Admin\AdminController::class, 'roles'])
        ->middleware('admin.permission:roles')
        ->name('admin.roles');

    // Users management
    Route::get('/users', [App\Http\Controllers\Admin\AdminController::class, 'users'])
        ->middleware('admin.permission:users')
        ->name('admin.users');
    Route::get('/user/create', [UserController::class, 'create'])
        ->middleware('admin.permission:users')
        ->name('admin.user.create');

    // User verification (Super Admin only)
    Route::post('/users/{user}/verify', [App\Http\Controllers\Admin\AdminController::class, 'verifyUser'])
        ->middleware('check.role:Super Admin')
        ->name('admin.users.verify');

    // Coupons management
    Route::resource('coupons', App\Http\Controllers\Admin\CouponController::class)
        ->names(['index' => 'admin.coupons.index', 'create' => 'admin.coupons.create', 'store' => 'admin.coupons.store',
                'show' => 'admin.coupons.show', 'edit' => 'admin.coupons.edit', 'update' => 'admin.coupons.update',
                'destroy' => 'admin.coupons.destroy'])
        ->middleware('admin.permission:coupons');

    // Orders & Payments management
    Route::get('/orders', [App\Http\Controllers\Admin\OrderController::class, 'index'])
        ->middleware('admin.permission:orders')
        ->name('admin.orders');
    Route::get('/order/{order}', [App\Http\Controllers\Admin\OrderController::class, 'show'])
        ->middleware('admin.permission:orders')
        ->name('admin.order.show');
    Route::post('/order/{order}/approve', [App\Http\Controllers\Admin\OrderController::class, 'approve'])
        ->middleware('admin.permission:orders')
        ->name('admin.order.approve');
    Route::post('/order/{order}/reject', [App\Http\Controllers\Admin\OrderController::class, 'reject'])
        ->middleware('admin.permission:orders')
        ->name('admin.order.reject');

    // User Courses management
    Route::resource('user-courses', App\Http\Controllers\Admin\UserCourseController::class)
        ->names(['index' => 'admin.user-courses.index', 'create' => 'admin.user-courses.create', 'store' => 'admin.user-courses.store',
                'show' => 'admin.user-courses.show', 'edit' => 'admin.user-courses.edit', 'update' => 'admin.user-courses.update',
                'destroy' => 'admin.user-courses.destroy'])
        ->middleware('admin.permission:user_courses');

    // Admin Answer routes
    Route::get('/questions', [App\Http\Controllers\Admin\AnswerController::class, 'index'])
        ->middleware('admin.permission:questions')
        ->name('admin.questions.index');
    Route::get('/all-questions', [App\Http\Controllers\Admin\AnswerController::class, 'allQuestions'])
        ->middleware('admin.permission:questions')
        ->name('admin.questions.all');
    Route::post('/questions/{question}/reject', [App\Http\Controllers\Admin\AnswerController::class, 'rejectQuestion'])
        ->middleware('admin.permission:questions')
        ->name('admin.questions.reject');
    Route::post('/answers', [App\Http\Controllers\Admin\AnswerController::class, 'store'])
        ->middleware('admin.permission:questions')
        ->name('admin.answers.store');
    Route::post('/answers/{answer}/toggle-pin', [App\Http\Controllers\Admin\AnswerController::class, 'togglePin'])
        ->middleware('admin.permission:questions')
        ->name('admin.answers.toggle-pin');

    // Admin Permissions Management (only for admins, not super admins)
    Route::get('/permissions', [App\Http\Controllers\Admin\AdminController::class, 'permissions'])
        ->middleware('check.role:Super Admin')
        ->name('admin.permissions');
    Route::post('/permissions/update', [App\Http\Controllers\Admin\AdminController::class, 'updatePermissions'])
        ->middleware('check.role:Super Admin')
        ->name('admin.permissions.update');

    // Debug route
    Route::get('/debug-user', [App\Http\Controllers\Admin\AdminController::class, 'debugUser'])->name('admin.debug');



    // Admin access to user lecture pages (for management purposes)
    Route::get('/my-course-detail/{course}', [CourseViewController::class, 'showPurchasedCourse'])->name('admin.user.course.purchased');
    Route::get('/my-lecture-detail/{course}/{lecture}', [CourseViewController::class, 'showPurchasedLecture'])->name('admin.user.lecture.purchased');

    // Admin Quiz Management Routes
    Route::middleware(['check.role:Admin,Super Admin'])->prefix('admin')->name('admin.')->group(function () {
        // Quiz Management
        Route::resource('quizzes', App\Http\Controllers\Admin\QuizController::class);

        // Question Management
        Route::post('/quizzes/{quiz}/questions', [App\Http\Controllers\Admin\QuizController::class, 'storeQuestion'])
            ->name('quizzes.questions.store');
        Route::get('/quizzes/{quiz}/questions/create', [App\Http\Controllers\Admin\QuizController::class, 'createQuestion'])
            ->name('quiz-questions.create');
        Route::get('/quiz-questions/{question}/edit', [App\Http\Controllers\Admin\QuizController::class, 'editQuestion'])
            ->name('quiz-questions.edit');
        Route::put('/quiz-questions/{question}', [App\Http\Controllers\Admin\QuizController::class, 'updateQuestion'])
            ->name('quiz-questions.update');
        Route::delete('/quiz-questions/{question}', [App\Http\Controllers\Admin\QuizController::class, 'destroyQuestion'])
            ->name('quiz-questions.destroy');

        // Attempt Management
        Route::get('/quizzes/{quiz}/attempts', [App\Http\Controllers\Admin\QuizController::class, 'quizAttempts'])
            ->name('quizzes.attempts');
        Route::get('/quiz-attempts/{attempt}', [App\Http\Controllers\Admin\QuizController::class, 'viewAttempt'])
            ->name('quiz-attempts.show');
        Route::put('/quiz-attempts/{attempt}/grade', [App\Http\Controllers\Admin\QuizController::class, 'gradeAnswer'])
            ->name('quiz-attempts.grade');

        // Pending Quiz Reviews
        Route::get('/quiz-reviews/pending', [App\Http\Controllers\Admin\QuizController::class, 'pendingReviews'])
            ->name('quiz-reviews.pending');
        Route::post('/quiz-reviews/review', [App\Http\Controllers\Admin\QuizController::class, 'reviewAnswers'])
            ->name('quiz-reviews.review');
    });

    // Certificate Management (Super Admin only)
    Route::middleware(['check.role:Super Admin'])->group(function () {
        Route::get('/certificates', [App\Http\Controllers\Admin\CertificateController::class, 'index'])
            ->name('admin.certificates.index');
        Route::get('/certificate-requests/{id}', [App\Http\Controllers\Admin\CertificateController::class, 'showRequest'])
            ->name('admin.certificates.show-request');
        Route::post('/certificate-requests/{id}/approve', [App\Http\Controllers\Admin\CertificateController::class, 'approve'])
            ->name('admin.certificates.approve');
        Route::post('/certificate-requests/{id}/reject', [App\Http\Controllers\Admin\CertificateController::class, 'reject'])
            ->name('admin.certificates.reject');
        Route::get('/certificates/{id}/view', [App\Http\Controllers\Admin\CertificateController::class, 'viewCertificate'])
            ->name('admin.certificates.view');
        Route::get('/certificates/{id}/download', [App\Http\Controllers\Admin\CertificateController::class, 'downloadCertificate'])
            ->name('admin.certificates.download');
        Route::get('/certificates/{id}/edit', [App\Http\Controllers\Admin\CertificateController::class, 'editCertificate'])
            ->name('admin.certificates.edit');
        Route::post('/certificates/{id}/update', [App\Http\Controllers\Admin\CertificateController::class, 'updateCertificate'])
            ->name('admin.certificates.update');
    });
});

// Super Admin Routes
Route::middleware(['auth', 'super.admin'])->prefix('super-admin')->group(function () {
    // User-Admin Assignments
    Route::get('/assignments', [App\Http\Controllers\Admin\AdminUserAssignmentController::class, 'index'])->name('admin.assignments.index');
    Route::get('/assignments/create', [App\Http\Controllers\Admin\AdminUserAssignmentController::class, 'create'])->name('admin.assignments.create');
    Route::post('/assignments', [App\Http\Controllers\Admin\AdminUserAssignmentController::class, 'store'])->name('admin.assignments.store');
    Route::get('/assignments/{assignment}', [App\Http\Controllers\Admin\AdminUserAssignmentController::class, 'show'])->name('admin.assignments.show');
    Route::delete('/assignments/{assignment}', [App\Http\Controllers\Admin\AdminUserAssignmentController::class, 'destroy'])->name('admin.assignments.destroy');
    Route::post('/assignments/batch', [App\Http\Controllers\Admin\AdminUserAssignmentController::class, 'batchAssign'])->name('admin.assignments.batch');
});

// Search route
Route::get('/search', [App\Http\Controllers\SearchController::class, 'search'])->name('search');

// Video proxy routes
Route::get('/video/embed/{token}', [VideoProxyController::class, 'embedVideo'])->name('video.embed');
Route::get('/video/get-embed-url', [VideoProxyController::class, 'getEmbedUrl'])->middleware('ajax');
Route::get('/video/stream/{token}', [VideoProxyController::class, 'getVideo'])->name('video.stream');

// Course and Lecture Detail Pages
Route::get('/course/{slug}', App\Livewire\CourseDetail::class)->name('course.detail');
Route::get('/lecture/{course}/{lecture}', App\Livewire\LectureDetail::class)->name('lecture.detail');
Route::get('/courses', App\Livewire\CoursesList::class)->name('courses');
Route::get('/instructor-profiles', App\Livewire\InstructorProfiles::class)->name('instructor-profiles');

// Add a debug route to verify the configuration values
Route::get('/test-email-config', function () {
    $apiUrl = Config::get('services.custom_email.api_url');
    $secretToken = Config::get('services.custom_email.secret_token');
    $fromEmail = Config::get('services.custom_email.from_email');
    $replyToEmail = Config::get('services.custom_email.reply_to_email');

    return [
        'api_url' => $apiUrl,
        'secret_token' => substr($secretToken, 0, 10) . '...',
        'from_email' => $fromEmail,
        'reply_to_email' => $replyToEmail
    ];
});

// Direct test of our controller (for debugging)
Route::get('/test-sms-direct', function () {
    // Use exact same parameters as test-sms-diba
    $countryCode = '92';
    $mobile = '3152771063';
    $message = 'This is a test message from Laravel (Direct Test)';

    // Get API parameters
    $apiUrl = Config::get('services.diba_sms.api_url');
    $secretKey = Config::get('services.diba_sms.secret_key');

    // Format mobile number exactly as in test-sms-diba
    $formattedMobile = $countryCode . $mobile;

    // Make the same request as in test-sms-diba
    $response = Http::post($apiUrl, [
        'secret_token' => $secretKey,
        'country_code' => $countryCode,
        'mobile' => $formattedMobile,
        'message' => $message,
    ]);

    // Return response in the same format as test-sms-diba
    return [
        'sent_to' => $formattedMobile,
        'status' => $response->status(),
        'response_body' => $response->json(),
    ];
});


// Device info test route
Route::get('/device-info', function () {
    $deviceService = new \App\Services\DeviceDetectionService();
    $deviceInfo = $deviceService->getDeviceInfo();
    $deviceId = $deviceService->generateDeviceId(request());
    $ip = $deviceService->getIpAddress(request());

    $userDevices = auth()->check() ? auth()->user()->devices : collect();

    return view('device-info', compact('deviceInfo', 'deviceId', 'ip', 'userDevices'));
})->middleware('auth');

// Debug user admin status
Route::get('/debug-user/{email}', function ($email) {
    $user = \App\Models\User::where('email', $email)->first();

    if (!$user) {
        return "User not found!";
    }

    $roles = $user->roles->pluck('name')->toArray();
    $devices = $user->devices;

    return [
        'user' => [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'roles' => $roles,
            'isAdmin' => $user->isAdmin(),
            'isSuperAdmin' => $user->isSuperAdmin(),
        ],
        'devices' => $devices->map(function($device) {
            return [
                'id' => $device->id,
                'device_name' => $device->device_name,
                'device_type' => $device->device_type,
                'ip_address' => $device->ip_address,
                'is_primary' => $device->is_primary,
                'last_login_at' => $device->last_login_at,
            ];
        }),
        'device_count' => $devices->count(),
        'unique_ips' => $devices->pluck('ip_address')->unique()->count(),
    ];
});

// Test admin status when logged in
Route::get('/test-admin-status', function () {
    if (!auth()->check()) {
        return "Not logged in";
    }

    $user = auth()->user();
    return [
        'user_id' => $user->id,
        'email' => $user->email,
        'roles' => $user->roles->pluck('name')->toArray(),
        'isAdmin' => $user->isAdmin(),
        'isSuperAdmin' => $user->isSuperAdmin(),
        'device_restriction_should_skip' => $user->isAdmin() || $user->isSuperAdmin()
    ];
})->middleware('auth');

// Apply middleware to all authenticated routes
Route::middleware(['auth', 'device.restriction'])->group(function () {
    Route::get('/tables', function () {
        return view('tables');
    })->name('tables')->middleware('verified');

    // Wallet route removed - example page deleted

    Route::get('/RTL', function () {
        return view('RTL');
    })->name('RTL')->middleware('verified');

    Route::get('/profile', function () {
        return view('account-pages.profile');
    })->name('profile')->middleware('verified');

    Route::post('/logout', [LoginController::class, 'destroy'])
        ->name('logout');

    Route::get('/account/profile', [ProfileController::class, 'index'])->name('users.profile')->middleware('verified');
    Route::put('/account/profile/update', [ProfileController::class, 'update'])->name('users.update')->middleware('verified');
    Route::get('/account/users', [UserController::class, 'index'])->name('users-management')->middleware('verified');
});




