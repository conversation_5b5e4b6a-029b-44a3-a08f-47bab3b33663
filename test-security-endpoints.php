<?php
/**
 * Comprehensive Security Test Script
 * Tests the endpoints mentioned in the security report
 */

$baseUrl = 'http://127.0.0.1:8000';

// List of admin panel endpoints that should be blocked
$adminPanelEndpoints = [
    '/isp',
    '/controlpanel',
    '/cwp',
    '/cpanel',
    '/whm',
    '/webmin',
    '/plesk',
    '/directadmin',
    '/ispconfig',
    '/cyberpanel'
];

// List of sensitive directories that should be protected
$sensitiveDirectories = [
    '/build',
    '/build/',
    '/storage/courses/',
    '/storage/lectures/',
    '/storage/app/',
    '/app/',
    '/config/',
    '/database/',
    '/vendor/'
];

// List of sensitive files that should be blocked
$sensitiveFiles = [
    '/composer.json',
    '/composer.lock',
    '/package.json',
    '/.env',
    '/artisan',
    '/phpunit.xml'
];

// Test for port-based redirects (should be blocked)
$portTests = [
    ':2030',
    ':2031',
    ':2083',
    ':8080',
    ':10000'
];

echo "🔒 COMPREHENSIVE SECURITY TEST\n";
echo "==============================\n\n";

$totalTests = 0;
$passedTests = 0;
$failedTests = 0;

function testEndpoint($url, $description, &$totalTests, &$passedTests, &$failedTests) {
    echo "Testing: $description\n";
    echo "URL: $url\n";

    $totalTests++;

    // Initialize cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        echo "  ❌ Connection Error: $error\n";
        $failedTests++;
        return false;
    }

    switch ($httpCode) {
        case 404:
            echo "  ✅ SECURE - Blocked (404 Not Found)\n";
            $passedTests++;
            return true;
        case 403:
            echo "  ✅ SECURE - Blocked (403 Forbidden)\n";
            $passedTests++;
            return true;
        case 401:
            echo "  ✅ SECURE - Requires Authentication (401)\n";
            $passedTests++;
            return true;
        case 500:
            echo "  ✅ SECURE - Server Error (500) - Likely blocked by middleware\n";
            $passedTests++;
            return true;
        case 302:
        case 301:
            // Check if redirecting to home page (acceptable) or external IP (vulnerability)
            if (preg_match('/Location: (.+)/i', $response, $matches)) {
                $location = trim($matches[1]);
                if (strpos($location, '/') === 0 || strpos($location, $GLOBALS['baseUrl']) === 0) {
                    echo "  ✅ SECURE - Redirects to safe location: $location\n";
                    $passedTests++;
                    return true;
                } else {
                    echo "  ❌ VULNERABILITY - Redirects to external location: $location\n";
                    $failedTests++;
                    return false;
                }
            }
            echo "  ⚠️  REDIRECTS ($httpCode) - Location unknown\n";
            $failedTests++;
            return false;
        case 200:
            echo "  ❌ VULNERABILITY - Accessible (200 OK)\n";
            $failedTests++;
            return false;
        case 405:
            echo "  ✅ SECURE - Method not allowed (405)\n";
            $passedTests++;
            return true;
        default:
            echo "  ⚠️  HTTP $httpCode - Needs manual review\n";
            $failedTests++;
            return false;
    }
}

// Test 1: Admin Panel Endpoints
echo "1️⃣  TESTING ADMIN PANEL ENDPOINTS\n";
echo "==================================\n";
foreach ($adminPanelEndpoints as $endpoint) {
    testEndpoint($baseUrl . $endpoint, "Admin Panel: $endpoint", $totalTests, $passedTests, $failedTests);
    echo "\n";
}

// Test 2: Sensitive Directories
echo "2️⃣  TESTING SENSITIVE DIRECTORIES\n";
echo "=================================\n";
foreach ($sensitiveDirectories as $directory) {
    testEndpoint($baseUrl . $directory, "Directory: $directory", $totalTests, $passedTests, $failedTests);
    echo "\n";
}

// Test 3: Sensitive Files
echo "3️⃣  TESTING SENSITIVE FILES\n";
echo "===========================\n";
foreach ($sensitiveFiles as $file) {
    testEndpoint($baseUrl . $file, "File: $file", $totalTests, $passedTests, $failedTests);
    echo "\n";
}

// Test 4: Port-based Access
echo "4️⃣  TESTING PORT-BASED ACCESS\n";
echo "=============================\n";
foreach ($portTests as $port) {
    testEndpoint($baseUrl . $port, "Port access: $port", $totalTests, $passedTests, $failedTests);
    echo "\n";
}

// Test 5: Directory Listing
echo "5️⃣  TESTING DIRECTORY LISTING\n";
echo "=============================\n";
$directoryListingTests = [
    '/public/',
    '/storage/',
    '/assets/',
    '/css/',
    '/js/'
];

foreach ($directoryListingTests as $dir) {
    testEndpoint($baseUrl . $dir, "Directory listing: $dir", $totalTests, $passedTests, $failedTests);
    echo "\n";
}

// Final Report
echo "📊 SECURITY TEST SUMMARY\n";
echo "========================\n";
echo "Total Tests: $totalTests\n";
echo "Passed: $passedTests ✅\n";
echo "Failed: $failedTests ❌\n";
echo "Success Rate: " . round(($passedTests / $totalTests) * 100, 1) . "%\n\n";

if ($failedTests === 0) {
    echo "🎉 ALL SECURITY TESTS PASSED!\n";
    echo "Your application appears to be secure against the reported vulnerabilities.\n";
} else {
    echo "⚠️  SECURITY ISSUES DETECTED!\n";
    echo "Please review the failed tests above and implement additional security measures.\n";
}

echo "\nLegend:\n";
echo "✅ = Secure (properly blocked or protected)\n";
echo "❌ = Vulnerability detected\n";
echo "⚠️  = Needs manual review\n";
?>
