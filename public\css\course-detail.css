/* Course Detail Component Styles */

/* Instructor avatar styles */
.instructor-avatar-img {
    object-fit: cover;
}

.instructor-avatar-small {
    width: 60px;
    height: 60px;
    font-size: 1.2rem;
}

.instructor-avatar-medium {
    width: 80px;
    height: 80px;
    font-size: 1.5rem;
}

.instructor-avatar-placeholder-small {
    width: 50px;
    height: 50px;
}

/* Badge styles */
.expertise-badge {
    background-color: #f0f7ff;
    color: #0056b3;
    font-weight: 500;
    font-size: 0.85rem;
    border: 1px solid #deecff;
}

.expertise-badge-small {
    background-color: #f0f7ff;
    color: #0056b3;
    font-weight: 500;
    font-size: 0.8rem;
    border: 1px solid #deecff;
}

.skills-badge {
    background-color: #f8f9fa;
    color: #212529;
    font-weight: 500;
    font-size: 0.85rem;
    border: 1px solid #e2e8f0;
}

/* Rating bar styles */
.rating-stars-container {
    width: 80px;
}

.rating-progress-bar {
    height: 10px;
}

/* Video Protection Styles */
.video-protected {
    position: relative;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    pointer-events: auto;
}

.video-protected::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    z-index: 1;
    pointer-events: none;
}

.video-protected iframe,
.video-protected video {
    pointer-events: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.video-protected:hover::before {
    pointer-events: auto;
}

/* Disable text selection on video containers */
.ratio.ratio-16x9 {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* Star Rating Styles */
.star-rating-input .rating-star {
    cursor: pointer;
    color: #ddd;
    transition: color 0.2s ease;
}

.star-rating-input .rating-star:hover,
.star-rating-input .rating-star.active {
    color: #ffc107;
}

.star-rating-input .rating-star.hovered {
    color: #ffc107;
}

/* Chat Interface Styles */
.chat-container {
    max-height: 500px;
    overflow-y: auto;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.chat-message {
    margin-bottom: 15px;
    padding: 10px 15px;
    border-radius: 10px;
    max-width: 80%;
    word-wrap: break-word;
}

.chat-message.user {
    background: #007bff;
    color: white;
    margin-left: auto;
    text-align: right;
}

.chat-message.admin {
    background: white;
    border: 1px solid #dee2e6;
    margin-right: auto;
}

.chat-message .message-meta {
    font-size: 0.8em;
    opacity: 0.7;
    margin-top: 5px;
}

.chat-message .message-content {
    margin-bottom: 5px;
}

.chat-input-container {
    background: white;
    border-top: 1px solid #dee2e6;
    padding: 15px;
    border-radius: 0 0 8px 8px;
}

.chat-background {
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f3f4' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Attachment Preview Styles */
.attachment-preview {
    display: inline-block;
    margin-right: 10px;
    margin-bottom: 10px;
}

.attachment-preview .card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
}

.attachment-preview img {
    width: 100%;
    height: 80px;
    object-fit: cover;
}

.attachment-preview .card-footer {
    padding: 5px;
    background: #f8f9fa;
}

.attachment-preview .remove-attachment {
    font-size: 12px;
    padding: 2px 6px;
}

/* Recording Animation */
.recording-animation {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Hidden Elements */
.hidden {
    display: none !important;
}

.lecture-player-hidden {
    display: none;
}

.nav-button-hidden {
    display: none;
}

/* Image Thumbnails */
.img-thumbnail-fixed {
    max-height: 150px;
    width: auto;
    object-fit: cover;
}

/* Card Previews */
.card-preview-small {
    width: 100px;
}

.card-preview-medium {
    width: 180px;
}

/* Audio Controls */
.audio-controls {
    width: 100%;
    max-width: 200px;
}

/* Lecture Detail Page Specific Styles */
.lecture-progress-height {
    height: 8px;
}

.course-progress-height {
    height: 8px;
}

.pdf-viewer-height {
    height: 600px;
}

.attachment-img-height {
    max-height: 150px;
}

.security-warning-icon {
    font-size: 48px;
    color: red;
    margin-bottom: 20px;
}

.resume-dialog-content {
    z-index: 1000;
    width: 300px;
}

.manual-progress-tester {
    position: fixed;
    top: 10px;
    left: 10px;
    background: rgba(0,0,0,0.9);
    color: white;
    padding: 15px;
    border-radius: 8px;
    z-index: 9999;
    font-family: monospace;
    font-size: 12px;
    min-width: 300px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.5);
}

.manual-progress-title {
    font-weight: bold;
    margin-bottom: 10px;
    color: #4CAF50;
}

.security-protection-flex {
    display: flex;
    align-items: center;
    gap: 10px;
}

.security-protection-icon {
    font-size: 18px;
}

.security-protection-title {
    font-size: 16px;
    margin-bottom: 5px;
}

.security-protection-message {
    font-size: 12px;
    opacity: 0.9;
}

.content-protection-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.content-protection-title {
    margin-bottom: 15px;
    color: white;
}

.content-protection-text {
    margin-bottom: 20px;
    font-size: 16px;
}

.content-protection-subtext {
    margin-bottom: 25px;
    font-size: 14px;
    opacity: 0.9;
}

.content-protection-note {
    font-size: 12px;
    opacity: 0.7;
    margin: 0;
}

.refresh-button {
    background: white;
    color: #ff0000;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    font-weight: bold;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    margin-right: 10px;
}

.refresh-button:hover {
    background: #f0f0f0;
}

.continue-button {
    background: transparent;
    color: white;
    border: 2px solid white;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: bold;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.continue-button:hover {
    background: rgba(255,255,255,0.1);
}

.manual-test-button {
    margin: 2px;
    padding: 5px 8px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.manual-save-button {
    margin: 2px;
    padding: 5px 8px;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.manual-reset-button {
    margin: 2px;
    padding: 5px 8px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.manual-hide-button {
    margin: 2px;
    padding: 5px 8px;
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.user-info-text {
    font-size: 0.9em;
    margin-top: 8px;
}

.violation-info-text {
    font-size: 0.9em;
    margin-top: 4px;
}

/* JavaScript-generated content styles */
.card-preview-small {
    width: 100px;
}

.card-preview-medium {
    width: 180px;
}

.card-img-height {
    height: 80px;
    object-fit: cover;
}

.audio-controls {
    flex: 1;
}

.security-warning-icon {
    font-size: 48px;
    color: red;
    margin-bottom: 20px;
}

.security-protection-flex {
    display: flex;
    align-items: center;
    gap: 10px;
}

.security-protection-icon {
    font-size: 18px;
}

.security-protection-title {
    font-size: 16px;
    margin-bottom: 5px;
}

.security-protection-message {
    font-size: 12px;
    opacity: 0.9;
}

.resume-dialog-content {
    z-index: 1000;
    width: 300px;
}

.recording-warning-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #ff0000, #cc0000);
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 999999;
    text-align: center;
    padding: 20px;
    font-family: Arial, sans-serif;
}

.recording-warning-title {
    font-size: 48px;
    margin-bottom: 20px;
}

.recording-warning-heading {
    margin-bottom: 15px;
    color: white;
}

.recording-warning-text {
    margin-bottom: 20px;
    font-size: 16px;
}

.recording-warning-subtext {
    margin-bottom: 25px;
    font-size: 14px;
    opacity: 0.9;
}

.recording-warning-buttons {
    margin-bottom: 20px;
}

.recording-warning-refresh {
    background: white;
    color: #ff0000;
    border: none;
    padding: 12px 25px;
    border-radius: 5px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    margin-right: 15px;
    transition: all 0.3s ease;
}

.recording-warning-dismiss {
    background: transparent;
    color: white;
    border: 2px solid white;
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.recording-warning-note {
    font-size: 12px;
    opacity: 0.7;
    margin: 0;
}

/* Lecture image section styles */
.lecture-image-section {
    margin: 1.5rem 0;
}

.lecture-image-section .card {
    border: none;
    overflow: hidden;
}

.lecture-image-section img {
    max-height: 400px;
    object-fit: cover;
    border-radius: 0.375rem;
}
