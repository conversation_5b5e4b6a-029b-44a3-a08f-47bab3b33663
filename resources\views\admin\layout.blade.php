<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Admin Dashboard - @yield('title')</title>

    <!-- Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link href="{{ asset('assets/css/nucleo-icons.css') }}" rel="stylesheet" />
    <link href="{{ asset('assets/css/nucleo-svg.css') }}" rel="stylesheet" />
    <link id="pagestyle" href="{{ asset('assets/css/corporate-ui-dashboard.css?v=1.0.0') }}" rel="stylesheet" />

    @livewireStyles

    <style>
        .sidebar {
            min-height: 100vh;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .sidebar .nav-link {
            color: #333;
            padding: 0.8rem 1rem;
            border-radius: 0.25rem;
            margin-bottom: 0.5rem;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: #f8f9fa;
            color: #0d6efd;
        }

        .sidebar .nav-link i {
            margin-right: 0.5rem;
            width: 20px;
            text-align: center;
        }

        .main-content {
            padding: 2rem;
        }

        .card-stats {
            transition: all 0.3s ease;
        }

        .card-stats:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- Include Global Navbar -->
    <x-app.navbar />

    <div class="container mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="py-4 px-3 mb-4 @if(Auth::user()->isSuperAdmin()) bg-danger @else bg-primary @endif text-white">
                        <div class="text-center">
                            @if(Auth::user()->isSuperAdmin())
                                <h4>Super Admin Panel</h4>
                            @else
                                <h4>Admin Panel</h4>
                            @endif
                        </div>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a href="{{ route('admin.dashboard') }}" class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                                <i class="fas fa-tachometer-alt"></i> Admin Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ route('dashboard') }}" class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                                <i class="fas fa-home"></i> Main Dashboard
                            </a>
                        </li>

                        @if(Auth::user()->isSuperAdmin() || Auth::user()->permissions()->where('page', 'courses')->where('is_allowed', true)->exists())
                        <li class="nav-item">
                            <a href="{{ route('admin.courses') }}" class="nav-link {{ request()->routeIs('admin.courses') ? 'active' : '' }}">
                                <i class="fas fa-book"></i> Courses
                            </a>
                        </li>
                        @endif

                        @if(Auth::user()->isSuperAdmin() || Auth::user()->permissions()->where('page', 'lectures')->where('is_allowed', true)->exists())
                        <li class="nav-item">
                            <a href="{{ route('admin.lectures') }}" class="nav-link {{ request()->routeIs('admin.lectures') ? 'active' : '' }}">
                                <i class="fas fa-video"></i> Lectures
                            </a>
                        </li>
                        @endif

                        @if(Auth::user()->isSuperAdmin() || Auth::user()->permissions()->where('page', 'users')->where('is_allowed', true)->exists())
                        <li class="nav-item">
                            <a href="{{ route('admin.users') }}" class="nav-link {{ request()->routeIs('admin.users') ? 'active' : '' }}">
                                <i class="fas fa-users"></i> Users
                            </a>
                        </li>
                        @endif

                        @if(Auth::user()->isSuperAdmin() || Auth::user()->permissions()->where('page', 'questions')->where('is_allowed', true)->exists())
                        <li class="nav-item">
                            <a href="{{ route('admin.questions.index') }}" class="nav-link {{ request()->routeIs('admin.questions.index') ? 'active' : '' }}">
                                <i class="fas fa-question-circle"></i> Questions
                                @php
                                    if (Auth::user()->isSuperAdmin()) {
                                        // Super admin sees all pending questions
                                        $pendingCount = \App\Models\Question::where('status', 'pending')->count();
                                    } else {
                                        // Regular admin only sees pending questions from users assigned to them
                                        $assignedUserIds = \App\Models\AdminUserAssignment::where('admin_id', Auth::id())->pluck('user_id')->toArray();
                                        $pendingCount = \App\Models\Question::whereIn('user_id', $assignedUserIds)
                                                        ->where('status', 'pending')
                                                        ->count();
                                    }
                                @endphp
                                @if($pendingCount > 0)
                                    <span class="badge bg-danger ml-2">{{ $pendingCount }}</span>
                                @endif
                            </a>
                        </li>
                        @endif

                        @if(Auth::user()->isSuperAdmin() || Auth::user()->permissions()->where('page', 'roles')->where('is_allowed', true)->exists())
                        <li class="nav-item">
                            <a href="{{ route('admin.roles') }}" class="nav-link {{ request()->routeIs('admin.roles') ? 'active' : '' }}">
                                <i class="fas fa-user-tag"></i> Roles
                            </a>
                        </li>
                        @endif

                        @if(Auth::user()->isSuperAdmin() || Auth::user()->permissions()->where('page', 'coupons')->where('is_allowed', true)->exists())
                        <li class="nav-item">
                            <a href="{{ route('admin.coupons.index') }}" class="nav-link {{ request()->routeIs('admin.coupons.*') ? 'active' : '' }}">
                                <i class="fas fa-tags"></i> Coupons
                            </a>
                        </li>
                        @endif

                        @if(Auth::user()->isSuperAdmin() || Auth::user()->permissions()->where('page', 'orders')->where('is_allowed', true)->exists())
                        <li class="nav-item">
                            <a href="{{ route('admin.orders') }}" class="nav-link {{ request()->routeIs('admin.orders') ? 'active' : '' }}">
                                <i class="fas fa-receipt"></i> Orders
                                @php
                                    if (Auth::user()->isSuperAdmin()) {
                                        // Super admin sees all pending orders
                                        $pendingOrderCount = \App\Models\Order::where('status', 'pending')->count();
                                    } else {
                                        // Regular admin only sees pending orders from users assigned to them
                                        $assignedUserIds = \App\Models\AdminUserAssignment::where('admin_id', Auth::id())->pluck('user_id')->toArray();
                                        $pendingOrderCount = \App\Models\Order::whereIn('user_id', $assignedUserIds)
                                                        ->where('status', 'pending')
                                                        ->count();
                                    }
                                @endphp
                                @if($pendingOrderCount > 0)
                                    <span class="badge bg-warning ml-2">{{ $pendingOrderCount }}</span>
                                @endif
                            </a>
                        </li>
                        @endif

                        @if(Auth::user()->isSuperAdmin() || Auth::user()->permissions()->where('page', 'payment_methods')->where('is_allowed', true)->exists())
                        <li class="nav-item">
                            <a href="{{ route('admin.payment-methods.index') }}" class="nav-link {{ request()->routeIs('admin.payment-methods.*') ? 'active' : '' }}">
                                <i class="fas fa-credit-card"></i> Payment Methods
                            </a>
                        </li>
                        @endif

                        @if(Auth::user()->isSuperAdmin() || Auth::user()->permissions()->where('page', 'instructor_profiles')->where('is_allowed', true)->exists())
                        <li class="nav-item">
                            <a href="{{ route('admin.instructor-profiles') }}" class="nav-link {{ request()->routeIs('admin.instructor-profiles') ? 'active' : '' }}">
                                <i class="fas fa-chalkboard-teacher"></i> Instructor Profiles
                            </a>
                        </li>
                        @endif

                        @if(Auth::user()->isSuperAdmin() || Auth::user()->permissions()->where('page', 'quizzes')->where('is_allowed', true)->exists())
                        <li class="nav-item">
                            <a href="{{ route('admin.quizzes.index') }}" class="nav-link {{ request()->routeIs('admin.quizzes.*') && !request()->routeIs('admin.quiz-reviews.*') ? 'active' : '' }}">
                                <i class="fas fa-clipboard-check"></i> Quizzes
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ route('admin.quiz-reviews.pending') }}" class="nav-link {{ request()->routeIs('admin.quiz-reviews.*') ? 'active' : '' }}">
                                <i class="fas fa-clipboard-list"></i> Quiz Reviews
                                @php
                                    if (Auth::user()->isSuperAdmin()) {
                                        // Super admin sees all pending quiz reviews
                                        $pendingReviewCount = \App\Models\QuizAnswer::where('review_status', 'pending_review')->count();
                                    } else {
                                        // Regular admin only sees pending reviews from users assigned to them
                                        $assignedUserIds = \App\Models\AdminUserAssignment::where('admin_id', Auth::id())->pluck('user_id')->toArray();
                                        $pendingReviewCount = \App\Models\QuizAnswer::where('review_status', 'pending_review')
                                                            ->whereHas('attempt', function($query) use ($assignedUserIds) {
                                                                $query->whereIn('user_id', $assignedUserIds);
                                                            })
                                                            ->count();
                                    }
                                @endphp
                                @if($pendingReviewCount > 0)
                                    <span class="badge bg-warning ml-2">{{ $pendingReviewCount }}</span>
                                @endif
                            </a>
                        </li>
                        @endif

                        @if(Auth::user()->isSuperAdmin())
                        <li class="nav-item">
                            <a href="{{ route('admin.assignments.index') }}" class="nav-link {{ request()->routeIs('admin.assignments.*') ? 'active' : '' }}">
                                <i class="fas fa-user-cog"></i> Admin-User Assignments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ route('admin.permissions') }}" class="nav-link {{ request()->routeIs('admin.permissions') ? 'active' : '' }}">
                                <i class="fas fa-lock"></i> Admin Permissions
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ route('devices.index') }}" class="nav-link {{ request()->routeIs('devices.*') ? 'active' : '' }}">
                                <i class="fas fa-mobile-alt"></i> Device Management
                            </a>
                        </li>
                        @endif

                        <li class="nav-item mt-5">
                            <a href="{{ route('dashboard') }}" class="nav-link text-danger">
                                <i class="fas fa-sign-out-alt"></i> Back to Site
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">@yield('header', 'Dashboard')</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        @yield('actions')
                    </div>
                </div>

                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                <div class="main-content">
                    @yield('content')
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    @livewireScripts

    <!-- Event Handler CSP Fixer -->
    <script src="{{ asset('js/event-handler-fixer.js') }}" @cspNonce></script>
    <!-- Dynamic Styles Handler -->
    <script src="{{ asset('js/dynamic-styles-handler.js') }}" @cspNonce></script>

    @yield('scripts')
    @stack('scripts')
</body>
</html>
