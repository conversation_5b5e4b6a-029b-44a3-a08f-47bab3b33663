/**
 * Aggressive CSP Fix
 * Continuously monitors and fixes CSP violations as they occur
 */

(function() {
    'use strict';
    
    // Get CSP nonce
    const nonce = document.querySelector('meta[name="csp-nonce"]')?.getAttribute('content');
    
    if (!nonce) {
        console.warn('Aggressive CSP Fix: CSP nonce not found');
        return;
    }
    
    console.log('Aggressive CSP Fix: Initialized with nonce:', nonce);
    
    let fixCounter = 0;
    let isRunning = false;
    
    // Function to create a unique class name
    function createUniqueClass() {
        return 'acf-' + Date.now() + '-' + (++fixCounter);
    }
    
    // Function to create a nonce-enabled style
    function createNoncedStyle(css) {
        const style = document.createElement('style');
        style.setAttribute('nonce', nonce);
        style.textContent = css;
        document.head.appendChild(style);
        return style;
    }
    
    // Function to fix all CSP violations aggressively
    function aggressiveFix() {
        if (isRunning) return;
        isRunning = true;
        
        try {
            let totalFixed = 0;
            
            // Fix ALL inline styles
            const elementsWithStyle = document.querySelectorAll('[style]');
            elementsWithStyle.forEach(element => {
                const inlineStyle = element.getAttribute('style');
                if (inlineStyle && inlineStyle.trim()) {
                    try {
                        element.removeAttribute('style');
                        const uniqueClass = createUniqueClass();
                        element.classList.add(uniqueClass);
                        createNoncedStyle('.' + uniqueClass + ' { ' + inlineStyle + ' }');
                        totalFixed++;
                    } catch (error) {
                        // Ignore errors and continue
                    }
                }
            });
            
            // Fix ALL inline event handlers
            const eventTypes = ['click', 'change', 'submit', 'load', 'error', 'mouseover', 'mouseout', 'focus', 'blur', 'keydown', 'keyup'];
            eventTypes.forEach(eventType => {
                const handlerAttr = 'on' + eventType;
                const elements = document.querySelectorAll('[' + handlerAttr + ']');
                elements.forEach(element => {
                    const handlerCode = element.getAttribute(handlerAttr);
                    if (handlerCode && handlerCode.trim()) {
                        try {
                            element.removeAttribute(handlerAttr);
                            element.addEventListener(eventType, function(event) {
                                try {
                                    const func = new Function('event', handlerCode);
                                    func.call(this, event);
                                } catch (error) {
                                    // Ignore errors
                                }
                            });
                            totalFixed++;
                        } catch (error) {
                            // Ignore errors and continue
                        }
                    }
                });
            });
            
            // Fix ALL scripts without nonce
            const inlineScripts = document.querySelectorAll('script:not([src]):not([nonce])');
            inlineScripts.forEach(script => {
                try {
                    script.setAttribute('nonce', nonce);
                    totalFixed++;
                } catch (error) {
                    // Ignore errors
                }
            });
            
            // Fix ALL styles without nonce
            const stylesWithoutNonce = document.querySelectorAll('style:not([nonce])');
            stylesWithoutNonce.forEach(style => {
                try {
                    style.setAttribute('nonce', nonce);
                    totalFixed++;
                } catch (error) {
                    // Ignore errors
                }
            });
            
            // Fix Livewire styles specifically
            const livewireContainer = document.getElementById('livewire-styles-container');
            if (livewireContainer) {
                const livewireStyles = livewireContainer.querySelectorAll('style:not([nonce])');
                livewireStyles.forEach(style => {
                    try {
                        style.setAttribute('nonce', nonce);
                        totalFixed++;
                    } catch (error) {
                        // Ignore errors
                    }
                });
            }
            
            if (totalFixed > 0) {
                console.log('Aggressive CSP Fix: Fixed', totalFixed, 'violations');
            }
            
        } catch (error) {
            console.error('Aggressive CSP Fix: Error during fix:', error);
        } finally {
            isRunning = false;
        }
    }
    
    // Run fix immediately and continuously
    aggressiveFix();
    
    // Set up multiple observers
    const observer = new MutationObserver(function(mutations) {
        aggressiveFix();
    });
    
    observer.observe(document.documentElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'onclick', 'onchange', 'onsubmit', 'onload', 'onerror', 'onfocus', 'onblur']
    });
    
    // Also observe the head for new styles
    observer.observe(document.head, {
        childList: true,
        subtree: true
    });
    
    // Run fix very frequently
    setInterval(aggressiveFix, 100);
    
    // Run fix on various events
    ['DOMContentLoaded', 'load', 'beforeunload'].forEach(eventType => {
        document.addEventListener(eventType, aggressiveFix);
    });
    
    // Run fix when Livewire updates
    document.addEventListener('livewire:load', aggressiveFix);
    document.addEventListener('livewire:update', aggressiveFix);
    
    // Expose global function
    window.aggressiveCSPFix = {
        fix: aggressiveFix,
        nonce: nonce
    };
    
    console.log('Aggressive CSP Fix: Monitoring started');
    
})();