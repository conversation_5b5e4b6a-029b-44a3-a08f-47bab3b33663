# Directory Listing Protection Verification Checklist

This document provides a checklist for verifying that directory listing protection is properly implemented after deployment.

## Pre-Deployment Verification

1. [ ] Ensure all index.php files are included in the deployment package
2. [ ] Verify that the PreventDirectoryListing middleware is registered in the Kernel.php file
3. [ ] Confirm that custom error pages (403.blade.php and 404.blade.php) are included in the deployment
4. [ ] Check that the security logging channel is properly configured

## Post-Deployment Verification

### Manual Testing

1. [ ] Test accessing the following URLs and verify they do not show directory listings:
   - [ ] https://stage-iec-courses.dibaadm.com/assets/
   - [ ] https://stage-iec-courses.dibaadm.com/assets/css/
   - [ ] https://stage-iec-courses.dibaadm.com/assets/fonts/
   - [ ] https://stage-iec-courses.dibaadm.com/assets/img/
   - [ ] https://stage-iec-courses.dibaadm.com/assets/img/logos/
   - [ ] https://stage-iec-courses.dibaadm.com/assets/img/small-logos/
   - [ ] https://stage-iec-courses.dibaadm.com/assets/js/
   - [ ] https://stage-iec-courses.dibaadm.com/assets/js/core/
   - [ ] https://stage-iec-courses.dibaadm.com/assets/js/plugins/
   - [ ] https://stage-iec-courses.dibaadm.com/assets/scss/
   - [ ] https://stage-iec-courses.dibaadm.com/storage/

2. [ ] Verify that accessing these directories either:
   - Redirects to the home page, or
   - Shows the custom error page, or
   - Displays the content of the index.php file

3. [ ] Check that normal application functionality is not affected

### Automated Testing

Run the following commands to execute the automated tests:

```bash
# Run unit tests for the middleware
php artisan test --filter=PreventDirectoryListingTest

# Run feature tests for directory access protection
php artisan test --filter=DirectoryListingProtectionTest
```

### Log Verification

1. [ ] Check the security log file for any directory listing attempts:

```bash
tail -f storage/logs/security-*.log
```

2. [ ] Verify that attempted directory access is being properly logged with:
   - [ ] Path information
   - [ ] IP address
   - [ ] User agent details

## Security Scanning

1. [ ] Run a security scan using tools like OWASP ZAP or Nikto to verify directory listing is disabled
2. [ ] Check for any 403 or directory listing vulnerabilities in the scan results

## Troubleshooting

If directory listing protection is not working as expected:

1. Check that all index.php files are present in the directories
2. Verify that the PreventDirectoryListing middleware is registered and active
3. Check the server configuration (.htaccess or Nginx config) for any conflicting settings
4. Review the application logs for any errors related to the middleware
5. Test with different browsers to rule out caching issues

## Commands for Testing

Use these commands to test directory listing protection:

```bash
# Using curl to test directory access
curl -I https://stage-iec-courses.dibaadm.com/assets/

# Using wget to test directory access
wget --spider https://stage-iec-courses.dibaadm.com/assets/

# Using a browser with cache disabled (replace with actual domain)
# Open browser in incognito/private mode and navigate to:
# https://stage-iec-courses.dibaadm.com/assets/
```

## Reporting Issues

If any issues are found during verification:

1. Document the specific URL and behavior observed
2. Capture screenshots of any directory listings that are still visible
3. Check server logs for any related errors
4. Report the issue to the development team immediately