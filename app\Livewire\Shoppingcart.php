<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Shoppingcart as Cart;
use Illuminate\Support\Facades\Auth;

class Shoppingcart extends Component
{
    public $cartitems = [];
    public $totalAmount = 0;

    public function mount()
    {
        // Check if user is logged in but not verified
        if (Auth::check() && (!Auth::user()->email_verified_at || !Auth::user()->phone_verified_at)) {
            Auth::logout();
            session()->invalidate();
            session()->regenerateToken();
            return redirect()->route('sign-in')
                ->with('error', 'Please verify your email and phone number to access the shopping cart.');
        }

        $this->loadCartItems();
    }

    public function loadCartItems()
    {
        if (auth()->check()) {
            $this->cartitems = Cart::with('course', 'lecture')
                ->where('user_id', auth()->id())
                ->get();

            $this->calculateTotalAmount();
        }
    }

    public function calculateTotalAmount()
    {
        $this->totalAmount = $this->cartitems->sum('price');
    }

    public function removeFromCart($itemId)
    {
        // Check if user is verified
        if (auth()->check() && (!auth()->user()->email_verified_at || !auth()->user()->phone_verified_at)) {
            Auth::logout();
            session()->invalidate();
            session()->regenerateToken();
            return redirect()->route('sign-in')
                ->with('error', 'Please verify your email and phone number to manage your cart.');
        }

        if (auth()->check()) {
            Cart::where('id', $itemId)
                ->where('user_id', auth()->id())
                ->delete();
            $this->loadCartItems();

            // Update cart count directly via CartIcon component's method
            $this->dispatch('cartUpdated');
        }
    }

    public function checkout()
    {
        // Check if user is logged in
        if (!auth()->check()) {
            session()->flash('error', 'Please login or register to checkout.');
            return redirect()->route('sign-in');
        }

        // Check if user is verified
        if (!auth()->user()->email_verified_at || !auth()->user()->phone_verified_at) {
            Auth::logout();
            session()->invalidate();
            session()->regenerateToken();
            return redirect()->route('sign-in')
                ->with('error', 'Please verify your email and phone number to proceed with checkout.');
        }

        if ($this->cartitems->isEmpty()) {
            session()->flash('error', 'Your cart is empty.');
            return;
        }

        $cartDetails = $this->cartitems->map(function ($item) {
            if ($item->course) {
                $details = [
                    'course' => [
                        'name' => $item->course->name,
                        'price' => $item->price,
                        'id' => $item->course_id
                    ],
                    'course_id' => $item->course_id,
                    'lecture' => null,
                    'lecture_id' => null,
                    'quantity' => 1,
                    'price' => $item->price
                ];

                // Add discount information if available
                if ($item->discount_amount > 0) {
                    $details['original_price'] = $item->original_price;
                    $details['discount_amount'] = $item->discount_amount;
                    $details['discount_reason'] = $item->discount_reason;
                }

                return $details;
            } else {
                $details = [
                    'course' => null,
                    'course_id' => null,
                    'lecture' => [
                        'name' => $item->lecture->name,
                        'price' => $item->price,
                        'id' => $item->lecture_id
                    ],
                    'lecture_id' => $item->lecture_id,
                    'quantity' => 1,
                    'price' => $item->price
                ];

                // Add discount information if available
                if ($item->discount_amount > 0) {
                    $details['original_price'] = $item->original_price;
                    $details['discount_amount'] = $item->discount_amount;
                    $details['discount_reason'] = $item->discount_reason;
                }

                return $details;
            }
        })->toArray();

        return redirect()->route('checkout', [
            'cartItems' => $cartDetails,
            'total' => $this->totalAmount
        ]);
    }

    public function render()
    {
        return view('livewire.shoppingcart', [
            'cartitems' => $this->cartitems,
            'totalAmount' => $this->totalAmount,
        ])->layout('layouts.app');
    }
}
