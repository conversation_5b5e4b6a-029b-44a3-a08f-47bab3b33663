<x-app-layout>
    <div class="container py-4">
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-gradient-primary p-4">
                        <div class="row">
                            <div class="col-md-8">
                                <h3 class="text-white mb-0">Certificate of Completion</h3>
                                <p class="text-white opacity-8 mb-0">{{ $certificate->course->name }}</p>
                            </div>
                            <div class="col-md-4 text-end d-flex align-items-center justify-content-end">
                                <a href="{{ route('user.certificates') }}" class="btn btn-white btn-sm ms-auto">
                                    <i class="fas fa-arrow-left me-1"></i> Back to Certificates
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm border-0">
                    <div class="card-body p-4">
                        <div class="text-center mb-4">
                            <h2 class="mb-1">Certificate of Completion</h2>
                            <p class="text-muted">This certificate verifies course completion</p>
                        </div>

                        @if($certificate->file_path)
                            <div class="text-center mb-4">
                                <div class="certificate-container">
                                    @php
                                        $extension = pathinfo($certificate->file_path, PATHINFO_EXTENSION);
                                        $isPdf = strtolower($extension) === 'pdf';
                                        $isImage = in_array(strtolower($extension), ['jpg', 'jpeg', 'png', 'gif']);
                                        $filePath = asset('storage/' . $certificate->file_path);
                                    @endphp

                                    @if($isPdf)
                                        <div class="ratio ratio-16x9">
                                            <embed src="{{ $filePath }}" type="application/pdf" width="100%" height="600px" />
                                        </div>
                                        <div class="mt-3">
                                            <a href="{{ $filePath }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                                <i class="fas fa-external-link-alt me-1"></i> Open PDF in New Tab
                                            </a>
                                        </div>
                                    @elseif($isImage)
                                        <img src="{{ $filePath }}" alt="Certificate" class="img-fluid certificate-image">
                                    @else
                                        <div class="alert alert-warning">
                                            Certificate file format is not supported for preview. Please download to view.
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @else
                            <div class="alert alert-warning text-center">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                Certificate file not available for preview. Please contact support if this issue persists.
                            </div>
                        @endif

                        <div class="certificate-details p-4 border rounded-3 bg-light">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h5 class="text-muted small text-uppercase">Certificate Number</h5>
                                    <p class="font-weight-bold">{{ $certificate->certificate_number }}</p>
                                </div>
                                <div class="col-md-6">
                                    <h5 class="text-muted small text-uppercase">Issue Date</h5>
                                    <p class="font-weight-bold">{{ $certificate->issue_date->format('F d, Y') }}</p>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h5 class="text-muted small text-uppercase">Course</h5>
                                    <p class="font-weight-bold">{{ $certificate->course->name }}</p>
                                </div>
                                <div class="col-md-6">
                                    <h5 class="text-muted small text-uppercase">Recipient</h5>
                                    <p class="font-weight-bold">{{ $certificate->user->name }}</p>
                                </div>
                            </div>

                            @if($certificate->expiry_date)
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <h5 class="text-muted small text-uppercase">Expiry Date</h5>
                                        <p class="font-weight-bold">{{ $certificate->expiry_date->format('F d, Y') }}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <h5 class="text-muted small text-uppercase">Status</h5>
                                        <p>
                                            <span class="badge bg-{{ $certificate->hasExpired() ? 'danger' : 'success' }} p-2">
                                                {{ $certificate->hasExpired() ? 'Expired' : 'Valid' }}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            @else
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <h5 class="text-muted small text-uppercase">Validity</h5>
                                        <p><span class="badge bg-success p-2">Never Expires</span></p>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <div class="text-center mt-4">
                            <a href="{{ route('user.certificate.download', $certificate->id) }}" class="btn btn-primary">
                                <i class="fas fa-download me-2"></i> Download Certificate
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <x-app.footer />

    <style>
        .certificate-container {
            max-width: 800px;
            margin: 0 auto;
            border: 1px solid #ddd;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .certificate-image {
            max-width: 100%;
            height: auto;
        }
        .bg-gradient-primary {
            background-image: linear-gradient(310deg, #7928CA 0%, #FF0080 100%);
        }
        .certificate-details {
            max-width: 800px;
            margin: 20px auto;
        }
    </style>
</x-app-layout> 