@extends('admin.layout')

@section('title', 'Users Management')

@section('header', 'Users Management')

@section('actions')
    <a href="{{ route('admin.user.create') }}" class="btn btn-sm btn-primary">
        <i class="fas fa-user-plus"></i> Add New User
    </a>
@endsection

@section('content')
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">All Users</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Roles</th>
                            <th>Verification Status</th>
                            <th>Created At</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($users as $user)
                            <tr>
                                <td>{{ $user->id }}</td>
                                <td>{{ $user->name }}</td>
                                <td>{{ $user->email }}</td>
                                <td>
                                    @foreach($user->roles as $role)
                                        <span class="badge bg-primary">{{ $role->name }}</span>
                                    @endforeach
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="mb-1">
                                            <small class="text-muted">Email:</small>
                                            @if($user->email_verified_at)
                                                <span class="badge bg-success">✓ Verified</span>
                                            @else
                                                <span class="badge bg-danger">✗ Not Verified</span>
                                            @endif
                                        </div>
                                        <div>
                                            <small class="text-muted">Phone:</small>
                                            @if($user->phone_verified_at)
                                                <span class="badge bg-success">✓ Verified</span>
                                            @else
                                                <span class="badge bg-danger">✗ Not Verified</span>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td>{{ $user->created_at->format('M d, Y') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('users.edit', $user->id) }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>

                                        @if(auth()->user()->isSuperAdmin())
                                            @if(!$user->email_verified_at || !$user->phone_verified_at)
                                                <button class="btn btn-success btn-sm verify-btn"
                                                        data-id="{{ $user->id }}"
                                                        data-name="{{ $user->name }}"
                                                        title="Verify User">
                                                    <i class="fas fa-check-circle"></i>
                                                </button>
                                            @endif
                                        @endif

                                        @if($user->id != auth()->id())
                                            <button class="btn btn-danger btn-sm delete-btn" data-id="{{ $user->id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="text-center">No users found</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="mt-4">
                {{ $users->links() }}
            </div>
        </div>
    </div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const deleteButtons = document.querySelectorAll('.delete-btn');
        const verifyButtons = document.querySelectorAll('.verify-btn');

        // Handle verify buttons
        verifyButtons.forEach(button => {
            button.addEventListener('click', function() {
                const userId = this.getAttribute('data-id');
                const userName = this.getAttribute('data-name');

                if (confirm(`Are you sure you want to verify ${userName}? This will verify both email and phone.`)) {
                    // Submit verify request
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/admin/users/${userId}/verify`;
                    form.style.display = 'none';

                    const csrfToken = document.createElement('input');
                    csrfToken.type = 'hidden';
                    csrfToken.name = '_token';
                    csrfToken.value = '{{ csrf_token() }}';

                    form.appendChild(csrfToken);
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });

        // Handle delete buttons
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const userId = this.getAttribute('data-id');

                if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                    // Submit delete request
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/users/${userId}`;
                    form.style.display = 'none';

                    const csrfToken = document.createElement('input');
                    csrfToken.type = 'hidden';
                    csrfToken.name = '_token';
                    csrfToken.value = '{{ csrf_token() }}';

                    const method = document.createElement('input');
                    method.type = 'hidden';
                    method.name = '_method';
                    method.value = 'DELETE';

                    form.appendChild(csrfToken);
                    form.appendChild(method);
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
</script>
@endsection
