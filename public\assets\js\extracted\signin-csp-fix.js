/**
 * Sign-in Page CSP Fix
 * This script specifically handles CSP issues on the sign-in page
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get CSP nonce from meta tag
    const nonceMetaTag = document.querySelector('meta[name="csp-nonce"]');
    const nonce = nonceMetaTag ? nonceMetaTag.getAttribute('content') : null;
    
    if (!nonce) {
        console.warn('CSP nonce not found in meta tag');
        return;
    }
    
    // Function to add nonce to all style and script elements
    function addNonceToElements() {
        // Add nonce to style elements
        document.querySelectorAll('style:not([nonce])').forEach(style => {
            style.setAttribute('nonce', nonce);
        });
        
        // Add nonce to script elements
        document.querySelectorAll('script:not([nonce])').forEach(script => {
            script.setAttribute('nonce', nonce);
        });
    }
    
    // Run immediately
    addNonceToElements();
    
    // Set up a MutationObserver to watch for new elements
    const observer = new MutationObserver(mutations => {
        let needsUpdate = false;
        
        mutations.forEach(mutation => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) {
                        // Check if the node is a style or script element
                        if ((node.tagName === 'STYLE' || node.tagName === 'SCRIPT') && !node.hasAttribute('nonce')) {
                            node.setAttribute('nonce', nonce);
                            needsUpdate = true;
                        }
                        
                        // Check for style and script elements within the added node
                        if (node.querySelectorAll) {
                            const elements = node.querySelectorAll('style:not([nonce]), script:not([nonce])');
                            if (elements.length > 0) {
                                elements.forEach(element => element.setAttribute('nonce', nonce));
                                needsUpdate = true;
                            }
                        }
                    }
                });
            }
        });
        
        if (needsUpdate) {
            // Double check for any missed elements
            addNonceToElements();
        }
    });
    
    // Start observing the document with all possible mutation types
    observer.observe(document.documentElement, {
        childList: true,
        subtree: true,
        attributes: true,
        characterData: true
    });
    
    // Run periodically to catch any missed elements
    setInterval(addNonceToElements, 500);
});