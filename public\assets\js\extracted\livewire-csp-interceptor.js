/**
 * Livewire CSP Interceptor
 * Specifically intercepts Livewire's style creation to add nonces
 */

(function() {
    'use strict';
    
    // Get nonce
    const nonce = document.querySelector('meta[name="csp-nonce"]')?.getAttribute('content') || 
                  document.currentScript?.getAttribute('nonce');
    
    if (!nonce) {
        console.error('Livewire CSP Interceptor: No nonce available');
        return;
    }
    
    console.log('Livewire CSP Interceptor: Starting with nonce:', nonce);
    
    // Override document.createElement to add nonces to style elements
    const originalCreateElement = document.createElement;
    document.createElement = function(tagName) {
        const element = originalCreateElement.call(this, tagName);
        
        if (tagName.toLowerCase() === 'style') {
            element.setAttribute('nonce', nonce);
        }
        
        return element;
    };
    
    // Override appendChild to ensure styles have nonces
    const originalAppendChild = Node.prototype.appendChild;
    Node.prototype.appendChild = function(child) {
        if (child && child.tagName === 'STYLE' && !child.hasAttribute('nonce')) {
            child.setAttribute('nonce', nonce);
        }
        return originalAppendChild.call(this, child);
    };
    
    // Override insertBefore to ensure styles have nonces
    const originalInsertBefore = Node.prototype.insertBefore;
    Node.prototype.insertBefore = function(newNode, referenceNode) {
        if (newNode && newNode.tagName === 'STYLE' && !newNode.hasAttribute('nonce')) {
            newNode.setAttribute('nonce', nonce);
        }
        return originalInsertBefore.call(this, newNode, referenceNode);
    };
    
    // Function to fix existing Livewire styles
    function fixLivewireStyles() {
        // Fix styles in Livewire container
        const livewireContainer = document.getElementById('livewire-styles-container');
        if (livewireContainer) {
            const styles = livewireContainer.querySelectorAll('style:not([nonce])');
            styles.forEach(style => {
                style.setAttribute('nonce', nonce);
            });
        }
        
        // Fix any styles in the head without nonce
        const headStyles = document.head.querySelectorAll('style:not([nonce])');
        headStyles.forEach(style => {
            // Check if it looks like a Livewire style
            if (style.textContent && (
                style.textContent.includes('nprogress') ||
                style.textContent.includes('wire:') ||
                style.textContent.includes('[wire:') ||
                style.textContent.includes('livewire')
            )) {
                style.setAttribute('nonce', nonce);
            }
        });
        
        // Fix any inline styles that might be Livewire-related
        const elementsWithStyle = document.querySelectorAll('[style]');
        elementsWithStyle.forEach(element => {
            const inlineStyle = element.getAttribute('style');
            if (inlineStyle && (
                inlineStyle.includes('nprogress') ||
                element.hasAttribute('wire:loading') ||
                element.hasAttribute('wire:target') ||
                element.classList.contains('nprogress-custom-parent')
            )) {
                element.removeAttribute('style');
                const className = 'livewire-csp-' + Date.now();
                element.classList.add(className);
                
                const style = document.createElement('style');
                style.setAttribute('nonce', nonce);
                style.textContent = '.' + className + ' { ' + inlineStyle + ' }';
                document.head.appendChild(style);
            }
        });
    }
    
    // Run immediately
    fixLivewireStyles();
    
    // Set up observer for Livewire updates
    const observer = new MutationObserver(function(mutations) {
        fixLivewireStyles();
    });
    
    observer.observe(document.documentElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'wire:loading', 'wire:target']
    });
    
    // Run periodically for first 10 seconds
    const interval = setInterval(fixLivewireStyles, 10);
    setTimeout(() => {
        clearInterval(interval);
        console.log('Livewire CSP Interceptor: Periodic fixing stopped');
    }, 10000);
    
    // Listen for Livewire events
    document.addEventListener('livewire:load', fixLivewireStyles);
    document.addEventListener('livewire:update', fixLivewireStyles);
    document.addEventListener('livewire:component:hydrated', fixLivewireStyles);
    
    console.log('Livewire CSP Interceptor: Initialized');
    
})();