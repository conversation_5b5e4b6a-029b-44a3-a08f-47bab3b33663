<?php
/**
 * CSP Compliance Monitor
 * Run this script regularly to track CSP compliance progress
 * Usage: php monitor-csp-compliance.php
 */

class CSPMonitor {
    private $baseDir;
    private $logFile;
    private $reportDir;
    
    public function __construct() {
        $this->baseDir = __DIR__;
        $this->logFile = $this->baseDir . '/csp-compliance.log';
        $this->reportDir = $this->baseDir . '/csp-reports';
        
        if (!is_dir($this->reportDir)) {
            mkdir($this->reportDir, 0755, true);
        }
    }
    
    public function runMonitoring() {
        $this->log("🚀 Starting CSP compliance monitoring...");
        
        // Run the scanner
        $scanResults = $this->runScanner();
        
        // Generate report
        $report = $this->generateReport($scanResults);
        
        // Save report
        $this->saveReport($report);
        
        // Check for regressions
        $this->checkRegressions($report);
        
        // Display summary
        $this->displaySummary($report);
        
        $this->log("✅ Monitoring completed successfully");
        
        return $report;
    }
    
    private function runScanner() {
        $scannerPath = $this->baseDir . '/public/csp-scanner.php';
        
        if (!file_exists($scannerPath)) {
            $this->log("❌ Scanner not found at: $scannerPath");
            return null;
        }
        
        $output = shell_exec("php $scannerPath");
        $results = json_decode($output, true);
        
        if (!$results || !$results['success']) {
            $this->log("❌ Scanner failed to run properly");
            return null;
        }
        
        return $results['data'];
    }
    
    private function generateReport($scanResults) {
        if (!$scanResults) {
            return null;
        }
        
        $stats = $scanResults['stats'];
        $violations = $scanResults['violations'];
        
        // Group violations by type
        $violationsByType = [];
        foreach ($violations as $violation) {
            $type = $violation['type'];
            if (!isset($violationsByType[$type])) {
                $violationsByType[$type] = [];
            }
            $violationsByType[$type][] = $violation;
        }
        
        // Group violations by file
        $violationsByFile = [];
        foreach ($violations as $violation) {
            $file = $violation['file'];
            if (!isset($violationsByFile[$file])) {
                $violationsByFile[$file] = [];
            }
            $violationsByFile[$file][] = $violation;
        }
        
        // Calculate trends (compare with previous report)
        $trends = $this->calculateTrends($stats);
        
        return [
            'timestamp' => date('Y-m-d H:i:s'),
            'stats' => $stats,
            'violations_by_type' => $violationsByType,
            'violations_by_file' => $violationsByFile,
            'trends' => $trends,
            'summary' => $scanResults['summary'] ?? null
        ];
    }
    
    private function calculateTrends($currentStats) {
        $previousReport = $this->getLatestReport();
        
        if (!$previousReport) {
            return ['status' => 'baseline', 'message' => 'First report - establishing baseline'];
        }
        
        $prevStats = $previousReport['stats'];
        $trends = [];
        
        foreach (['inline_styles', 'inline_scripts', 'event_handlers', 'style_blocks', 'script_blocks'] as $metric) {
            $current = $currentStats[$metric] ?? 0;
            $previous = $prevStats[$metric] ?? 0;
            $change = $current - $previous;
            
            $trends[$metric] = [
                'current' => $current,
                'previous' => $previous,
                'change' => $change,
                'direction' => $change > 0 ? 'increased' : ($change < 0 ? 'decreased' : 'unchanged')
            ];
        }
        
        return $trends;
    }
    
    private function getLatestReport() {
        $files = glob($this->reportDir . '/csp-report-*.json');
        if (empty($files)) {
            return null;
        }
        
        // Sort by modification time, get latest
        usort($files, function($a, $b) {
            return filemtime($b) - filemtime($a);
        });
        
        $content = file_get_contents($files[0]);
        return json_decode($content, true);
    }
    
    private function saveReport($report) {
        if (!$report) {
            return;
        }
        
        $filename = 'csp-report-' . date('Y-m-d-H-i-s') . '.json';
        $filepath = $this->reportDir . '/' . $filename;
        
        file_put_contents($filepath, json_encode($report, JSON_PRETTY_PRINT));
        $this->log("📄 Report saved: $filename");
    }
    
    private function checkRegressions($report) {
        if (!$report || !isset($report['trends'])) {
            return;
        }
        
        $regressions = [];
        foreach ($report['trends'] as $metric => $trend) {
            if ($trend['direction'] === 'increased' && $trend['change'] > 0) {
                $regressions[] = "$metric increased by {$trend['change']} ({$trend['previous']} → {$trend['current']})";
            }
        }
        
        if (!empty($regressions)) {
            $this->log("⚠️  REGRESSIONS DETECTED:");
            foreach ($regressions as $regression) {
                $this->log("   - $regression");
            }
        } else {
            $this->log("✅ No regressions detected");
        }
    }
    
    private function displaySummary($report) {
        if (!$report) {
            echo "❌ No report data available\n";
            return;
        }
        
        $stats = $report['stats'];
        $summary = $report['summary'];
        
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "📊 CSP COMPLIANCE SUMMARY\n";
        echo str_repeat("=", 60) . "\n";
        
        echo "📅 Report Date: " . $report['timestamp'] . "\n";
        echo "📁 Total Files: " . $stats['total_files'] . "\n";
        echo "🧹 Clean Files: " . ($summary['clean_files'] ?? 'N/A') . "\n";
        echo "📈 Compliance: " . ($summary['compliance_percentage'] ?? 'N/A') . "%\n\n";
        
        echo "🔍 VIOLATIONS BREAKDOWN:\n";
        echo "   Inline Styles: " . $stats['inline_styles'] . "\n";
        echo "   Inline Scripts: " . $stats['inline_scripts'] . "\n";
        echo "   Event Handlers: " . $stats['event_handlers'] . "\n";
        echo "   Style Blocks: " . $stats['style_blocks'] . "\n";
        echo "   Script Blocks: " . $stats['script_blocks'] . "\n\n";
        
        if (isset($report['trends']) && is_array($report['trends'])) {
            echo "📈 TRENDS (vs previous report):\n";
            foreach ($report['trends'] as $metric => $trend) {
                $arrow = $trend['direction'] === 'increased' ? '📈' : 
                        ($trend['direction'] === 'decreased' ? '📉' : '➡️');
                echo "   $arrow $metric: {$trend['previous']} → {$trend['current']} ({$trend['change']:+d})\n";
            }
        }
        
        echo "\n" . str_repeat("=", 60) . "\n";
    }
    
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logEntry = "[$timestamp] $message\n";
        
        // Write to log file
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
        
        // Also output to console
        echo $logEntry;
    }
}

// Run the monitor if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $monitor = new CSPMonitor();
    $report = $monitor->runMonitoring();
    
    // Exit with appropriate code
    if ($report && isset($report['trends'])) {
        $hasRegressions = false;
        foreach ($report['trends'] as $trend) {
            if ($trend['direction'] === 'increased' && $trend['change'] > 0) {
                $hasRegressions = true;
                break;
            }
        }
        exit($hasRegressions ? 1 : 0);
    }
    
    exit(0);
}
?>
