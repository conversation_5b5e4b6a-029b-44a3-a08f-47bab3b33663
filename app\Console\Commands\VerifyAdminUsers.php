<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Role;

class VerifyAdminUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:verify-users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Auto-verify all admin and superadmin users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Auto-verifying admin and superadmin users...');

        // Get admin and superadmin users
        $adminUsers = User::whereHas('roles', function($query) {
            $query->whereIn('name', ['Admin', 'Super Admin']);
        })->get();

        $count = 0;
        foreach ($adminUsers as $user) {
            $user->update([
                'email_verified_at' => now(),
                'phone_verified_at' => now()
            ]);
            
            $this->line("Verified: {$user->name} ({$user->email})");
            $count++;
        }

        $this->info("Successfully verified {$count} admin/superadmin users!");
        
        return 0;
    }
}
