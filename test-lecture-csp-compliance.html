<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lecture Page CSP Compliance Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .violation-list {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        .violation-item {
            background-color: white;
            border-left: 4px solid #dc3545;
            padding: 10px;
            margin: 5px 0;
            border-radius: 0 4px 4px 0;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .iframe-container {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🛡️ Lecture Page CSP Compliance Test</h1>
        <p>This tool tests the CSP compliance of your lecture detail page and monitors for violations.</p>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="violation-count">0</div>
                <div class="stat-label">CSP Violations</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="youtube-status">❓</div>
                <div class="stat-label">YouTube Status</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="js-errors">0</div>
                <div class="stat-label">JS Errors</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="test-duration">0s</div>
                <div class="stat-label">Test Duration</div>
            </div>
        </div>

        <div class="status info">
            <strong>Instructions:</strong>
            <ol>
                <li>Click "Start CSP Monitoring" to begin tracking violations</li>
                <li>Open the lecture page in the iframe below</li>
                <li>Interact with the page (play videos, submit forms, etc.)</li>
                <li>Check the results below for any CSP violations</li>
            </ol>
        </div>

        <button class="test-button" onclick="startMonitoring()">Start CSP Monitoring</button>
        <button class="test-button" onclick="clearResults()">Clear Results</button>
        <button class="test-button" onclick="testYouTube()">Test YouTube Embed</button>
        <button class="test-button" onclick="exportResults()">Export Results</button>

        <div id="monitoring-status" class="status info" style="display: none;">
            <strong>🔍 Monitoring Active:</strong> CSP violations and errors are being tracked...
        </div>

        <div id="results-container">
            <div id="violation-results"></div>
            <div id="error-results"></div>
        </div>
    </div>

    <div class="test-container">
        <h2>📄 Test Page</h2>
        <p>Enter the URL of your lecture detail page to test:</p>
        <input type="text" id="page-url" placeholder="http://127.0.0.1:8000/my-lecture-detail/1/1"
               style="width: 70%; padding: 8px; margin-right: 10px;">
        <button class="test-button" onclick="loadPage()">Load Page</button>

        <div style="margin-top: 10px;">
            <strong>Quick Test Links:</strong>
            <button class="test-button" onclick="loadQuickPage('http://127.0.0.1:8000/my-lecture-detail/1/1')">Lecture 1</button>
            <button class="test-button" onclick="loadQuickPage('http://127.0.0.1:8000/my-lecture-detail/1/2')">Lecture 2</button>
            <button class="test-button" onclick="loadQuickPage('http://127.0.0.1:8000/my-lecture-detail/1/3')">Lecture 3</button>
        </div>
        
        <div class="iframe-container">
            <iframe id="test-iframe" src="about:blank"></iframe>
        </div>
    </div>

    <script>
        let violations = [];
        let jsErrors = [];
        let monitoring = false;
        let startTime = null;
        let timerInterval = null;

        // Set default URL
        document.getElementById('page-url').value = 'http://127.0.0.1:8000/my-lecture-detail/1/1';

        // Auto-load the page for immediate testing
        setTimeout(() => {
            loadPage();
        }, 1000);

        function startMonitoring() {
            if (monitoring) return;
            
            monitoring = true;
            startTime = Date.now();
            violations = [];
            jsErrors = [];
            
            document.getElementById('monitoring-status').style.display = 'block';
            updateStats();
            
            // Start timer
            timerInterval = setInterval(updateTimer, 1000);
            
            // Listen for CSP violations
            document.addEventListener('securitypolicyviolation', function(e) {
                const violation = {
                    timestamp: new Date().toISOString(),
                    directive: e.violatedDirective,
                    blockedURI: e.blockedURI,
                    documentURI: e.documentURI,
                    originalPolicy: e.originalPolicy,
                    sourceFile: e.sourceFile,
                    lineNumber: e.lineNumber,
                    columnNumber: e.columnNumber
                };
                
                violations.push(violation);
                updateViolationResults();
                updateStats();
                
                console.log('CSP Violation detected:', violation);
            });
            
            // Listen for JavaScript errors
            window.addEventListener('error', function(e) {
                const error = {
                    timestamp: new Date().toISOString(),
                    message: e.message,
                    filename: e.filename,
                    lineno: e.lineno,
                    colno: e.colno,
                    error: e.error ? e.error.toString() : 'Unknown error'
                };
                
                jsErrors.push(error);
                updateErrorResults();
                updateStats();
                
                console.log('JavaScript error detected:', error);
            });
            
            // Also listen for iframe errors
            const iframe = document.getElementById('test-iframe');
            iframe.addEventListener('load', function() {
                try {
                    // Try to access iframe content to check for errors
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    
                    // Listen for errors in iframe
                    iframe.contentWindow.addEventListener('error', function(e) {
                        const error = {
                            timestamp: new Date().toISOString(),
                            message: 'Iframe: ' + e.message,
                            filename: e.filename,
                            lineno: e.lineno,
                            colno: e.colno,
                            error: e.error ? e.error.toString() : 'Unknown iframe error'
                        };
                        
                        jsErrors.push(error);
                        updateErrorResults();
                        updateStats();
                    });
                } catch (e) {
                    // Cross-origin restrictions prevent access
                    console.log('Cannot access iframe content due to cross-origin restrictions');
                }
            });
        }

        function updateTimer() {
            if (!startTime) return;
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            document.getElementById('test-duration').textContent = elapsed + 's';
        }

        function updateStats() {
            document.getElementById('violation-count').textContent = violations.length;
            document.getElementById('js-errors').textContent = jsErrors.length;
            
            // Update YouTube status based on violations
            const youtubeViolations = violations.filter(v => 
                v.blockedURI.includes('youtube') || 
                v.blockedURI.includes('ytimg') ||
                v.directive.includes('frame-src')
            );
            
            if (youtubeViolations.length > 0) {
                document.getElementById('youtube-status').textContent = '❌';
            } else if (violations.length === 0) {
                document.getElementById('youtube-status').textContent = '❓';
            } else {
                document.getElementById('youtube-status').textContent = '✅';
            }
        }

        function updateViolationResults() {
            const container = document.getElementById('violation-results');
            
            if (violations.length === 0) {
                container.innerHTML = '<div class="status success">✅ No CSP violations detected!</div>';
                return;
            }
            
            let html = `<div class="status error">❌ ${violations.length} CSP violation(s) detected:</div>`;
            html += '<div class="violation-list">';
            
            violations.forEach((violation, index) => {
                html += `
                    <div class="violation-item">
                        <strong>Violation #${index + 1}</strong> (${violation.timestamp})<br>
                        <strong>Directive:</strong> ${violation.directive}<br>
                        <strong>Blocked URI:</strong> ${violation.blockedURI}<br>
                        <strong>Source:</strong> ${violation.sourceFile}:${violation.lineNumber}:${violation.columnNumber}<br>
                        <strong>Document:</strong> ${violation.documentURI}
                    </div>
                `;
            });
            
            html += '</div>';
            container.innerHTML = html;
        }

        function updateErrorResults() {
            const container = document.getElementById('error-results');
            
            if (jsErrors.length === 0) {
                container.innerHTML = '<div class="status success">✅ No JavaScript errors detected!</div>';
                return;
            }
            
            let html = `<div class="status warning">⚠️ ${jsErrors.length} JavaScript error(s) detected:</div>`;
            html += '<div class="violation-list">';
            
            jsErrors.forEach((error, index) => {
                html += `
                    <div class="violation-item">
                        <strong>Error #${index + 1}</strong> (${error.timestamp})<br>
                        <strong>Message:</strong> ${error.message}<br>
                        <strong>File:</strong> ${error.filename}:${error.lineno}:${error.colno}<br>
                        <strong>Details:</strong> ${error.error}
                    </div>
                `;
            });
            
            html += '</div>';
            container.innerHTML = html;
        }

        function clearResults() {
            violations = [];
            jsErrors = [];
            monitoring = false;
            startTime = null;
            
            if (timerInterval) {
                clearInterval(timerInterval);
                timerInterval = null;
            }
            
            document.getElementById('monitoring-status').style.display = 'none';
            document.getElementById('violation-results').innerHTML = '';
            document.getElementById('error-results').innerHTML = '';
            updateStats();
        }

        function loadPage() {
            const url = document.getElementById('page-url').value;
            if (!url) {
                alert('Please enter a URL to test');
                return;
            }

            document.getElementById('test-iframe').src = url;
        }

        function loadQuickPage(url) {
            document.getElementById('page-url').value = url;
            document.getElementById('test-iframe').src = url;
        }

        function testYouTube() {
            // Create a simple YouTube embed test
            const testWindow = window.open('', '_blank', 'width=800,height=600');
            testWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>YouTube CSP Test</title>
                    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; frame-src 'self' https://www.youtube.com https://youtube.com https://*.youtube.com https://*.youtube-nocookie.com; img-src 'self' data: https: https://i.ytimg.com https://s.ytimg.com https://img.youtube.com https://*.ytimg.com; script-src 'self' 'unsafe-inline' https://www.youtube.com; style-src 'self' 'unsafe-inline';">
                </head>
                <body>
                    <h2>YouTube CSP Test</h2>
                    <p>Testing YouTube embed with CSP...</p>
                    <iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>
                    <script>
                        console.log('YouTube test page loaded');
                        document.addEventListener('securitypolicyviolation', function(e) {
                            console.error('CSP Violation in YouTube test:', e);
                            alert('CSP Violation: ' + e.violatedDirective + ' - ' + e.blockedURI);
                        });
                    </script>
                </body>
                </html>
            `);
        }

        function exportResults() {
            const results = {
                timestamp: new Date().toISOString(),
                testDuration: startTime ? Math.floor((Date.now() - startTime) / 1000) : 0,
                violations: violations,
                jsErrors: jsErrors,
                summary: {
                    totalViolations: violations.length,
                    totalErrors: jsErrors.length,
                    youtubeIssues: violations.filter(v => 
                        v.blockedURI.includes('youtube') || 
                        v.blockedURI.includes('ytimg') ||
                        v.directive.includes('frame-src')
                    ).length
                }
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'csp-test-results-' + new Date().toISOString().slice(0, 19).replace(/:/g, '-') + '.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Initialize
        updateStats();
    </script>
</body>
</html>
