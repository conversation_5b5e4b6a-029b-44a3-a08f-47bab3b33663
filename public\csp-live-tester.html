<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live CSP Compliance Tester</title>
    <style>
        * { box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .content {
            padding: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 25px;
            border: 1px solid #e1e5e9;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .test-section h2 {
            margin: 0 0 20px 0;
            color: #495057;
            font-size: 1.5em;
            font-weight: 500;
        }
        .url-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 15px;
            transition: border-color 0.3s;
        }
        .url-input:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            margin: 5px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
        }
        .results {
            background: #212529;
            color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 15px;
        }
        .iframe-container {
            position: relative;
            width: 100%;
            height: 500px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            margin-top: 15px;
        }
        .iframe-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #e9ecef;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #6c757d;
        }
        .status-dot.success { background: #28a745; }
        .status-dot.error { background: #dc3545; }
        .status-dot.warning { background: #ffc107; }
        .status-dot.testing { background: #17a2b8; animation: pulse 1s infinite; }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }
        .quick-link {
            padding: 10px 15px;
            background: #e9ecef;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.2s;
            text-align: center;
            font-size: 14px;
        }
        .quick-link:hover {
            background: #dee2e6;
        }
        .error-count {
            background: #dc3545;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .success-count {
            background: #28a745;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Live CSP Compliance Tester</h1>
            <p>Test your Laravel pages for CSP violations and console errors in real-time</p>
        </div>
        
        <div class="content">
            <div class="status-bar">
                <div class="status-indicator">
                    <div class="status-dot" id="statusDot"></div>
                    <span id="statusText">Ready to test</span>
                </div>
                <div>
                    <span id="errorCount" class="error-count" style="display: none;">0 errors</span>
                    <span id="successCount" class="success-count" style="display: none;">0 passed</span>
                </div>
            </div>

            <div class="test-section">
                <h2>🎯 Quick Test Links</h2>
                <div class="quick-links">
                    <div class="quick-link" onclick="testUrl('/login')">Login Page</div>
                    <div class="quick-link" onclick="testUrl('/register')">Register Page</div>
                    <div class="quick-link" onclick="testUrl('/dashboard')">Dashboard</div>
                    <div class="quick-link" onclick="testUrl('/admin')">Admin Panel</div>
                    <div class="quick-link" onclick="testUrl('/courses')">Courses</div>
                    <div class="quick-link" onclick="testUrl('/cart')">Shopping Cart</div>
                </div>
            </div>

            <div class="test-section">
                <h2>🔗 Custom URL Test</h2>
                <input type="text" id="urlInput" class="url-input" placeholder="Enter URL to test (e.g., /admin/users)" value="/dashboard">
                <div>
                    <button class="btn" onclick="testCurrentUrl()">🧪 Test Page</button>
                    <button class="btn btn-secondary" onclick="clearResults()">🗑️ Clear Results</button>
                    <button class="btn btn-success" onclick="exportResults()">📄 Export Report</button>
                </div>
            </div>

            <div class="test-section">
                <h2>📱 Live Preview</h2>
                <div class="iframe-container">
                    <iframe id="testFrame" src="about:blank"></iframe>
                </div>
            </div>

            <div class="test-section">
                <h2>📊 Test Results</h2>
                <div id="results" class="results">Ready to test pages for CSP compliance...

Usage:
1. Click a quick link or enter a custom URL
2. Watch the live preview load
3. Check results for any console errors or CSP violations
4. Export detailed reports for analysis

The tester will automatically detect:
- CSP violations
- JavaScript errors  
- Failed resource loads
- Inline style/script violations</div>
            </div>
        </div>
    </div>

    <script>
        let testResults = [];
        let currentErrors = 0;
        let currentSuccess = 0;

        function updateStatus(status, text) {
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');
            
            statusDot.className = `status-dot ${status}`;
            statusText.textContent = text;
        }

        function updateCounts() {
            const errorCount = document.getElementById('errorCount');
            const successCount = document.getElementById('successCount');
            
            if (currentErrors > 0) {
                errorCount.textContent = `${currentErrors} errors`;
                errorCount.style.display = 'inline';
            } else {
                errorCount.style.display = 'none';
            }
            
            if (currentSuccess > 0) {
                successCount.textContent = `${currentSuccess} passed`;
                successCount.style.display = 'inline';
            } else {
                successCount.style.display = 'none';
            }
        }

        function logResult(message, type = 'info') {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            
            results.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            results.scrollTop = results.scrollHeight;
        }

        function testUrl(url) {
            document.getElementById('urlInput').value = url;
            testCurrentUrl();
        }

        function testCurrentUrl() {
            const url = document.getElementById('urlInput').value.trim();
            if (!url) {
                alert('Please enter a URL to test');
                return;
            }

            updateStatus('testing', `Testing ${url}...`);
            logResult(`Starting test for: ${url}`);
            
            // Reset counters for this test
            currentErrors = 0;
            currentSuccess = 0;
            
            const iframe = document.getElementById('testFrame');
            const fullUrl = url.startsWith('http') ? url : `${window.location.origin}${url}`;
            
            // Set up error monitoring
            const testResult = {
                url: url,
                timestamp: new Date().toISOString(),
                errors: [],
                warnings: [],
                success: true
            };

            // Monitor iframe load
            iframe.onload = function() {
                try {
                    // Try to access iframe content (will fail for cross-origin)
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    
                    if (iframeDoc) {
                        logResult(`Page loaded successfully: ${url}`, 'success');
                        currentSuccess++;
                        
                        // Check for inline styles
                        const inlineStyles = iframeDoc.querySelectorAll('[style]');
                        if (inlineStyles.length > 0) {
                            logResult(`Found ${inlineStyles.length} inline style(s)`, 'warning');
                            testResult.warnings.push(`${inlineStyles.length} inline styles found`);
                        }
                        
                        // Check for inline scripts
                        const inlineScripts = iframeDoc.querySelectorAll('script:not([src])');
                        if (inlineScripts.length > 0) {
                            logResult(`Found ${inlineScripts.length} inline script(s)`, 'warning');
                            testResult.warnings.push(`${inlineScripts.length} inline scripts found`);
                        }
                        
                        if (inlineStyles.length === 0 && inlineScripts.length === 0) {
                            logResult('No obvious CSP violations detected', 'success');
                        }
                        
                        updateStatus('success', 'Test completed successfully');
                    } else {
                        logResult('Page loaded but content not accessible (possible cross-origin)', 'warning');
                        updateStatus('warning', 'Limited access to page content');
                    }
                } catch (error) {
                    logResult(`Page loaded but content not accessible: ${error.message}`, 'warning');
                    updateStatus('warning', 'Limited access to page content');
                }
                
                updateCounts();
                testResults.push(testResult);
            };

            iframe.onerror = function() {
                logResult(`Failed to load page: ${url}`, 'error');
                currentErrors++;
                testResult.success = false;
                testResult.errors.push('Failed to load page');
                updateStatus('error', 'Page failed to load');
                updateCounts();
                testResults.push(testResult);
            };

            // Load the page
            iframe.src = fullUrl;
        }

        function clearResults() {
            document.getElementById('results').textContent = 'Results cleared. Ready for new tests...\n';
            testResults = [];
            currentErrors = 0;
            currentSuccess = 0;
            updateStatus('', 'Ready to test');
            updateCounts();
        }

        function exportResults() {
            if (testResults.length === 0) {
                alert('No test results to export. Run some tests first.');
                return;
            }

            const report = {
                timestamp: new Date().toISOString(),
                summary: {
                    total_tests: testResults.length,
                    successful: testResults.filter(r => r.success).length,
                    failed: testResults.filter(r => !r.success).length,
                    total_errors: currentErrors,
                    total_warnings: testResults.reduce((sum, r) => sum + r.warnings.length, 0)
                },
                results: testResults
            };

            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `csp-test-report-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            logResult('Test report exported successfully', 'success');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            logResult('CSP Live Tester initialized and ready');
            logResult('Click a quick link or enter a custom URL to start testing');
        });
    </script>
</body>
</html>
