<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use App\Models\EmailVerification;

class EmailVerificationService
{
    protected $apiUrl;
    protected $secretToken;
    protected $fromEmail;
    protected $replyToEmail;

    public function __construct()
    {
        $this->apiUrl = Config::get('services.custom_email.api_url');
        $this->secretToken = Config::get('services.custom_email.secret_token');
        $this->fromEmail = Config::get('services.custom_email.from_email');
        $this->replyToEmail = Config::get('services.custom_email.reply_to_email');
    }

    /**
     * Generate and send OTP for email verification
     */
    public function generateOtp(string $email): string
    {
        // Generate a 6-digit OTP
        $otp = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);

        // Store OTP in database with expiration (5 minutes)
        EmailVerification::updateOrCreate(
            ['email' => $email],
            ['code' => $otp, 'expires_at' => now()->addMinutes(5)]
        );

        // Send OTP via email using the API
        $this->sendVerificationEmail($email, $otp);

        return $otp;
    }

    /**
     * Verify the OTP for the given email
     */
    public function verifyOtp(string $email, string $code): bool
    {
        try {
            // Set a shorter timeout for this operation
            set_time_limit(30);

            $verification = EmailVerification::where('email', $email)
                ->where('code', $code)
                ->where('expires_at', '>', now())
                ->first();

            if ($verification) {
                // Delete the verification record immediately after successful verification
                $verification->delete();
                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('Email verification failed', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send verification email using custom API
     */
    protected function sendVerificationEmail(string $email, string $otp): bool
    {
        $subject = 'Your IEC Courses Email Verification Code';

        $body = '
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
                <h2 style="color: #333; text-align: center;">Email Verification</h2>
                <p>Thank you for registering with IEC Courses. Please use the following verification code to complete your registration:</p>
                <div style="background-color: #f0f7ff; padding: 15px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0; border-radius: 5px;">
                    ' . $otp . '
                </div>
                <p>This code is valid for 5 minutes and can only be used once.</p>
                <p>If you did not request this verification, please ignore this email.</p>
                <p style="margin-top: 30px; font-size: 12px; color: #777; text-align: center;">
                    &copy; ' . date('Y') . ' IEC Courses. All rights reserved.
                </p>
            </div>
        ';

        // Check if required configuration values are present
        if (!$this->apiUrl || !$this->secretToken || !$this->fromEmail || !$this->replyToEmail) {
            Log::error('Email API - Missing required configuration values', [
                'apiUrl' => $this->apiUrl,
                'secretToken' => $this->secretToken ? '[exists]' : '[missing]',
                'fromEmail' => $this->fromEmail,
                'replyToEmail' => $this->replyToEmail
            ]);
            return false;
        }

        $payload = [
            'secret_token' => $this->secretToken,
            'from' => $this->fromEmail,
            'repto' => $this->replyToEmail,
            'to' => $email,
            'subject' => $subject,
            'body' => $body,
            'ctype' => 'html'
        ];

        // Log the request payload before sending
        Log::info('Sending verification email with parameters:', $payload);

        try {
            $response = Http::asForm()->post($this->apiUrl, $payload);

            Log::info('Email API Response', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            if ($response->successful()) {
                Log::info('Email verification sent successfully to: ' . $email);
                return true;
            }

            Log::error('Email API Error', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);
            return false;
        } catch (\Exception $e) {
            Log::error('Email verification sending failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
}
