/**
 * GitHub Buttons CSP Fix
 * This script fixes CSP issues with GitHub buttons
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get all GitHub buttons
    const githubButtons = document.querySelectorAll('.github-button');
    
    if (githubButtons.length === 0) {
        return;
    }
    
    // Replace GitHub buttons with CSP-compliant versions
    githubButtons.forEach(button => {
        // Get button attributes
        const href = button.getAttribute('href') || '';
        const ariaLabel = button.getAttribute('aria-label') || '';
        const dataIcon = button.getAttribute('data-icon') || 'octicon-star';
        const dataSize = button.getAttribute('data-size') || '';
        const dataShowCount = button.getAttribute('data-show-count') === 'true';
        
        // Create button elements
        const container = document.createElement('span');
        container.className = 'github-btn';
        
        if (dataSize === 'large') {
            container.classList.add('github-btn-large');
        }
        
        const btnLink = document.createElement('a');
        btnLink.className = 'gh-btn';
        btnLink.href = href;
        btnLink.target = '_blank';
        btnLink.rel = 'noopener noreferrer';
        
        const btnIcon = document.createElement('span');
        btnIcon.className = 'gh-ico';
        
        const btnText = document.createElement('span');
        btnText.className = 'gh-text';
        
        // Set button text based on icon
        switch (dataIcon) {
            case 'octicon-star':
                btnText.textContent = 'Star';
                break;
            case 'octicon-repo-forked':
                btnText.textContent = 'Fork';
                break;
            case 'octicon-eye':
                btnText.textContent = 'Watch';
                break;
            case 'octicon-issue-opened':
                btnText.textContent = 'Issue';
                break;
            default:
                btnText.textContent = 'Star';
        }
        
        // Assemble button
        btnLink.appendChild(btnIcon);
        btnLink.appendChild(btnText);
        container.appendChild(btnLink);
        
        // Add count if needed
        if (dataShowCount) {
            const countLink = document.createElement('a');
            countLink.className = 'gh-count gh-count-visible';
            countLink.href = href;
            countLink.target = '_blank';
            countLink.rel = 'noopener noreferrer';
            countLink.textContent = '1k';
            
            container.appendChild(countLink);
        }
        
        // Replace original button with our custom one
        button.parentNode.replaceChild(container, button);
    });
});