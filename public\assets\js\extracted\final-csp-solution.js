/**
 * Final CSP Solution
 * A single, comprehensive solution for all CSP violations
 */

(function() {
    'use strict';
    
    // Get CSP nonce
    const nonce = document.querySelector('meta[name="csp-nonce"]')?.getAttribute('content');
    
    if (!nonce) {
        console.error('Final CSP Solution: No nonce found');
        return;
    }
    
    console.log('Final CSP Solution: Initialized with nonce:', nonce);
    
    let styleCounter = 0;
    let isProcessing = false;
    
    // Function to create a unique class name
    function createUniqueClass() {
        return 'fcs-' + Date.now() + '-' + (++styleCounter);
    }
    
    // Function to create a nonce-enabled style
    function createNoncedStyle(css) {
        const style = document.createElement('style');
        style.setAttribute('nonce', nonce);
        style.textContent = css;
        document.head.appendChild(style);
        return style;
    }
    
    // Function to fix all CSP violations
    function fixAllViolations() {
        if (isProcessing) return;
        isProcessing = true;
        
        try {
            let totalFixed = 0;
            
            // Fix ALL inline styles
            const elementsWithStyle = document.querySelectorAll('[style]');
            elementsWithStyle.forEach(element => {
                const inlineStyle = element.getAttribute('style');
                if (inlineStyle && inlineStyle.trim()) {
                    try {
                        element.removeAttribute('style');
                        const uniqueClass = createUniqueClass();
                        element.classList.add(uniqueClass);
                        createNoncedStyle('.' + uniqueClass + ' { ' + inlineStyle + ' }');
                        totalFixed++;
                    } catch (error) {
                        // Continue on error
                    }
                }
            });
            
            // Fix ALL inline event handlers
            const eventTypes = ['click', 'change', 'submit', 'load', 'error', 'mouseover', 'mouseout', 'focus', 'blur'];
            eventTypes.forEach(eventType => {
                const handlerAttr = 'on' + eventType;
                const elements = document.querySelectorAll('[' + handlerAttr + ']');
                elements.forEach(element => {
                    const handlerCode = element.getAttribute(handlerAttr);
                    if (handlerCode && handlerCode.trim()) {
                        try {
                            element.removeAttribute(handlerAttr);
                            element.addEventListener(eventType, function(event) {
                                try {
                                    const func = new Function('event', handlerCode);
                                    func.call(this, event);
                                } catch (error) {
                                    // Continue on error
                                }
                            });
                            totalFixed++;
                        } catch (error) {
                            // Continue on error
                        }
                    }
                });
            });
            
            // Fix ALL scripts without nonce
            const inlineScripts = document.querySelectorAll('script:not([src]):not([nonce])');
            inlineScripts.forEach(script => {
                try {
                    script.setAttribute('nonce', nonce);
                    totalFixed++;
                } catch (error) {
                    // Continue on error
                }
            });
            
            // Fix ALL styles without nonce
            const stylesWithoutNonce = document.querySelectorAll('style:not([nonce])');
            stylesWithoutNonce.forEach(style => {
                try {
                    style.setAttribute('nonce', nonce);
                    totalFixed++;
                } catch (error) {
                    // Continue on error
                }
            });
            
            // Fix Livewire styles specifically
            const livewireContainer = document.getElementById('livewire-styles-container');
            if (livewireContainer) {
                const livewireStyles = livewireContainer.querySelectorAll('style:not([nonce])');
                livewireStyles.forEach(style => {
                    try {
                        style.setAttribute('nonce', nonce);
                        totalFixed++;
                    } catch (error) {
                        // Continue on error
                    }
                });
            }
            
            if (totalFixed > 0) {
                console.log('Final CSP Solution: Fixed', totalFixed, 'violations');
            }
            
        } catch (error) {
            console.error('Final CSP Solution: Error during fix:', error);
        } finally {
            isProcessing = false;
        }
    }
    
    // Initialize immediately
    fixAllViolations();
    
    // Set up MutationObserver for dynamic content
    const observer = new MutationObserver(function(mutations) {
        let needsFix = false;
        
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        if (node.hasAttribute && (
                            node.hasAttribute('style') ||
                            node.hasAttribute('onclick') ||
                            (node.tagName === 'SCRIPT' && !node.hasAttribute('nonce') && !node.hasAttribute('src')) ||
                            (node.tagName === 'STYLE' && !node.hasAttribute('nonce'))
                        )) {
                            needsFix = true;
                        }
                        
                        // Check child elements
                        const problematicChildren = node.querySelectorAll ? 
                            node.querySelectorAll('[style], [onclick], script:not([nonce]), style:not([nonce])') : [];
                        if (problematicChildren.length > 0) {
                            needsFix = true;
                        }
                    }
                });
            } else if (mutation.type === 'attributes') {
                if (mutation.attributeName === 'style' || mutation.attributeName.startsWith('on')) {
                    needsFix = true;
                }
            }
        });
        
        if (needsFix) {
            setTimeout(fixAllViolations, 10);
        }
    });
    
    observer.observe(document.documentElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'onclick', 'onchange', 'onsubmit', 'onload', 'onerror']
    });
    
    // Run fix periodically for the first 5 seconds
    const periodicFix = setInterval(fixAllViolations, 100);
    setTimeout(() => {
        clearInterval(periodicFix);
        console.log('Final CSP Solution: Periodic fixing stopped');
    }, 5000);
    
    // Run fix on various events
    ['DOMContentLoaded', 'load'].forEach(eventType => {
        document.addEventListener(eventType, fixAllViolations);
    });
    
    // Run fix when Livewire updates
    document.addEventListener('livewire:load', fixAllViolations);
    document.addEventListener('livewire:update', fixAllViolations);
    
    // Expose global function
    window.finalCSPSolution = {
        fix: fixAllViolations,
        nonce: nonce
    };
    
    console.log('Final CSP Solution: Monitoring started');
    
})();