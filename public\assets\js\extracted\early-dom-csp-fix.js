/**
 * Early DOM CSP Fix
 * Runs as early as possible to catch violations during DOM construction
 */

(function() {
    'use strict';
    
    // Get nonce from current script or meta tag
    let nonce = document.currentScript?.getAttribute('nonce');
    if (!nonce) {
        const metaTag = document.querySelector('meta[name="csp-nonce"]');
        nonce = metaTag?.getAttribute('content');
    }
    
    if (!nonce) {
        console.error('Early DOM CSP Fix: No nonce available');
        return;
    }
    
    console.log('Early DOM CSP Fix: Starting with nonce:', nonce);
    
    let counter = 0;
    
    // Function to fix violations immediately
    function fixViolationsNow() {
        try {
            // Fix all inline styles
            const elementsWithStyle = document.querySelectorAll('[style]');
            elementsWithStyle.forEach(element => {
                const inlineStyle = element.getAttribute('style');
                if (inlineStyle && inlineStyle.trim()) {
                    element.removeAttribute('style');
                    const className = 'edf-' + Date.now() + '-' + (++counter);
                    element.classList.add(className);
                    
                    const style = document.createElement('style');
                    style.setAttribute('nonce', nonce);
                    style.textContent = '.' + className + ' { ' + inlineStyle + ' }';
                    document.head.appendChild(style);
                }
            });
            
            // Fix scripts without nonce
            const scriptsWithoutNonce = document.querySelectorAll('script:not([src]):not([nonce])');
            scriptsWithoutNonce.forEach(script => {
                script.setAttribute('nonce', nonce);
            });
            
            // Fix styles without nonce
            const stylesWithoutNonce = document.querySelectorAll('style:not([nonce])');
            stylesWithoutNonce.forEach(style => {
                style.setAttribute('nonce', nonce);
            });
            
        } catch (error) {
            console.error('Early DOM CSP Fix: Error during fix:', error);
        }
    }
    
    // Run immediately
    fixViolationsNow();
    
    // Set up observer
    if (typeof MutationObserver !== 'undefined') {
        const observer = new MutationObserver(function(mutations) {
            fixViolationsNow();
        });
        
        observer.observe(document.documentElement, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style']
        });
    }
    
    // Run periodically for first 2 seconds
    const interval = setInterval(fixViolationsNow, 100);
    setTimeout(() => {
        clearInterval(interval);
        console.log('Early DOM CSP Fix: Periodic fixing stopped');
    }, 2000);
    
    // Expose global function
    window.earlyDOMCSPFix = {
        fix: fixViolationsNow,
        nonce: nonce
    };
    
    console.log('Early DOM CSP Fix: Initialized');
    
})();