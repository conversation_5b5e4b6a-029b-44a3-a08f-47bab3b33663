/**
 * Comprehensive Sign-in Page CSP Fix Styles
 * This CSS file contains all styles that were previously inline to fix CSP violations
 */

/* Captcha display styles */
.captcha-display-fixed {
    display: inline-block;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-family: monospace;
    font-size: 16px;
    font-weight: bold;
    color: #495057;
    min-width: 100px;
    text-align: center;
}

/* Oblique image background fix */
.oblique-image-fixed {
    background-image: url('../../../assets/img/curved-images/curved6.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

/* Progress bar fixes */
.progress-bar-fixed-0,
.progress-bar-fixed-1,
.progress-bar-fixed-2,
.progress-bar-fixed-3,
.progress-bar-fixed-4 {
    transition: width 0.3s ease;
}

/* Card background fixes */
.card-bg-fixed-0 {
    background-image: url('../../../assets/img/curved-images/img-6.jpg');
    background-size: cover;
    background-position: center;
}

.card-bg-fixed-1 {
    background-image: url('../../../assets/img/curved-images/img-7.jpg');
    background-size: cover;
    background-position: center;
}

/* Video container fixes */
.video-container-fixed {
    position: relative;
    width: 100%;
    height: 400px;
    background: #000;
}

/* Loading placeholder fixes */
.loading-placeholder-fixed {
    color: white;
    text-align: center;
    padding-top: 200px;
}

.loading-details-fixed {
    font-size: 14px;
    margin-top: 10px;
}

/* Video overlay fixes */
.video-overlay-fixed {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.01);
    pointer-events: auto;
}

/* Custom controls fixes */
.custom-controls-fixed {
    display: none;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1001;
    background: rgba(0, 0, 0, 0.8);
    padding: 10px;
    align-items: center;
    gap: 10px;
}

/* Control button fixes */
.control-btn-fixed {
    background: #ff4444;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
}

.control-btn-mute-fixed {
    background: transparent;
    color: white;
    border: none;
    padding: 8px;
    cursor: pointer;
}

/* Progress container fixes */
.progress-container-fixed {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    cursor: pointer;
    position: relative;
}

.progress-bar-video-fixed {
    height: 100%;
    background: #ff4444;
    border-radius: 3px;
    width: 0%;
    transition: width 0.1s;
}

/* Time display fixes */
.time-display-fixed {
    color: white;
    font-size: 12px;
    min-width: 80px;
}

/* Volume container fixes */
.volume-container-fixed {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Volume slider fixes */
.volume-slider-fixed {
    width: 60px;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    cursor: pointer;
    position: relative;
}

.volume-bar-fixed {
    height: 100%;
    background: #ff4444;
    border-radius: 3px;
    width: 100%;
    transition: width 0.1s;
}

/* Error display fixes */
.error-display-fixed {
    color: red;
    text-align: center;
    padding-top: 200px;
}

/* Loading spinner fixes */
.loading-spinner-fixed {
    display: none;
}

/* Input group fixes */
.input-group-text-fixed {
    cursor: pointer;
}

/* Card height fixes */
.card-height-fixed {
    border-radius: 8px;
}

/* Image height fixes */
.image-height-fixed {
    height: 180px;
    object-fit: cover;
}

/* Progress height fixes */
.progress-height-fixed {
    height: 4px;
}

.progress-height-6-fixed {
    height: 6px;
    width: 100px;
}

/* Background image fixes for various elements */
.bg-cover-fixed {
    background-image: url('../../../assets/img/header-blue-purple.jpg');
    background-size: cover;
    background-position: center;
}

.illustration-bg-fixed {
    background-image: url('../../../assets/img/illustrations/illustration-signup.jpg');
    background-size: cover;
    background-position: center;
}

/* Margin and padding fixes */
.margin-20-fixed {
    margin: 20px 0;
}

.padding-4-fixed {
    padding: 4px;
}

.text-center-padding-fixed {
    text-center: center;
    padding: 4px;
}

/* Livewire specific fixes */
.livewire-csp-fix-0,
.livewire-csp-fix-1,
.livewire-csp-fix-2,
.livewire-csp-fix-3,
.livewire-csp-fix-4 {
    /* Placeholder for dynamically generated Livewire styles */
}

/* Generic CSP fix classes */
.csp-fix-style-0,
.csp-fix-style-1,
.csp-fix-style-2,
.csp-fix-style-3,
.csp-fix-style-4,
.csp-fix-style-5,
.csp-fix-style-6,
.csp-fix-style-7,
.csp-fix-style-8,
.csp-fix-style-9 {
    /* Placeholder for dynamically generated styles */
}

/* Sign-in specific fixes */
.signin-fix-0,
.signin-fix-1,
.signin-fix-2,
.signin-fix-3,
.signin-fix-4 {
    /* Placeholder for sign-in specific styles */
}

/* Additional common inline style fixes */
.display-none-fixed {
    display: none !important;
}

.display-block-fixed {
    display: block !important;
}

.display-flex-fixed {
    display: flex !important;
}

.position-relative-fixed {
    position: relative !important;
}

.position-absolute-fixed {
    position: absolute !important;
}

.width-100-fixed {
    width: 100% !important;
}

.height-100-fixed {
    height: 100% !important;
}

.text-center-fixed {
    text-align: center !important;
}

.color-white-fixed {
    color: white !important;
}

.color-red-fixed {
    color: red !important;
}

.background-black-fixed {
    background: #000 !important;
}

.cursor-pointer-fixed {
    cursor: pointer !important;
}

.z-index-1000-fixed {
    z-index: 1000 !important;
}

.z-index-1001-fixed {
    z-index: 1001 !important;
}

/* Responsive fixes */
@media (max-width: 768px) {
    .oblique-image-fixed {
        display: none;
    }
    
    .video-container-fixed {
        height: 250px;
    }
    
    .loading-placeholder-fixed {
        padding-top: 100px;
    }
}

/* Print styles */
@media print {
    .video-overlay-fixed,
    .custom-controls-fixed {
        display: none !important;
    }
}