/**
 * Immediate CSP Fix
 * Runs as early as possible to prevent CSP violations
 */

(function() {
    'use strict';
    
    // Get nonce from meta tag or current script
    let nonce = document.querySelector('meta[name="csp-nonce"]')?.getAttribute('content');
    if (!nonce && document.currentScript) {
        nonce = document.currentScript.getAttribute('nonce');
    }
    
    if (!nonce) {
        console.error('Immediate CSP Fix: No nonce available');
        return;
    }
    
    console.log('Immediate CSP Fix: Starting with nonce:', nonce);
    
    let counter = 0;
    
    // Function to fix violations immediately
    function fixViolationsNow() {
        try {
            // Fix inline styles
            const elementsWithStyle = document.querySelectorAll('[style]');
            elementsWithStyle.forEach(element => {
                const inlineStyle = element.getAttribute('style');
                if (inlineStyle && inlineStyle.trim()) {
                    element.removeAttribute('style');
                    const className = 'icf-' + Date.now() + '-' + (++counter);
                    element.classList.add(className);
                    
                    const style = document.createElement('style');
                    style.setAttribute('nonce', nonce);
                    style.textContent = '.' + className + ' { ' + inlineStyle + ' }';
                    document.head.appendChild(style);
                }
            });
            
            // Fix inline event handlers
            const eventTypes = ['click', 'change', 'submit', 'load', 'error', 'mouseover', 'mouseout', 'focus', 'blur'];
            eventTypes.forEach(eventType => {
                const handlerAttr = 'on' + eventType;
                const elements = document.querySelectorAll('[' + handlerAttr + ']');
                elements.forEach(element => {
                    const handlerCode = element.getAttribute(handlerAttr);
                    if (handlerCode && handlerCode.trim()) {
                        try {
                            element.removeAttribute(handlerAttr);
                            element.addEventListener(eventType, function(event) {
                                try {
                                    const func = new Function('event', handlerCode);
                                    func.call(this, event);
                                } catch (error) {
                                    console.error('CSP Fix: Error executing event handler:', error);
                                }
                            });
                        } catch (error) {
                            console.error('CSP Fix: Error fixing event handler:', error);
                        }
                    }
                });
            });
            
            // Fix scripts without nonce
            const scriptsWithoutNonce = document.querySelectorAll('script:not([src]):not([nonce])');
            scriptsWithoutNonce.forEach(script => {
                script.setAttribute('nonce', nonce);
            });
            
            // Fix styles without nonce
            const stylesWithoutNonce = document.querySelectorAll('style:not([nonce])');
            stylesWithoutNonce.forEach(style => {
                // Only add nonce to styles that aren't from external sources
                if (!style.textContent || !style.textContent.includes('/*# sourceMappingURL=')) {
                    style.setAttribute('nonce', nonce);
                }
            });
            
        } catch (error) {
            console.error('Immediate CSP Fix: Error during fix:', error);
        }
    }
    
    // Run immediately
    fixViolationsNow();
    
    // Set up observer
    if (typeof MutationObserver !== 'undefined') {
        const observer = new MutationObserver(function(mutations) {
            fixViolationsNow();
        });
        
        observer.observe(document.documentElement, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'onclick', 'onchange', 'onsubmit', 'onload', 'onerror', 'onmouseover', 'onmouseout', 'onfocus', 'onblur']
        });
    }
    
    // Run moderately for first 3 seconds since we have specific Livewire fixes
    const interval = setInterval(fixViolationsNow, 50);
    setTimeout(() => {
        clearInterval(interval);
        console.log('Immediate CSP Fix: Periodic fixing stopped');
    }, 3000);
    
    // Expose global function
    window.immediateCSPFix = {
        fix: fixViolationsNow,
        nonce: nonce
    };
    
    console.log('Immediate CSP Fix: Initialized');
    
})();