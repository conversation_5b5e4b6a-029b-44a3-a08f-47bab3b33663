<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;

class CspReportController extends Controller
{
    /**
     * Handle CSP violation reports.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function report(Request $request)
    {
        // Get the CSP report from the request
        $report = json_decode($request->getContent(), true);
        
        // Extract the 'csp-report' if it exists
        $cspReport = $report['csp-report'] ?? $report;
        
        if (empty($cspReport)) {
            return response()->json(['status' => 'error', 'message' => 'Invalid CSP report format'], 400);
        }

        // Generate a unique key for this violation type
        $violationKey = $this->getViolationKey($cspReport);
        
        // Log the violation
        if (config('csp.reporting.log', true)) {
            Log::channel('csp')->warning('CSP Violation', [
                'report' => $cspReport,
                'violation_key' => $violationKey,
            ]);
        }

        // Check if we should send an alert for this violation
        $this->handleViolationAlert($violationKey, $cspReport);

        return response()->json(['status' => 'ok']);
    }

    /**
     * Generate a unique key for this violation type.
     *
     * @param  array  $report
     * @return string
     */
    protected function getViolationKey(array $report): string
    {
        // Create a key based on the violated directive and blocked URI
        $directive = $report['violated-directive'] ?? $report['effective-directive'] ?? 'unknown';
        $blockedUri = $report['blocked-uri'] ?? 'unknown';
        
        return md5($directive . '|' . $blockedUri);
    }

    /**
     * Handle violation alerting based on thresholds.
     *
     * @param  string  $violationKey
     * @param  array  $report
     * @return void
     */
    protected function handleViolationAlert(string $violationKey, array $report): void
    {
        // Skip if alerting is disabled
        if (!config('csp.reporting.enabled', true) || !config('csp.reporting.email')) {
            return;
        }

        $cacheKey = 'csp_violation:' . $violationKey;
        $threshold = config('csp.reporting.threshold', 10);
        $throttle = config('csp.reporting.throttle', 60); // minutes
        
        // Increment the violation count
        $count = Cache::get($cacheKey, 0) + 1;
        Cache::put($cacheKey, $count, now()->addMinutes($throttle));
        
        // Check if we've hit the threshold and haven't sent an alert recently
        $alertSentKey = 'csp_alert_sent:' . $violationKey;
        
        if ($count >= $threshold && !Cache::has($alertSentKey)) {
            // Send alert email
            $this->sendAlertEmail($report, $count);
            
            // Mark that we've sent an alert for this violation
            Cache::put($alertSentKey, true, now()->addMinutes($throttle));
        }
    }

    /**
     * Send an alert email about CSP violations.
     *
     * @param  array  $report
     * @param  int  $count
     * @return void
     */
    protected function sendAlertEmail(array $report, int $count): void
    {
        $emailTo = config('csp.reporting.email');
        
        if (!$emailTo) {
            return;
        }
        
        // In a real implementation, you would send an email here
        // For now, we'll just log that we would have sent an email
        Log::channel('csp')->alert('CSP Violation Alert Email', [
            'to' => $emailTo,
            'count' => $count,
            'report' => $report,
        ]);
        
        // Example of how you might send an actual email:
        /*
        Mail::raw(
            "CSP Violation Alert\n\n" .
            "Count: {$count}\n" .
            "Directive: {$report['violated-directive'] ?? 'unknown'}\n" .
            "Blocked URI: {$report['blocked-uri'] ?? 'unknown'}\n" .
            "Document URI: {$report['document-uri'] ?? 'unknown'}\n\n" .
            "Full Report: " . json_encode($report, JSON_PRETTY_PRINT),
            function ($message) use ($emailTo, $report) {
                $message->to($emailTo)
                    ->subject('CSP Violation Alert: ' . ($report['violated-directive'] ?? 'unknown'));
            }
        );
        */
    }
}