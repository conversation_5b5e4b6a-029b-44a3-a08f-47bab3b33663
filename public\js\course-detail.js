// Course Detail Page JavaScript

// Global variables
let currentCourseId = null;
let currentLectureId = null;
let attachments = {
    images: [],
    pdfs: [],
    voice: null
};
let mediaRecorder = null;
let recordingChunks = [];

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

function initializePage() {
    // Get course ID from URL or data attribute
    const pathParts = window.location.pathname.split('/');
    currentCourseId = pathParts[pathParts.indexOf('my-course-detail') + 1];

    // Initialize all components
    initializeVideoProtection();
    initializeStarRating();
    initializeAttachments();
    initializeQuestionForm();
    initializeRatingForm();
    initializeLectureNavigation();

    // Load existing ratings and questions
    loadRatings();
    loadQuestions();

    // Handle dynamic progress bar widths
    const progressBars = document.querySelectorAll('.progress-bar[data-width]');
    progressBars.forEach(bar => {
        const width = bar.getAttribute('data-width');
        if (width) {
            bar.style.width = width + '%';
        }
    });
}

// Video Protection Functions
function initializeVideoProtection() {
    // Apply protection to all video elements
    document.querySelectorAll('iframe, video').forEach(function(mediaElement) {
        // Add protection class
        const container = mediaElement.closest('.ratio') || mediaElement.parentElement;
        if (container) {
            container.classList.add('video-protected');
        }

        // Disable right-click context menu
        mediaElement.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            return false;
        });

        // Apply specific protection to HTML5 video
        if (mediaElement.tagName === 'VIDEO') {
            mediaElement.setAttribute('controlsList', 'nodownload nofullscreen noremoteplayback');
            mediaElement.setAttribute('disablePictureInPicture', 'true');
        }
    });

    // Global keyboard shortcut prevention
    document.addEventListener('keydown', function(e) {
        // Disable F12, Ctrl+Shift+I, Ctrl+U, Ctrl+S
        if (e.key === 'F12' ||
            (e.ctrlKey && e.shiftKey && e.key === 'I') ||
            (e.ctrlKey && e.key === 'u') ||
            (e.ctrlKey && e.key === 's')) {
            e.preventDefault();
            return false;
        }
    });

    // Disable drag and drop on videos
    document.querySelectorAll('iframe, video').forEach(function(element) {
        element.addEventListener('dragstart', function(e) {
            e.preventDefault();
        });
    });
}

// Star Rating Functions
function initializeStarRating() {
    const stars = document.querySelectorAll('.rating-star');
    const ratingInput = document.getElementById('rating-value');

    if (!stars.length || !ratingInput) return;

    stars.forEach(function(star, index) {
        star.addEventListener('mouseenter', function() {
            highlightStars(index + 1);
        });

        star.addEventListener('mouseleave', function() {
            const currentRating = parseInt(ratingInput.value) || 0;
            highlightStars(currentRating);
        });

        star.addEventListener('click', function() {
            const rating = index + 1;
            ratingInput.value = rating;
            highlightStars(rating);
        });
    });
}

function highlightStars(rating) {
    const stars = document.querySelectorAll('.rating-star');
    stars.forEach(function(star, index) {
        if (index < rating) {
            star.classList.remove('far');
            star.classList.add('fas', 'active');
        } else {
            star.classList.remove('fas', 'active');
            star.classList.add('far');
        }
    });
}

// Attachment Functions
function initializeAttachments() {
    // Image upload
    const imageBtn = document.getElementById('attach-image-btn');
    const imageInput = document.getElementById('image-upload');

    if (imageBtn && imageInput) {
        imageBtn.addEventListener('click', () => imageInput.click());
        imageInput.addEventListener('change', handleImageUpload);
    }

    // PDF upload
    const pdfBtn = document.getElementById('attach-pdf-btn');
    const pdfInput = document.getElementById('pdf-upload');

    if (pdfBtn && pdfInput) {
        pdfBtn.addEventListener('click', () => pdfInput.click());
        pdfInput.addEventListener('change', handlePdfUpload);
    }

    // Voice recording
    const startRecordingBtn = document.getElementById('start-recording-btn');
    const stopRecordingBtn = document.getElementById('stop-recording-btn');

    if (startRecordingBtn && stopRecordingBtn) {
        startRecordingBtn.addEventListener('click', startRecording);
        stopRecordingBtn.addEventListener('click', stopRecording);
    }
}

function handleImageUpload(event) {
    const files = Array.from(event.target.files);
    files.forEach(file => {
        if (file.type.startsWith('image/')) {
            attachments.images.push(file);
        }
    });
    updateAttachmentPreview();
}

function handlePdfUpload(event) {
    const files = Array.from(event.target.files);
    files.forEach(file => {
        if (file.type === 'application/pdf') {
            attachments.pdfs.push(file);
        }
    });
    updateAttachmentPreview();
}

async function startRecording() {
    try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        mediaRecorder = new MediaRecorder(stream);
        recordingChunks = [];

        mediaRecorder.ondataavailable = function(event) {
            if (event.data.size > 0) {
                recordingChunks.push(event.data);
            }
        };

        mediaRecorder.onstop = function() {
            const blob = new Blob(recordingChunks, { type: 'audio/wav' });
            attachments.voice = blob;
            updateAttachmentPreview();

            // Stop all tracks
            stream.getTracks().forEach(track => track.stop());
        };

        mediaRecorder.start();

        // Update UI
        document.getElementById('start-recording-btn').classList.add('hidden');
        document.getElementById('stop-recording-btn').classList.remove('d-none');
        document.getElementById('start-recording-btn').classList.add('recording-animation');

    } catch (error) {
        console.error('Error starting recording:', error);
        alert('Could not start recording. Please check your microphone permissions.');
    }
}

function stopRecording() {
    if (mediaRecorder && mediaRecorder.state === 'recording') {
        mediaRecorder.stop();
    }

    // Update UI
    document.getElementById('start-recording-btn').classList.remove('hidden', 'recording-animation');
    document.getElementById('stop-recording-btn').classList.add('d-none');
}

function updateAttachmentPreview() {
    const previewContainer = document.getElementById('attachments-preview');
    const attachmentList = document.getElementById('attachment-list');

    if (!previewContainer || !attachmentList) return;

    // Clear existing previews
    attachmentList.innerHTML = '';

    // Show/hide preview container
    const hasAttachments = attachments.images.length > 0 || attachments.pdfs.length > 0 || attachments.voice;
    if (hasAttachments) {
        previewContainer.classList.remove('d-none');
    } else {
        previewContainer.classList.add('d-none');
        return;
    }

    // Add image previews
    attachments.images.forEach((image, index) => {
        const imgPreview = document.createElement('div');
        imgPreview.className = 'attachment-preview';
        imgPreview.innerHTML = `
            <div class="card card-preview-small">
                <img src="${URL.createObjectURL(image)}" class="card-img-top" alt="Image preview">
                <div class="card-footer p-1">
                    <button type="button" class="btn btn-sm btn-danger w-100 remove-attachment" data-type="image" data-index="${index}">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
        attachmentList.appendChild(imgPreview);
    });

    // Add PDF previews
    attachments.pdfs.forEach((pdf, index) => {
        const pdfPreview = document.createElement('div');
        pdfPreview.className = 'attachment-preview';
        pdfPreview.innerHTML = `
            <div class="card card-preview-small">
                <div class="card-body p-2 text-center">
                    <i class="fas fa-file-pdf fa-2x text-danger"></i>
                    <p class="mb-0 small text-truncate">${pdf.name}</p>
                </div>
                <div class="card-footer p-1">
                    <button type="button" class="btn btn-sm btn-danger w-100 remove-attachment" data-type="pdf" data-index="${index}">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
        attachmentList.appendChild(pdfPreview);
    });

    // Add voice preview
    if (attachments.voice) {
        const voicePreview = document.createElement('div');
        voicePreview.className = 'attachment-preview';
        voicePreview.innerHTML = `
            <div class="card card-preview-medium">
                <div class="card-body p-2">
                    <div class="d-flex align-items-center gap-2">
                        <i class="fas fa-microphone text-primary"></i>
                        <audio controls class="audio-controls">
                            <source src="${URL.createObjectURL(attachments.voice)}" type="${attachments.voice.type}">
                        </audio>
                    </div>
                </div>
                <div class="card-footer p-1">
                    <button type="button" class="btn btn-sm btn-danger w-100 remove-attachment" data-type="voice">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
        attachmentList.appendChild(voicePreview);
    }

    // Add event listeners for remove buttons
    attachmentList.querySelectorAll('.remove-attachment').forEach(button => {
        button.addEventListener('click', function() {
            const type = this.dataset.type;
            const index = parseInt(this.dataset.index);

            if (type === 'image') {
                attachments.images.splice(index, 1);
            } else if (type === 'pdf') {
                attachments.pdfs.splice(index, 1);
            } else if (type === 'voice') {
                attachments.voice = null;
            }

            updateAttachmentPreview();
        });
    });
}

// Placeholder functions for other features
function initializeQuestionForm() {
    // Question form initialization
    const submitBtn = document.getElementById('submit-question');
    if (submitBtn) {
        submitBtn.addEventListener('click', submitQuestion);
    }
}

function initializeRatingForm() {
    // Rating form initialization
    const submitBtn = document.getElementById('submit-rating');
    if (submitBtn) {
        submitBtn.addEventListener('click', submitRating);
    }
}

function initializeLectureNavigation() {
    // Lecture navigation initialization
    // This would handle lecture switching logic
}

function loadRatings() {
    // Load existing ratings via AJAX
}

function loadQuestions() {
    // Load existing questions via AJAX
}

function submitQuestion() {
    // Submit question with attachments
}

function submitRating() {
    // Submit rating
}
