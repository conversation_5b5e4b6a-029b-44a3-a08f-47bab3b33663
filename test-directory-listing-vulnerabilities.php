<?php
/**
 * Directory Listing Vulnerability Test
 * Tests the specific directories mentioned in the security report
 */

$baseUrl = 'http://127.0.0.1:8000';

// Exact directories from the security report
$vulnerableDirectories = [
    '/assets/',
    '/assets/css/',
    '/assets/fonts/',
    '/assets/img/',
    '/assets/img/logos/',
    '/assets/img/small-logos/',
    '/assets/js/',
    '/assets/js/core/',
    '/assets/js/plugins/',
    '/assets/scss/',
    '/storage/'
];

echo "🔍 DIRECTORY LISTING VULNERABILITY TEST\n";
echo "=======================================\n";
echo "Testing directories from security report...\n\n";

$totalTests = 0;
$passedTests = 0;
$failedTests = 0;

function testDirectoryListing($url, $directory, &$totalTests, &$passedTests, &$failedTests) {
    echo "Testing: $directory\n";
    echo "URL: $url\n";
    
    $totalTests++;
    
    // Initialize cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Security Test)');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "  ❌ Connection Error: $error\n";
        $failedTests++;
        return false;
    }
    
    // Check response content for directory listing indicators
    $isDirectoryListing = false;
    if ($httpCode === 200) {
        $responseBody = curl_exec(curl_init($url));
        curl_setopt($ch = curl_init(), CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
        $responseBody = curl_exec($ch);
        curl_close($ch);
        
        // Check for common directory listing indicators
        $directoryListingIndicators = [
            'Index of /',
            'Directory listing for',
            'Parent Directory',
            '<title>Index of',
            'Directory Listing',
            '[DIR]',
            'Last modified'
        ];
        
        foreach ($directoryListingIndicators as $indicator) {
            if (stripos($responseBody, $indicator) !== false) {
                $isDirectoryListing = true;
                break;
            }
        }
    }
    
    switch ($httpCode) {
        case 200:
            if ($isDirectoryListing) {
                echo "  ❌ VULNERABILITY - Directory listing exposed (200 OK)\n";
                $failedTests++;
                return false;
            } else {
                echo "  ✅ SECURE - Returns content but not directory listing (200 OK)\n";
                $passedTests++;
                return true;
            }
        case 403:
            echo "  ✅ SECURE - Access forbidden (403)\n";
            $passedTests++;
            return true;
        case 404:
            echo "  ✅ SECURE - Not found (404)\n";
            $passedTests++;
            return true;
        case 302:
        case 301:
            // Check redirect location
            if (preg_match('/Location: (.+)/i', $response, $matches)) {
                $location = trim($matches[1]);
                if (strpos($location, '/') === 0 || strpos($location, $GLOBALS['baseUrl']) === 0) {
                    echo "  ✅ SECURE - Redirects to safe location: $location\n";
                    $passedTests++;
                    return true;
                } else {
                    echo "  ❌ VULNERABILITY - Redirects to external location: $location\n";
                    $failedTests++;
                    return false;
                }
            }
            echo "  ✅ SECURE - Redirects (location unknown)\n";
            $passedTests++;
            return true;
        case 500:
            echo "  ✅ SECURE - Server error (500) - Likely blocked\n";
            $passedTests++;
            return true;
        default:
            echo "  ⚠️  HTTP $httpCode - Needs manual review\n";
            $failedTests++;
            return false;
    }
}

// Test each vulnerable directory
foreach ($vulnerableDirectories as $directory) {
    $url = $baseUrl . $directory;
    testDirectoryListing($url, $directory, $totalTests, $passedTests, $failedTests);
    echo "\n";
}

// Additional comprehensive tests
echo "🔍 ADDITIONAL DIRECTORY TESTS\n";
echo "=============================\n";

$additionalTests = [
    '/public/',
    '/build/',
    '/vendor/',
    '/app/',
    '/config/',
    '/database/',
    '/resources/',
    '/routes/',
    '/tests/'
];

foreach ($additionalTests as $directory) {
    $url = $baseUrl . $directory;
    testDirectoryListing($url, $directory, $totalTests, $passedTests, $failedTests);
    echo "\n";
}

// Final Report
echo "📊 DIRECTORY LISTING TEST SUMMARY\n";
echo "=================================\n";
echo "Total Tests: $totalTests\n";
echo "Passed (Secure): $passedTests ✅\n";
echo "Failed (Vulnerable): $failedTests ❌\n";
echo "Success Rate: " . round(($passedTests / $totalTests) * 100, 1) . "%\n\n";

if ($failedTests === 0) {
    echo "🎉 ALL DIRECTORY LISTING TESTS PASSED!\n";
    echo "No directory listing vulnerabilities detected.\n";
    echo "Your application is secure against directory listing attacks.\n";
} else {
    echo "⚠️  DIRECTORY LISTING VULNERABILITIES DETECTED!\n";
    echo "Please review the failed tests above.\n";
    echo "Consider adding index.php files or server-level protection.\n";
}

echo "\nSecurity Status:\n";
echo "✅ = Directory properly protected\n";
echo "❌ = Directory listing vulnerability detected\n";
echo "⚠️  = Needs manual review\n";
?>
