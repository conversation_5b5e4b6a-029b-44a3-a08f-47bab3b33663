/**
 * Enhanced Livewire CSP Patch
 * This script provides a comprehensive fix for Livewire CSP issues
 * with special handling for inline styles and scripts
 */

(function() {
    // Execute immediately to patch before DOM content loaded
    // Get CSP nonce from meta tag
    const nonceMetaTag = document.querySelector('meta[name="csp-nonce"]');
    const nonce = nonceMetaTag ? nonceMetaTag.getAttribute('content') : null;
    
    if (!nonce) {
        console.warn('CSP nonce not found in meta tag');
        return;
    }
    
    // Create a container for Livewire styles with nonce
    const livewireStyleContainer = document.createElement('style');
    livewireStyleContainer.setAttribute('nonce', nonce);
    livewireStyleContainer.setAttribute('id', 'livewire-styles-container');
    document.head.appendChild(livewireStyleContainer);
    
    // Create a container for dynamic scripts with nonce
    const dynamicScriptContainer = document.createElement('script');
    dynamicScriptContainer.setAttribute('nonce', nonce);
    dynamicScriptContainer.setAttribute('id', 'dynamic-scripts-container');
    document.head.appendChild(dynamicScriptContainer);
    
    // Override createElement to add nonce to style and script elements
    const originalCreateElement = document.createElement;
    document.createElement = function(tagName) {
        const element = originalCreateElement.apply(document, arguments);
        
        if (tagName.toLowerCase() === 'style' || tagName.toLowerCase() === 'script') {
            element.setAttribute('nonce', nonce);
        }
        
        return element;
    };
    
    // Function to handle Livewire styles
    function handleLivewireStyles() {
        // Find all style elements without nonce
        const styleElements = document.querySelectorAll('style:not([nonce])');
        
        styleElements.forEach(style => {
            // Option 1: Add nonce to the style element
            style.setAttribute('nonce', nonce);
            
            // Option 2: Move content to our nonced container
            // Uncomment this if Option 1 doesn't work
            /*
            if (style.textContent) {
                livewireStyleContainer.textContent += style.textContent + '\n';
                if (style.parentNode) {
                    style.parentNode.removeChild(style);
                }
            }
            */
        });
    }
    
    // Function to handle inline scripts
    function handleInlineScripts() {
        // Find all script elements without nonce
        const scriptElements = document.querySelectorAll('script:not([nonce]):not([src])');
        
        scriptElements.forEach(script => {
            // Option 1: Add nonce to the script element
            script.setAttribute('nonce', nonce);
            
            // Option 2: Move content to our nonced container
            // Uncomment this if Option 1 doesn't work
            /*
            if (script.textContent) {
                // Create a new script element with the nonce
                const newScript = document.createElement('script');
                newScript.textContent = script.textContent;
                
                // Replace the old script with the new one
                if (script.parentNode) {
                    script.parentNode.replaceChild(newScript, script);
                }
            }
            */
        });
    }
    
    // Special handling for the dashboard route script
    function handleDashboardRouteScript() {
        // Instead of using :contains, use querySelectorAll and check textContent
        const scripts = document.querySelectorAll('script:not([nonce])');
        scripts.forEach(script => {
            if (script.textContent && script.textContent.includes('window.dashboardRoute')) {
                script.setAttribute('nonce', nonce);
            }
        });
    }
    
    // Function to run all handlers
    function runAllHandlers() {
        handleLivewireStyles();
        handleInlineScripts();
        handleDashboardRouteScript();
    }
    
    // Run immediately
    runAllHandlers();
    
    // Set up a MutationObserver to watch for new elements
    const observer = new MutationObserver(mutations => {
        let needsUpdate = false;
        
        mutations.forEach(mutation => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) {
                        // Check if the node is a style or script element
                        if ((node.tagName === 'STYLE' || node.tagName === 'SCRIPT') && !node.hasAttribute('nonce')) {
                            needsUpdate = true;
                        }
                        
                        // Check for style or script elements within the added node
                        if (node.querySelectorAll) {
                            const elements = node.querySelectorAll('style:not([nonce]), script:not([nonce])');
                            if (elements.length > 0) {
                                needsUpdate = true;
                            }
                        }
                    }
                });
            }
        });
        
        if (needsUpdate) {
            runAllHandlers();
        }
    });
    
    // Start observing the document with all possible mutation types
    observer.observe(document.documentElement, {
        childList: true,
        subtree: true,
        attributes: true,
        characterData: true
    });
    
    // Special handling for Livewire
    if (typeof window.Livewire !== 'undefined') {
        console.log('Livewire detected, applying enhanced CSP patches');
        
        // Patch Livewire's style injection
        const originalLivewireStyles = window.Livewire.styles;
        window.Livewire.styles = function() {
            const styles = originalLivewireStyles ? originalLivewireStyles() : '';
            if (styles) {
                livewireStyleContainer.textContent += styles;
                return '';
            }
            return styles;
        };
    }
    
    // Add event listeners for Livewire events
    document.addEventListener('livewire:load', runAllHandlers);
    document.addEventListener('livewire:update', runAllHandlers);
    
    // Run again when DOM is fully loaded
    document.addEventListener('DOMContentLoaded', runAllHandlers);
    
    // Run periodically to catch any missed elements
    setInterval(runAllHandlers, 500);
    
    // Special fix for querySelector to handle :contains selector
    if (!Element.prototype.matches) {
        Element.prototype.matches = Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;
    }
    
    if (!Document.prototype._querySelector) {
        Document.prototype._querySelector = Document.prototype.querySelector;
        Document.prototype.querySelector = function(selector) {
            if (selector.includes(':contains(')) {
                const containsMatch = selector.match(/:contains\((['"])(.*?)\1\)/);
                if (containsMatch && containsMatch[2]) {
                    const textToFind = containsMatch[2];
                    const baseSelector = selector.replace(/:contains\((['"])(.*?)\1\)/, '');
                    
                    const elements = this.querySelectorAll(baseSelector);
                    for (let i = 0; i < elements.length; i++) {
                        if (elements[i].textContent.includes(textToFind)) {
                            return elements[i];
                        }
                    }
                    return null;
                }
            }
            return this._querySelector(selector);
        };
    }
})();