<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Course;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * Display the dashboard page for both guests and authenticated users
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // If user is authenticated, handle verification
        if (Auth::check()) {
            $user = Auth::user();
            
            // If user is not verified, redirect to verification
            if (!$user->email_verified_at || !$user->phone_verified_at) {
                return redirect()->route('verification.notice');
            }
        }

        // Get the most recent courses for display
        try {
            $latestCourses = Course::with(['ratings' => function($query) {
                $query->where('is_approved', true)
                      ->where('show_publicly', true);
            }])->latest()->take(6)->get();
        } catch (\Exception $e) {
            // If there's an issue with courses, just use empty collection
            $latestCourses = collect();
        }

        return view('dashboard', compact('latestCourses'));
    }

    /**
     * Display the authenticated user's dashboard
     *
     * @return \Illuminate\View\View
     */
    public function authenticatedIndex()
    {
        $user = Auth::user();

        // Check if user has any purchases
        $hasPurchases = \App\Models\UserCourse::where('user_id', $user->id)
            ->where('status', 'active')
            ->exists();

        if ($hasPurchases) {
            return redirect()->route('user.dashboard');
        }

        // Get the most recent courses
        $latestCourses = Course::with(['ratings' => function($query) {
            $query->where('is_approved', true)
                  ->where('show_publicly', true);
        }])->latest()->take(6)->get();

        return view('dashboard', compact('latestCourses'));
    }
}
