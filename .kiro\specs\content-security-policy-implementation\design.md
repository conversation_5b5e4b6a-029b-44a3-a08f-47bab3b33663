# Design Document: Content Security Policy Implementation

## Overview

This design document outlines the approach for implementing a secure Content Security Policy (CSP) in the IEC Courses Portal application. The implementation will focus on removing unsafe CSP directives (`unsafe-inline` and `unsafe-eval`) by extracting all inline CSS and JavaScript into external files, implementing nonces or hashes where necessary, and configuring strict CSP headers.

## Architecture

The solution will follow a layered approach:

1. **Middleware Layer**: Enhance the existing CSP middleware to implement strict policies without unsafe directives
2. **Asset Management Layer**: Extract and organize inline CSS and JavaScript into external files
3. **View Layer**: Modify templates to reference external resources instead of inline code
4. **Reporting Layer**: Implement CSP violation reporting and monitoring

## Components and Interfaces

### 1. CSP Middleware Component

The existing middleware classes (`ContentSecurityPolicy.php`, `ForceCSP.php`, and `SecurityHeaders.php`) will be consolidated into a single, comprehensive CSP middleware that:

- Generates secure nonces for each request
- Applies appropriate CSP headers based on environment
- Handles CSP reporting
- Provides helper methods for views to access nonces

```php
// Enhanced CSP Middleware Interface
class ContentSecurityPolicy
{
    protected $nonce;
    
    public function __construct()
    {
        $this->nonce = $this->generateNonce();
    }
    
    public function handle($request, $next)
    {
        // Add nonce to view for use in templates
        // Generate and apply CSP headers
        // Return response
    }
    
    protected function generateNonce()
    {
        // Generate cryptographically secure nonce
    }
    
    protected function buildCspPolicy()
    {
        // Build CSP policy based on configuration
    }
}
```

### 2. Asset Management Component

A new asset management system will be implemented to:

- Organize extracted CSS and JavaScript files
- Handle dynamic script generation when needed
- Provide helper functions for views to include external resources

```php
// Asset Manager Interface
class AssetManager
{
    public function registerStyle($name, $path, $dependencies = [])
    {
        // Register stylesheet
    }
    
    public function registerScript($name, $path, $dependencies = [])
    {
        // Register script
    }
    
    public function enqueueStyle($name)
    {
        // Enqueue stylesheet for current request
    }
    
    public function enqueueScript($name)
    {
        // Enqueue script for current request
    }
    
    public function renderStyles()
    {
        // Output style tags in correct order
    }
    
    public function renderScripts()
    {
        // Output script tags in correct order
    }
}
```

### 3. View Helpers Component

Blade directives and helper functions will be created to:

- Easily include external resources in templates
- Apply nonces to dynamic scripts when needed
- Maintain compatibility with existing view structure

```php
// Example Blade directives
@cspNonce // Outputs nonce attribute for script tags
@externalScript('script-name') // Includes external script
@externalStyle('style-name') // Includes external stylesheet
```

### 4. CSP Reporting Component

A reporting system will be implemented to:

- Collect and log CSP violations
- Aggregate similar violations
- Alert administrators about critical violations

```php
// CSP Reporter Interface
class CspReporter
{
    public function handleViolation($request)
    {
        // Process CSP violation report
        // Log violation details
        // Aggregate similar violations
        // Alert if necessary
    }
}
```

## Data Models

### CSP Configuration Model

```php
// CSP Configuration structure
[
    'default-src' => ['self'],
    'script-src' => ['self', 'cdn.jsdelivr.net', 'code.jquery.com', ...],
    'style-src' => ['self', 'fonts.googleapis.com', ...],
    'font-src' => ['self', 'fonts.gstatic.com', ...],
    'img-src' => ['self', 'data:', 'https:', ...],
    'connect-src' => ['self', 'api.ipify.org', ...],
    'frame-src' => ['self', 'www.youtube.com', ...],
    'report-uri' => '/csp-report',
    'report-to' => 'csp-endpoint',
    'upgrade-insecure-requests' => true,
]
```

### CSP Violation Report Model

```php
// CSP Violation Report structure
[
    'document-uri' => string,
    'referrer' => string,
    'blocked-uri' => string,
    'violated-directive' => string,
    'original-policy' => string,
    'disposition' => string,
    'source-file' => string,
    'line-number' => int,
    'column-number' => int,
    'status-code' => int,
]
```

## Error Handling

1. **CSP Violations**: All CSP violations will be logged with detailed information to help identify the source of the problem.

2. **Graceful Degradation**: For browsers that don't fully support CSP, the application will include fallback mechanisms to ensure functionality.

3. **Resource Loading Failures**: If external resources fail to load, the application will have fallback mechanisms to maintain core functionality.

4. **Nonce Generation Failures**: If nonce generation fails, the system will fall back to hash-based CSP for critical resources.

## Testing Strategy

### 1. Unit Testing

- Test CSP middleware functionality
- Test nonce generation and validation
- Test asset management components
- Test CSP violation reporting

### 2. Integration Testing

- Test interaction between middleware and views
- Test CSP headers in responses
- Test external resource loading

### 3. Security Testing

- Validate CSP effectiveness against XSS attacks
- Test CSP bypass techniques
- Verify CSP reporting functionality

### 4. Browser Compatibility Testing

- Test across all supported browsers
- Verify graceful degradation in older browsers

## Implementation Approach

### Phase 1: Extract Inline CSS

1. Identify all inline styles in templates
2. Create external CSS files for each component or page
3. Update templates to reference external stylesheets
4. Implement dynamic style loading where necessary

### Phase 2: Extract Inline JavaScript

1. Identify all inline scripts in templates
2. Create external JavaScript files for each component or page
3. Update templates to reference external script files
4. Implement event delegation instead of inline event handlers
5. Use nonces for any remaining dynamic scripts

### Phase 3: Remove 'unsafe-eval'

1. Identify code using eval() or new Function()
2. Refactor code to avoid dynamic evaluation
3. Implement alternatives for third-party libraries that require eval

### Phase 4: Implement Strict CSP

1. Configure CSP headers without unsafe directives
2. Implement nonce generation and distribution
3. Set up CSP reporting endpoints
4. Test CSP effectiveness

## CSP Header Configuration

The final CSP header will include the following directives:

```
Content-Security-Policy: 
    default-src 'self'; 
    script-src 'self' 'nonce-{random-nonce}' https://cdn.jsdelivr.net https://code.jquery.com https://cdn.plyr.io https://buttons.github.io https://www.youtube.com https://youtube.com https://www.youtube-nocookie.com https://maps.googleapis.com; 
    style-src 'self' 'nonce-{random-nonce}' https://fonts.googleapis.com https://fonts.gstatic.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://use.fontawesome.com https://cdn.plyr.io; 
    font-src 'self' https://fonts.googleapis.com https://fonts.gstatic.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://use.fontawesome.com data:; 
    img-src 'self' data: https:; 
    media-src 'self' data: blob:; 
    connect-src 'self' https://api.ipify.org https://noembed.com https://cdn.plyr.io https://api.github.com https://maps.googleapis.com; 
    frame-src 'self' https://www.youtube.com https://youtube.com https://youtu.be https://www.youtube-nocookie.com; 
    object-src 'none'; 
    base-uri 'self'; 
    form-action 'self'; 
    report-uri /csp-report; 
    report-to csp-endpoint;
```

## Diagrams

### CSP Implementation Flow

```mermaid
flowchart TD
    A[Request] --> B[CSP Middleware]
    B --> C{Generate Nonce}
    C --> D[Add Nonce to View]
    D --> E[Apply CSP Headers]
    E --> F[Response]
    F --> G[Browser]
    G --> H{CSP Violation?}
    H -- Yes --> I[Report Violation]
    I --> J[Log Violation]
    J --> K[Alert if Critical]
    H -- No --> L[Render Page]
```

### Asset Loading Process

```mermaid
flowchart LR
    A[View Template] --> B{Need External Resource?}
    B -- Yes --> C[Asset Manager]
    C --> D[Load External CSS]
    C --> E[Load External JS]
    D --> F[Apply Nonce if Dynamic]
    E --> F
    F --> G[Render Page]
    B -- No --> G
```

## Security Considerations

1. **Nonce Regeneration**: Nonces will be regenerated for each request to prevent replay attacks.

2. **Strict CSP**: The CSP will be configured to be as strict as possible while maintaining functionality.

3. **Subresource Integrity**: SRI will be implemented for external resources where possible.

4. **CSP Reporting**: All CSP violations will be logged and monitored to identify potential attacks.

5. **Defense in Depth**: CSP will be one layer of a comprehensive security strategy, including other headers like X-XSS-Protection and X-Content-Type-Options.

## Compatibility Considerations

1. **Browser Support**: The implementation will consider varying levels of CSP support across browsers.

2. **Legacy Code**: Special attention will be paid to legacy code that might rely on inline scripts or eval().

3. **Third-Party Libraries**: Libraries that require unsafe CSP directives will be identified and alternatives will be considered.

4. **Performance Impact**: The impact of external resource loading on performance will be minimized through proper caching and optimization.