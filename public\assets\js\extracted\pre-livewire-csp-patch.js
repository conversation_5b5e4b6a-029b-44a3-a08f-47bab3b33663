/**
 * Pre-Livewire CSP Patch
 * Patches DOM methods before Livewire loads to ensure all styles get nonces
 */

(function() {
    'use strict';
    
    // Get nonce immediately
    const nonce = document.querySelector('meta[name="csp-nonce"]')?.getAttribute('content') || 
                  document.currentScript?.getAttribute('nonce');
    
    if (!nonce) {
        console.error('Pre-Livewire CSP Patch: No nonce available');
        return;
    }
    
    console.log('Pre-Livewire CSP Patch: Patching DOM methods with nonce:', nonce);
    
    // Store original methods
    const originalMethods = {
        createElement: document.createElement,
        appendChild: Node.prototype.appendChild,
        insertBefore: Node.prototype.insertBefore,
        replaceChild: Node.prototype.replaceChild
    };
    
    // Function to add nonce to style elements
    function addNonceToStyle(element) {
        if (element && element.tagName === 'STYLE' && !element.hasAttribute('nonce')) {
            element.setAttribute('nonce', nonce);
            return true;
        }
        return false;
    }
    
    // Override document.createElement
    document.createElement = function(tagName) {
        const element = originalMethods.createElement.call(this, tagName);
        if (tagName.toLowerCase() === 'style') {
            element.setAttribute('nonce', nonce);
        }
        return element;
    };
    
    // Override appendChild
    Node.prototype.appendChild = function(child) {
        addNonceToStyle(child);
        return originalMethods.appendChild.call(this, child);
    };
    
    // Override insertBefore
    Node.prototype.insertBefore = function(newNode, referenceNode) {
        addNonceToStyle(newNode);
        return originalMethods.insertBefore.call(this, newNode, referenceNode);
    };
    
    // Override replaceChild
    Node.prototype.replaceChild = function(newChild, oldChild) {
        addNonceToStyle(newChild);
        return originalMethods.replaceChild.call(this, newChild, oldChild);
    };
    
    // Function to fix any existing styles
    function fixExistingStyles() {
        const styles = document.querySelectorAll('style:not([nonce])');
        let fixed = 0;
        
        styles.forEach(style => {
            style.setAttribute('nonce', nonce);
            fixed++;
        });
        
        if (fixed > 0) {
            console.log('Pre-Livewire CSP Patch: Fixed', fixed, 'existing styles');
        }
    }
    
    // Fix existing styles immediately
    fixExistingStyles();
    
    // Set up observer for any new styles
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'STYLE') {
                        addNonceToStyle(node);
                    }
                });
            }
        });
    });
    
    observer.observe(document.head, {
        childList: true,
        subtree: true
    });
    
    // Expose a function to restore original methods if needed
    window.restoreOriginalDOMMethods = function() {
        document.createElement = originalMethods.createElement;
        Node.prototype.appendChild = originalMethods.appendChild;
        Node.prototype.insertBefore = originalMethods.insertBefore;
        Node.prototype.replaceChild = originalMethods.replaceChild;
        observer.disconnect();
        console.log('Pre-Livewire CSP Patch: Original DOM methods restored');
    };
    
    console.log('Pre-Livewire CSP Patch: DOM methods patched successfully');
    
})();