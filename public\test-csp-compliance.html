<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSP Compliance Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .violation {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .warning {
            background-color: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #5a6fd8;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
        }
        .file-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #fafafa;
        }
        .file-item {
            padding: 5px;
            margin: 2px 0;
            background: white;
            border-radius: 3px;
            border-left: 3px solid #667eea;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4caf50, #8bc34a);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ CSP Compliance Test Suite</h1>
            <p>Test your Laravel application for Content Security Policy violations</p>
        </div>

        <div class="stats" id="stats">
            <div class="stat-card">
                <div class="stat-number" id="total-files">0</div>
                <div class="stat-label">Total View Files</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="inline-styles">0</div>
                <div class="stat-label">Inline Styles Found</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="inline-scripts">0</div>
                <div class="stat-label">Inline Scripts Found</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="event-handlers">0</div>
                <div class="stat-label">Event Handlers Found</div>
            </div>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
        </div>
        <div style="text-align: center; margin: 10px 0;">
            <span id="progress-text">Ready to test</span>
        </div>

        <div class="test-section">
            <h3>🔍 Quick Tests</h3>
            <button class="test-button" onclick="testInlineStyles()">Test Inline Styles</button>
            <button class="test-button" onclick="testInlineScripts()">Test Inline Scripts</button>
            <button class="test-button" onclick="testEventHandlers()">Test Event Handlers</button>
            <button class="test-button" onclick="testAllViolations()">Test All Violations</button>
            <button class="test-button" onclick="generateReport()">Generate Full Report</button>
        </div>

        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div class="results" id="results">
                <p>Click a test button above to see results...</p>
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 Page Testing</h3>
            <p>Test specific pages for console errors and CSP violations:</p>
            <div id="page-tests">
                <button class="test-button" onclick="testPage('/course/purchased-lecture-detail')">Test Lecture Detail</button>
                <button class="test-button" onclick="testPage('/admin/instructor-profiles')">Test Instructor Profiles</button>
                <button class="test-button" onclick="testPage('/account-pages/signup')">Test Signup Page</button>
                <button class="test-button" onclick="testPage('/admin/assignments')">Test Admin Assignments</button>
                <button class="test-button" onclick="testPage('/admin/questions')">Test Admin Questions</button>
                <button class="test-button" onclick="testPage('/welcome')">Test Welcome Page</button>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 Detailed File Analysis</h3>
            <div class="file-list" id="file-analysis">
                <p>Run "Generate Full Report" to see detailed file analysis...</p>
            </div>
        </div>
    </div>

    <script>
        // CSP Compliance Test Functions

        let scanResults = null;

        // Real API calls to PHP scanner
        async function fetchScanResults() {
            try {
                const response = await fetch('/csp-scanner.php?action=scan');
                const data = await response.json();
                if (data.success) {
                    scanResults = data.data;
                    updateStatsFromAPI();
                    return data.data;
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                console.error('Error fetching scan results:', error);
                // Fallback to mock data
                return getMockData();
            }
        }

        function getMockData() {
            return {
                stats: {
                    total_files: 156,
                    inline_styles: 212,
                    inline_scripts: 24,
                    event_handlers: 34,
                    style_blocks: 24,
                    script_blocks: 15
                },
                violations: [
                    {type: 'inline_style', file: 'resources/views/RTL.blade.php', line: 360, severity: 'medium'},
                    {type: 'inline_style', file: 'resources/views/tables.blade.php', line: 10, severity: 'medium'},
                    {type: 'style_block', file: 'resources/views/admin/layout.blade.php', line: 23, severity: 'high'},
                    {type: 'event_handler', file: 'resources/views/admin/quizzes/edit.blade.php', line: 169, severity: 'high'}
                ],
                summary: {
                    total_violations: 285,
                    high_severity: 73,
                    medium_severity: 212,
                    files_with_violations: 45,
                    clean_files: 111,
                    compliance_percentage: 71.2
                }
            };
        }

        function updateStatsFromAPI() {
            if (!scanResults) return;

            document.getElementById('total-files').textContent = scanResults.stats.total_files;
            document.getElementById('inline-styles').textContent = scanResults.stats.inline_styles;
            document.getElementById('inline-scripts').textContent = scanResults.stats.script_blocks;
            document.getElementById('event-handlers').textContent = scanResults.stats.event_handlers;

            const progress = scanResults.summary.compliance_percentage;
            document.getElementById('progress-fill').style.width = progress + '%';
            document.getElementById('progress-text').textContent = `${progress}% Compliant (${scanResults.summary.clean_files}/${scanResults.stats.total_files} files clean)`;
        }

        async function updateStats() {
            if (!scanResults) {
                await fetchScanResults();
            }
            updateStatsFromAPI();
        }

        async function testInlineStyles() {
            const results = document.getElementById('results');
            results.innerHTML = '<h4>🎨 Inline Styles Test Results</h4><div class="warning">⏳ Scanning files...</div>';

            if (!scanResults) {
                await fetchScanResults();
            }

            const styleViolations = scanResults.violations.filter(v => v.type === 'inline_style');

            if (styleViolations.length > 0) {
                results.innerHTML = '<h4>🎨 Inline Styles Test Results</h4>';
                results.innerHTML += `<div class="violation"><strong>❌ ${styleViolations.length} Violations Found:</strong></div>`;
                styleViolations.slice(0, 10).forEach(violation => {
                    results.innerHTML += `<div class="violation">${violation.file}:${violation.line} - ${violation.content.substring(0, 100)}...</div>`;
                });
                if (styleViolations.length > 10) {
                    results.innerHTML += `<div class="warning">... and ${styleViolations.length - 10} more violations</div>`;
                }
                results.innerHTML += '<div class="warning"><strong>⚠️ Recommendation:</strong> Move these styles to external CSS files.</div>';
            } else {
                results.innerHTML = '<h4>🎨 Inline Styles Test Results</h4><div class="success"><strong>✅ No inline styles found!</strong></div>';
            }
        }

        function testInlineScripts() {
            const results = document.getElementById('results');
            results.innerHTML = '<h4>📜 Inline Scripts Test Results</h4>';
            
            if (mockViolations.inlineScripts.length > 0) {
                results.innerHTML += '<div class="violation"><strong>❌ Violations Found:</strong></div>';
                mockViolations.inlineScripts.forEach(violation => {
                    results.innerHTML += `<div class="violation">${violation}</div>`;
                });
                results.innerHTML += '<div class="warning"><strong>⚠️ Recommendation:</strong> Move these scripts to external JS files.</div>';
            } else {
                results.innerHTML += '<div class="success"><strong>✅ No inline scripts found!</strong></div>';
            }
        }

        function testEventHandlers() {
            const results = document.getElementById('results');
            results.innerHTML = '<h4>🎯 Event Handlers Test Results</h4>';
            
            if (mockViolations.eventHandlers.length > 0) {
                results.innerHTML += '<div class="violation"><strong>❌ Violations Found:</strong></div>';
                mockViolations.eventHandlers.forEach(violation => {
                    results.innerHTML += `<div class="violation">${violation}</div>`;
                });
                results.innerHTML += '<div class="warning"><strong>⚠️ Recommendation:</strong> Use addEventListener in external JS files.</div>';
            } else {
                results.innerHTML += '<div class="success"><strong>✅ No inline event handlers found!</strong></div>';
            }
        }

        function testAllViolations() {
            const results = document.getElementById('results');
            results.innerHTML = '<h4>🔍 Complete CSP Violation Scan</h4>';
            
            const totalViolations = mockViolations.inlineStyles.length + 
                                  mockViolations.inlineScripts.length + 
                                  mockViolations.eventHandlers.length;
            
            if (totalViolations > 0) {
                results.innerHTML += `<div class="violation"><strong>❌ Total Violations: ${totalViolations}</strong></div>`;
                
                if (mockViolations.inlineStyles.length > 0) {
                    results.innerHTML += '<h5>Inline Styles:</h5>';
                    mockViolations.inlineStyles.forEach(violation => {
                        results.innerHTML += `<div class="violation">${violation}</div>`;
                    });
                }
                
                if (mockViolations.inlineScripts.length > 0) {
                    results.innerHTML += '<h5>Inline Scripts:</h5>';
                    mockViolations.inlineScripts.forEach(violation => {
                        results.innerHTML += `<div class="violation">${violation}</div>`;
                    });
                }
                
                if (mockViolations.eventHandlers.length > 0) {
                    results.innerHTML += '<h5>Event Handlers:</h5>';
                    mockViolations.eventHandlers.forEach(violation => {
                        results.innerHTML += `<div class="violation">${violation}</div>`;
                    });
                }
            } else {
                results.innerHTML += '<div class="success"><strong>🎉 No CSP violations found! Your app is compliant!</strong></div>';
            }
        }

        function testPage(pagePath) {
            const results = document.getElementById('results');
            results.innerHTML = `<h4>🌐 Testing Page: ${pagePath}</h4>`;
            results.innerHTML += '<div class="warning">⏳ Simulating page test...</div>';
            
            // Simulate testing delay
            setTimeout(() => {
                // Mock console error detection
                const mockErrors = [
                    'ReferenceError: togglePassword is not defined',
                    'TypeError: Cannot read property \'addEventListener\' of null',
                    'CSP Violation: Refused to execute inline script'
                ];
                
                if (Math.random() > 0.5) {
                    results.innerHTML += '<div class="success">✅ No console errors detected!</div>';
                    results.innerHTML += '<div class="success">✅ No CSP violations on this page!</div>';
                } else {
                    results.innerHTML += '<div class="violation">❌ Console errors detected:</div>';
                    mockErrors.forEach(error => {
                        results.innerHTML += `<div class="violation">${error}</div>`;
                    });
                }
            }, 1000);
        }

        function generateReport() {
            const results = document.getElementById('results');
            const fileAnalysis = document.getElementById('file-analysis');
            
            results.innerHTML = '<h4>📊 Generating Comprehensive Report...</h4>';
            
            // Simulate report generation
            setTimeout(() => {
                results.innerHTML = `
                    <h4>📊 CSP Compliance Report</h4>
                    <div class="success">✅ Fixed Files: 12</div>
                    <div class="warning">⚠️ Files with remaining violations: ${mockViolations.inlineStyles.length + mockViolations.inlineScripts.length + mockViolations.eventHandlers.length}</div>
                    <div class="violation">❌ Critical violations: 3</div>
                    <div class="success">✅ External CSS files created: 5</div>
                    <div class="success">✅ External JS files created: 7</div>
                `;
                
                fileAnalysis.innerHTML = `
                    <h5>Files Successfully Fixed:</h5>
                    <div class="file-item">✅ resources/views/course/purchased-lecture-detail.blade.php</div>
                    <div class="file-item">✅ resources/views/admin/instructor-profiles/index.blade.php</div>
                    <div class="file-item">✅ resources/views/account-pages/signup.blade.php</div>
                    <div class="file-item">✅ resources/views/admin/assignments/index.blade.php</div>
                    <div class="file-item">✅ resources/views/admin/questions/index.blade.php</div>
                    <div class="file-item">✅ resources/views/welcome.blade.php</div>
                    
                    <h5>Files Still Needing Attention:</h5>
                    <div class="file-item">⚠️ resources/views/RTL.blade.php - 7 inline styles</div>
                    <div class="file-item">⚠️ resources/views/admin/layout.blade.php - embedded styles</div>
                    <div class="file-item">⚠️ resources/views/components/layout.blade.php - embedded styles</div>
                    <div class="file-item">⚠️ resources/views/auth/verify-phone.blade.php - embedded styles</div>
                `;
            }, 1500);
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
        });
    </script>
</body>
</html>
