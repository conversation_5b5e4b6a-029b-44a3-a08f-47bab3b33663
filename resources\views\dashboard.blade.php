<x-app-layout>

    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">

        <div class="container py-4 px-5">
            <div class="row">
                <div class="col-md-12">
                    <div class="d-md-flex align-items-center mb-3 mx-2">
                    <div class="mb-md-0 mb-3">
                        @auth
                            <h3 class="font-weight-bold mb-0">Welcome, {{ Auth::user()->name }}</h3>
                        @else
                            <h3 class="font-weight-bold mb-0">Welcome to Islamic Finance Courses</h3>
                        @endauth
                        <p class="mb-0">Start exploring your journey in Islamic Finance Courses!</p>
                    </div>

                    @auth
                        @if(Auth::user()->isAdmin() || Auth::user()->isSuperAdmin())
                        <div class="ms-md-auto text-md-end">
                            <a href="{{ route('devices.index') }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-mobile-alt me-1"></i> Manage Devices
                            </a>
                        </div>
                        @endif
                    @endauth
                    </div>
                </div>
            </div>

            <!-- Banner Slider -->
            <div class="row mb-4">
                <div class="col-12">
                    <div id="bannerCarousel" class="carousel slide" data-bs-ride="carousel">
                        <div class="carousel-indicators">
                            <button type="button" data-bs-target="#bannerCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
                            <button type="button" data-bs-target="#bannerCarousel" data-bs-slide-to="1" aria-label="Slide 2"></button>
                            <button type="button" data-bs-target="#bannerCarousel" data-bs-slide-to="2" aria-label="Slide 3"></button>
                        </div>
                        <div class="carousel-inner">
                            <div class="carousel-item active">
                                <img src="assets/img/banner8.png" class="d-block w-100" alt="Banner 1">
                                <div class="carousel-caption d-none d-md-block">
                                    <h5>Islamic Finance Excellence</h5>
                                    <p>Explore our comprehensive courses on Islamic Finance.</p>
                                </div>
                            </div>
                            <div class="carousel-item">
                                <img src="assets/img/banner2.png" class="d-block w-100" alt="Banner 2">
                                <div class="carousel-caption d-none d-md-block">
                                    <h5>Learn from Experts</h5>
                                    <p>Our courses are taught by industry professionals.</p>
                                </div>
                            </div>
                            <div class="carousel-item">
                                <img src="assets/img/banner3.png" class="d-block w-100" alt="Banner 3">
                                <div class="carousel-caption d-none d-md-block">
                                    <h5>Flexible Learning</h5>
                                    <p>Study at your own pace with our online platform.</p>
                                </div>
                            </div>
                        </div>
                        <button class="carousel-control-prev" type="button" data-bs-target="#bannerCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Previous</span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#bannerCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Next</span>
                        </button>
                    </div>
                </div>
            </div>
            <!-- End Banner Slider -->

            <div class="row">
                <!-- Display latest courses -->
                @if(isset($latestCourses) && $latestCourses->count() > 0)
                <div class="col-12 mb-4">
                    <h4 class="font-weight-bold">Latest Courses</h4>
                    <div class="row">
                        @foreach($latestCourses as $course)
                        <div class="col-md-4 mb-4">
                            <div class="card">
                                <img src="{{ $course->image_path ? Storage::url($course->image_path) : 'https://via.placeholder.com/300x200' }}" class="card-img-top" alt="{{ $course->name }}">
                                <div class="card-body">
                                    <h5 class="card-title">{{ $course->name }}</h5>
                                    <!-- Course Rating -->
                                    <div class="mb-2">
                                        @for ($i = 1; $i <= 5; $i++)
                                            @if ($i <= $course->average_rating)
                                                <i class="fas fa-star text-warning"></i>
                                            @elseif ($i - 0.5 <= $course->average_rating)
                                                <i class="fas fa-star-half-alt text-warning"></i>
                                            @else
                                                <i class="far fa-star text-warning"></i>
                                            @endif
                                        @endfor
                                        <span class="small text-muted ms-1">({{ number_format($course->average_rating, 1) }})</span>
                                    </div>
                                    <p class="card-text">{{ Str::limit(strip_tags($course->description), 100) }}</p>
                                    <a href="{{ route('course.detail', $course->id) }}" class="btn btn-primary">View Course</a>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif

                <livewire:courseslist />
            </div>

            <x-app.footer />
        </div>
    </main>

</x-app-layout>
