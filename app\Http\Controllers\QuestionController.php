<?php

namespace App\Http\Controllers;

use App\Models\Question;
use App\Models\QuestionAttachment;
use App\Models\Course;
use App\Models\Lecture;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class QuestionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Store a newly created question.
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'content' => 'nullable|string|min:5',
                'course_id' => 'required|exists:courses,id',
                'lecture_id' => 'nullable|exists:lectures,id',
                'images.*' => 'nullable|image|max:5120', // 5MB limit for images
                'pdfs.*' => 'nullable|mimes:pdf|max:10240', // 10MB limit for PDFs
                'voice' => 'nullable|file|mimes:mp3,webm,wav,ogg|max:15360', // 15MB limit for voice
                'voice_data' => 'nullable|string', // Base64 encoded audio
            ]);

            // At least one of content or attachments must be present
            if (empty($request->content) &&
                !($request->hasFile('images') ||
                  $request->hasFile('pdfs') ||
                  $request->hasFile('voice') ||
                  $request->filled('voice_data'))) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please provide either question text or at least one attachment.'
                ], 422);
            }

            $question = Question::create([
                'user_id' => Auth::id(),
                'course_id' => $request->course_id,
                'lecture_id' => $request->lecture_id,
                'content' => $request->content ?? 'See attachments',
                'status' => 'pending',
                'title' => substr($request->content ?? 'See attachments', 0, 100) . (strlen($request->content ?? 'See attachments') > 100 ? '...' : ''),
                'content_type' => $request->lecture_id ? 'lecture' : 'course',
                'content_id' => $request->lecture_id ?? $request->course_id,
            ]);

            $attachmentsAdded = [];

            // Handle image uploads
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $image) {
                    $path = $image->store('question_attachments/images', 'public');

                    $attachment = QuestionAttachment::create([
                        'question_id' => $question->id,
                        'file_path' => $path,
                        'file_name' => $image->getClientOriginalName(),
                        'file_type' => 'image',
                        'file_size' => $image->getSize(),
                    ]);

                    $attachmentsAdded[] = [
                        'id' => $attachment->id,
                        'url' => Storage::url($path),
                        'name' => $image->getClientOriginalName(),
                        'type' => 'image',
                        'size' => $image->getSize(),
                    ];
                }
            }

            // Handle PDF uploads
            if ($request->hasFile('pdfs')) {
                foreach ($request->file('pdfs') as $pdf) {
                    $path = $pdf->store('question_attachments/pdfs', 'public');

                    $attachment = QuestionAttachment::create([
                        'question_id' => $question->id,
                        'file_path' => $path,
                        'file_name' => $pdf->getClientOriginalName(),
                        'file_type' => 'pdf',
                        'file_size' => $pdf->getSize(),
                    ]);

                    $attachmentsAdded[] = [
                        'id' => $attachment->id,
                        'url' => Storage::url($path),
                        'name' => $pdf->getClientOriginalName(),
                        'type' => 'pdf',
                        'size' => $pdf->getSize(),
                    ];
                }
            }

            // Handle voice upload
            if ($request->hasFile('voice')) {
                $voice = $request->file('voice');
                $path = $voice->store('question_attachments/voice', 'public');

                $attachment = QuestionAttachment::create([
                    'question_id' => $question->id,
                    'file_path' => $path,
                    'file_name' => $voice->getClientOriginalName(),
                    'file_type' => 'voice',
                    'file_size' => $voice->getSize(),
                ]);

                $attachmentsAdded[] = [
                    'id' => $attachment->id,
                    'url' => Storage::url($path),
                    'name' => $voice->getClientOriginalName(),
                    'type' => 'voice',
                    'size' => $voice->getSize(),
                ];
            }

            // Handle base64 voice data
            if ($request->filled('voice_data')) {
                try {
                    // Extract the actual base64 data from the data URL
                    $base64Data = $request->voice_data;

                    if (Str::startsWith($base64Data, 'data:audio')) {
                        // Extract the audio type from the data URL (e.g., 'webm', 'ogg')
                        $matches = [];
                        preg_match('/data:audio\/(.*?);base64,/', $base64Data, $matches);
                        $audioType = $matches[1] ?? 'webm';

                        // Extract the actual base64 content
                        $base64Data = explode(',', $base64Data)[1];

                        // Decode the base64 data
                        $decodedData = base64_decode($base64Data);

                        // Generate a filename with the correct extension
                        $filename = 'voice_recording_' . time() . '.' . $audioType;
                        $path = 'question_attachments/voice/' . $filename;

                        // Store the file
                        Storage::disk('public')->put($path, $decodedData);

                        // Create attachment record
                        $attachment = QuestionAttachment::create([
                            'question_id' => $question->id,
                            'file_path' => $path,
                            'file_name' => $filename,
                            'file_type' => 'voice',
                            'file_size' => strlen($decodedData),
                            'mime_type' => 'audio/' . $audioType,
                        ]);

                        $attachmentsAdded[] = [
                            'id' => $attachment->id,
                            'url' => Storage::url($path),
                            'name' => $filename,
                            'type' => 'voice',
                            'size' => strlen($decodedData),
                            'mime_type' => 'audio/' . $audioType,
                        ];

                        Log::info('Voice recording saved with type: ' . $audioType);
                    }
                } catch (\Exception $e) {
                    Log::error('Error processing voice data: ' . $e->getMessage());
                    // Continue without stopping the process
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Your question has been submitted successfully.',
                'question' => [
                    'id' => $question->id,
                    'content' => $question->content,
                    'created_at' => $question->created_at->diffForHumans(),
                    'user' => Auth::user()->name,
                    'status' => $question->status,
                    'attachments' => $attachmentsAdded,
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error in question submission: ' . $e->getMessage());
            Log::error($e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => 'Error submitting question: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get questions for a course or lecture.
     */
    public function getQuestions(Request $request)
    {
        try {
            $request->validate([
                'course_id' => 'required|exists:courses,id',
                'lecture_id' => 'nullable|exists:lectures,id',
                'user_id' => 'nullable|exists:users,id',
            ]);

            $query = Question::with(['user', 'answers.user', 'answers.attachments', 'attachments'])
                ->where('course_id', $request->course_id)
                ->orderBy('created_at', 'desc');

            if ($request->lecture_id) {
                $query->where('lecture_id', $request->lecture_id);
            }

            // Filter by user_id if provided
            if ($request->user_id) {
                $query->where('user_id', $request->user_id);
            }

            $questions = $query->get();
            $formattedQuestions = [];

            foreach ($questions as $question) {
                // Get attachments separately to avoid any issues
                $attachments = [];
                foreach ($question->attachments as $attachment) {
                    $attachments[] = [
                        'id' => $attachment->id,
                        'url' => $attachment->url,
                        'name' => $attachment->file_name,
                        'type' => $attachment->file_type,
                        'size' => $attachment->file_size,
                    ];
                }

                $answers = [];
                foreach ($question->answers as $answer) {
                    $answerAttachments = [];

                    // Only try to access attachments if the relationship is loadable
                    try {
                        if (method_exists($answer, 'attachments') && $answer->attachments) {
                            foreach ($answer->attachments as $attachment) {
                                $answerAttachments[] = [
                                    'id' => $attachment->id,
                                    'url' => $attachment->url,
                                    'name' => $attachment->file_name ?? $attachment->original_name,
                                    'type' => $attachment->file_type,
                                    'size' => $attachment->file_size,
                                    'mime_type' => $attachment->mime_type,
                                ];
                            }
                        }
                    } catch (\Exception $e) {
                        // Just continue if there's an issue with attachments
                        Log::warning('Error loading answer attachments: ' . $e->getMessage());
                    }

                    $answers[] = [
                        'id' => $answer->id,
                        'content' => $answer->content,
                        'created_at' => $answer->created_at->diffForHumans(),
                        'user' => $answer->user->name,
                        'is_pinned' => $answer->is_pinned,
                        'is_accepted' => $answer->is_accepted,
                        'attachments' => $answerAttachments,
                    ];
                }

                $formattedQuestions[] = [
                    'id' => $question->id,
                    'content' => $question->content,
                    'created_at' => $question->created_at->diffForHumans(),
                    'user' => $question->user->name,
                    'status' => $question->status,
                    'attachments' => $attachments,
                    'answers' => $answers,
                ];
            }

            return response()->json([
                'success' => true,
                'questions' => $formattedQuestions
            ]);
        } catch (\Exception $e) {
            Log::error('Error in getQuestions: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            return response()->json([
                'success' => false,
                'message' => 'Error loading questions: ' . $e->getMessage()
            ], 500);
        }
    }
}
