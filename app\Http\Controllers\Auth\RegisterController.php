<?php

namespace App\Http\Controllers\Auth;

use App\Models\Role;
use App\Models\User;
use App\Models\UserDevice;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Services\TwilioService;
use App\Services\DibaSmsService;
use App\Services\DeviceDetectionService;
use App\Models\PhoneVerification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Illuminate\Auth\Events\Registered;
use Illuminate\Validation\Rules\Password;
use App\Providers\RouteServiceProvider;

class RegisterController extends Controller
{
    protected $deviceDetectionService;
    
    public function __construct(DeviceDetectionService $deviceDetectionService)
    {
        $this->deviceDetectionService = $deviceDetectionService;
    }
    
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('auth.signup');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'min:4', 'max:100', 'regex:/^[A-Za-z\s\.\-]+$/'],
            'email' => ['required', 'email', 'max:100', 'unique:users'],
            'password' => [
                'required',
                'min:6',
                'max:15',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#+=~])[A-Za-z\d@$!%*?&#+=~\.]{6,15}$/',
                'confirmed'
            ],
            'phone' => ['required', 'string', 'regex:/^\+?[0-9]{10,15}$/', 'unique:users'],
            'terms' => ['accepted'],
        ], [
            'name.required' => 'Name is required',
            'name.regex' => 'Name may only contain letters, spaces, dots and hyphens',
            'email.required' => 'Email is required',
            'password.required' => 'Password is required',
            'password.regex' => 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
            'phone.required' => 'Phone number is required',
            'phone.regex' => 'Invalid phone number format. Use only numbers with optional + prefix (10-15 digits)',
            'terms.accepted' => 'You must accept the terms and conditions'
        ]);

        // Sanitize phone number
        $sanitizedPhone = preg_replace('/[^0-9+]/', '', $request->phone);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'phone' => $sanitizedPhone
        ]);

        // Assign the "User" role to the newly registered user
        $userRole = Role::where('name', 'User')->first();
        if ($userRole) {
            $user->roles()->attach($userRole);
        }
        
        // Generate device identifier - only for regular users, not admins
        $deviceId = $this->deviceDetectionService->generateDeviceId($request);
        $deviceInfo = $this->deviceDetectionService->getDeviceInfo();

        // Store or update device information
        UserDevice::updateOrCreate(
            ['device_id' => $deviceId],
            [
                'user_id' => $user->id,
                'device_name' => $deviceInfo['device_name'],
                'browser' => $deviceInfo['browser'],
                'platform' => $deviceInfo['platform'],
                'ip_address' => $this->deviceDetectionService->getIpAddress($request),
                'last_login_at' => now(),
                'is_primary' => true
            ]
        );

        event(new Registered($user));

        // Check if phone number is from Pakistan
        $isPakistanNumber = $this->isFromPakistan($sanitizedPhone);
        
        if ($isPakistanNumber) {
            // Generate verification code only for Pakistani numbers
            $code = mt_rand(100000, 999999);
            $expiresAt = now()->addMinutes(15);
            
            // Store the code in database
            PhoneVerification::updateOrCreate(
                ['phone' => $sanitizedPhone],
                ['code' => $code, 'expires_at' => $expiresAt]
            );
            
            try {
                // Send SMS verification using Diba SMS for Pakistani numbers
                $dibaSmsService = new DibaSmsService();
                
                // Format the phone number for DibaSms (remove country code)
                $localNumber = $sanitizedPhone;
                if (str_starts_with($localNumber, '+')) {
                    $localNumber = substr($localNumber, 1);
                }
                if (str_starts_with($localNumber, '92')) {
                    $localNumber = substr($localNumber, 2);
                }
                
                Log::info('Register: Using DibaSMS for Pakistani number', [
                    'phone' => $sanitizedPhone,
                    'local_number' => $localNumber
                ]);
                
                $message = "Your verification code is: $code";
                $dibaSmsService->sendSms($localNumber, $message, '92');
            } catch (\Exception $e) {
                Log::error('Register: Failed to send verification SMS', [
                    'error' => $e->getMessage(),
                    'phone' => $sanitizedPhone
                ]);
                // Continue with login despite SMS failure
            }
        } else {
            // For non-Pakistani numbers, mark phone as verified automatically
            $user->phone_verified_at = now();
            $user->save();
        }

        Auth::login($user);

        return redirect(RouteServiceProvider::HOME);
    }
    
    /**
     * Check if phone number is from Pakistan
     *
     * @param string|null $phone
     * @return bool
     */
    private function isFromPakistan(?string $phone): bool
    {
        // Return false if phone is null or empty
        if (empty($phone)) {
            return false;
        }

        // Remove + if it exists
        if (str_starts_with($phone, '+')) {
            $phone = substr($phone, 1);
        }

        // Check if it starts with 92 (Pakistan)
        return str_starts_with($phone, '92');
    }
}
