/**
 * Ultimate CSP Fix
 * This is the final comprehensive solution for all CSP violations
 */

(function() {
    'use strict';
    
    // Configuration
    const DEBUG = true;
    const SCAN_INTERVAL = 100; // ms
    
    // Get CSP nonce
    const nonceMetaTag = document.querySelector('meta[name="csp-nonce"]');
    const nonce = nonceMetaTag ? nonceMetaTag.getAttribute('content') : null;
    
    if (!nonce) {
        console.error('Ultimate CSP Fix: CSP nonce not found in meta tag');
        return;
    }
    
    if (DEBUG) console.log('Ultimate CSP Fix: Initialized with nonce:', nonce);
    
    // Counters and tracking
    let fixCounter = 0;
    let processedElements = new WeakSet();
    
    // Create a style container for all our fixes
    const styleContainer = document.createElement('div');
    styleContainer.id = 'ultimate-csp-fix-styles';
    styleContainer.style.display = 'none';
    document.head.appendChild(styleContainer);
    
    // Function to create a unique class name
    function createUniqueClass() {
        return 'ucf-' + Date.now() + '-' + (++fixCounter);
    }
    
    // Function to create a nonce-enabled style
    function createNoncedStyle(css) {
        const style = document.createElement('style');
        style.setAttribute('nonce', nonce);
        style.textContent = css;
        styleContainer.appendChild(style);
        return style;
    }
    
    // Function to fix inline styles
    function fixInlineStyle(element) {
        if (processedElements.has(element)) return false;
        if (!element.hasAttribute || !element.hasAttribute('style')) return false;
        
        const inlineStyle = element.getAttribute('style');
        if (!inlineStyle || !inlineStyle.trim()) return false;
        
        try {
            // Remove the inline style
            element.removeAttribute('style');
            
            // Create unique class
            const uniqueClass = createUniqueClass();
            element.classList.add(uniqueClass);
            
            // Create the style rule
            createNoncedStyle('.' + uniqueClass + ' { ' + inlineStyle + ' }');
            
            // Mark as processed
            processedElements.add(element);
            
            if (DEBUG) console.log('Ultimate CSP Fix: Fixed inline style:', inlineStyle);
            return true;
        } catch (error) {
            console.error('Ultimate CSP Fix: Error fixing inline style:', error);
            return false;
        }
    }
    
    // Function to fix inline event handlers
    function fixInlineEventHandler(element, eventType) {
        if (processedElements.has(element)) return false;
        
        const handlerAttr = 'on' + eventType;
        if (!element.hasAttribute || !element.hasAttribute(handlerAttr)) return false;
        
        const handlerCode = element.getAttribute(handlerAttr);
        if (!handlerCode || !handlerCode.trim()) return false;
        
        try {
            // Remove the inline handler
            element.removeAttribute(handlerAttr);
            
            // Add proper event listener
            element.addEventListener(eventType, function(event) {
                try {
                    const func = new Function('event', handlerCode);
                    func.call(this, event);
                } catch (error) {
                    console.error('Ultimate CSP Fix: Error executing event handler:', error);
                }
            });
            
            // Mark as processed
            processedElements.add(element);
            
            if (DEBUG) console.log('Ultimate CSP Fix: Fixed inline event handler:', handlerAttr, handlerCode);
            return true;
        } catch (error) {
            console.error('Ultimate CSP Fix: Error fixing event handler:', error);
            return false;
        }
    }
    
    // Function to fix inline scripts
    function fixInlineScript(script) {
        if (processedElements.has(script)) return false;
        if (script.hasAttribute('src') || script.hasAttribute('nonce')) return false;
        
        try {
            script.setAttribute('nonce', nonce);
            processedElements.add(script);
            
            if (DEBUG) console.log('Ultimate CSP Fix: Added nonce to inline script');
            return true;
        } catch (error) {
            console.error('Ultimate CSP Fix: Error fixing inline script:', error);
            return false;
        }
    }
    
    // Function to fix styles without nonce
    function fixStyleWithoutNonce(style) {
        if (processedElements.has(style)) return false;
        if (style.hasAttribute('nonce')) return false;
        
        try {
            style.setAttribute('nonce', nonce);
            processedElements.add(style);
            
            if (DEBUG) console.log('Ultimate CSP Fix: Added nonce to style element');
            return true;
        } catch (error) {
            console.error('Ultimate CSP Fix: Error fixing style element:', error);
            return false;
        }
    }
    
    // Function to process a single element
    function processElement(element) {
        if (!element || element.nodeType !== Node.ELEMENT_NODE) return;
        
        let fixed = false;
        
        // Fix inline styles
        if (fixInlineStyle(element)) fixed = true;
        
        // Fix inline event handlers
        const eventTypes = ['click', 'change', 'submit', 'load', 'error', 'mouseover', 'mouseout', 'focus', 'blur'];
        eventTypes.forEach(eventType => {
            if (fixInlineEventHandler(element, eventType)) fixed = true;
        });
        
        // Fix inline scripts
        if (element.tagName === 'SCRIPT' && fixInlineScript(element)) fixed = true;
        
        // Fix styles without nonce
        if (element.tagName === 'STYLE' && fixStyleWithoutNonce(element)) fixed = true;
        
        return fixed;
    }
    
    // Function to scan the entire document
    function scanDocument() {
        let totalFixed = 0;
        
        // Process all elements with inline styles
        const elementsWithStyle = document.querySelectorAll('[style]');
        elementsWithStyle.forEach(element => {
            if (processElement(element)) totalFixed++;
        });
        
        // Process all elements with inline event handlers
        const eventTypes = ['click', 'change', 'submit', 'load', 'error', 'mouseover', 'mouseout', 'focus', 'blur'];
        eventTypes.forEach(eventType => {
            const elements = document.querySelectorAll('[on' + eventType + ']');
            elements.forEach(element => {
                if (processElement(element)) totalFixed++;
            });
        });
        
        // Process all inline scripts without nonce
        const inlineScripts = document.querySelectorAll('script:not([src]):not([nonce])');
        inlineScripts.forEach(script => {
            if (processElement(script)) totalFixed++;
        });
        
        // Process all styles without nonce
        const stylesWithoutNonce = document.querySelectorAll('style:not([nonce])');
        stylesWithoutNonce.forEach(style => {
            if (processElement(style)) totalFixed++;
        });
        
        if (totalFixed > 0 && DEBUG) {
            console.log('Ultimate CSP Fix: Fixed', totalFixed, 'violations in this scan');
        }
        
        return totalFixed;
    }
    
    // Set up MutationObserver
    const observer = new MutationObserver(function(mutations) {
        let needsScan = false;
        
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        if (processElement(node)) needsScan = true;
                        
                        // Process child elements
                        const children = node.querySelectorAll ? node.querySelectorAll('*') : [];
                        children.forEach(child => {
                            if (processElement(child)) needsScan = true;
                        });
                    }
                });
            } else if (mutation.type === 'attributes') {
                if (mutation.attributeName === 'style' || mutation.attributeName.startsWith('on')) {
                    if (processElement(mutation.target)) needsScan = true;
                }
            }
        });
        
        if (needsScan) {
            setTimeout(scanDocument, 10);
        }
    });
    
    // Start observing
    observer.observe(document.documentElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'onclick', 'onchange', 'onsubmit', 'onload', 'onerror', 'onmouseover', 'onmouseout', 'onfocus', 'onblur']
    });
    
    // Override dangerous methods
    function overrideDangerousMethods() {
        // Override setAttribute for style
        const originalSetAttribute = Element.prototype.setAttribute;
        Element.prototype.setAttribute = function(name, value) {
            if (name === 'style' && value && value.trim()) {
                const uniqueClass = createUniqueClass();
                this.classList.add(uniqueClass);
                createNoncedStyle('.' + uniqueClass + ' { ' + value + ' }');
                if (DEBUG) console.log('Ultimate CSP Fix: Intercepted setAttribute style:', value);
                return;
            }
            return originalSetAttribute.call(this, name, value);
        };
        
        // Override style property
        const originalStyleDescriptor = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'style');
        if (originalStyleDescriptor && originalStyleDescriptor.set) {
            Object.defineProperty(HTMLElement.prototype, 'style', {
                set: function(value) {
                    if (typeof value === 'string' && value.trim()) {
                        const uniqueClass = createUniqueClass();
                        this.classList.add(uniqueClass);
                        createNoncedStyle('.' + uniqueClass + ' { ' + value + ' }');
                        if (DEBUG) console.log('Ultimate CSP Fix: Intercepted style property:', value);
                        return;
                    }
                    return originalStyleDescriptor.set.call(this, value);
                },
                get: originalStyleDescriptor.get
            });
        }
        
        // Override document.write
        const originalDocumentWrite = document.write;
        document.write = function(content) {
            if (DEBUG) console.warn('Ultimate CSP Fix: Blocked document.write:', content);
            return;
        };
    }
    
    // Initialize
    function initialize() {
        if (DEBUG) console.log('Ultimate CSP Fix: Starting initialization');
        
        // Override dangerous methods
        overrideDangerousMethods();
        
        // Initial scan
        scanDocument();
        
        // Set up periodic scanning
        setInterval(scanDocument, SCAN_INTERVAL);
        
        if (DEBUG) console.log('Ultimate CSP Fix: Initialization complete');
    }
    
    // Start when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    // Expose global function for manual fixes
    window.ultimateCSPFix = {
        scan: scanDocument,
        process: processElement,
        nonce: nonce,
        debug: DEBUG
    };
    
})();