/**
 * Sign-in Page Specific CSP Fix
 * This script specifically targets CSP issues on the sign-in page
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get CSP nonce from meta tag
    const nonceMetaTag = document.querySelector('meta[name="csp-nonce"]');
    const nonce = nonceMetaTag ? nonceMetaTag.getAttribute('content') : null;
    
    if (!nonce) {
        console.warn('CSP nonce not found in meta tag');
        return;
    }
    
    // Function to fix specific sign-in page issues
    function fixSignInPageIssues() {
        // Fix captcha container styling
        const captchaContainer = document.getElementById('captcha-display');
        if (captchaContainer) {
            // Remove any inline styles
            if (captchaContainer.hasAttribute('style')) {
                const inlineStyle = captchaContainer.getAttribute('style');
                captchaContainer.removeAttribute('style');
                captchaContainer.classList.add('captcha-display-styled');
                
                // Create a style element with the nonce
                const styleElement = document.createElement('style');
                styleElement.setAttribute('nonce', nonce);
                styleElement.textContent = '.captcha-display-styled {' + inlineStyle + '}';
                document.head.appendChild(styleElement);
            }
        }
        
        // Fix password toggle button
        const passwordToggleButtons = document.querySelectorAll('.auth-password-toggle');
        passwordToggleButtons.forEach((button, index) => {
            // Remove any onclick attributes
            if (button.hasAttribute('onclick')) {
                const onclickCode = button.getAttribute('onclick');
                button.removeAttribute('onclick');
                
                // Add an event listener instead
                button.addEventListener('click', function(event) {
                    event.preventDefault();
                    const passwordField = this.closest('.position-relative').querySelector('input[type="password"], input[type="text"]');
                    if (passwordField) {
                        if (passwordField.type === 'password') {
                            passwordField.type = 'text';
                            const eyeIcon = this.querySelector('i');
                            if (eyeIcon) {
                                eyeIcon.classList.remove('fa-eye');
                                eyeIcon.classList.add('fa-eye-slash');
                            }
                        } else {
                            passwordField.type = 'password';
                            const eyeIcon = this.querySelector('i');
                            if (eyeIcon) {
                                eyeIcon.classList.remove('fa-eye-slash');
                                eyeIcon.classList.add('fa-eye');
                            }
                        }
                    }
                });
            }
        });
        
        // Fix captcha refresh button
        const captchaRefreshButtons = document.querySelectorAll('[data-action="refresh-captcha"]');
        captchaRefreshButtons.forEach(button => {
            // Remove any onclick attributes
            if (button.hasAttribute('onclick')) {
                button.removeAttribute('onclick');
            }
            
            // Ensure it has an event listener
            const hasListener = button._hasCaptchaRefreshListener;
            if (!hasListener) {
                button.addEventListener('click', function(event) {
                    event.preventDefault();
                    // Generate a new captcha
                    if (typeof generateCaptcha === 'function') {
                        generateCaptcha();
                    } else {
                        console.warn('generateCaptcha function not found');
                    }
                });
                button._hasCaptchaRefreshListener = true;
            }
        });
        
        // Fix any other inline styles in the sign-in form
        const signinForm = document.querySelector('form[action="/signin"]');
        if (signinForm) {
            const elementsWithStyle = signinForm.querySelectorAll('[style]');
            elementsWithStyle.forEach((element, index) => {
                const inlineStyle = element.getAttribute('style');
                element.removeAttribute('style');
                
                // Add a unique class
                const uniqueClass = 'signin-fix-' + index;
                element.classList.add(uniqueClass);
                
                // Create a style element with the nonce
                const styleElement = document.createElement('style');
                styleElement.setAttribute('nonce', nonce);
                styleElement.textContent = '.' + uniqueClass + ' {' + inlineStyle + '}';
                document.head.appendChild(styleElement);
            });
        }
        
        // Fix the oblique image background
        const obliqueImage = document.querySelector('.oblique-image');
        if (obliqueImage && obliqueImage.hasAttribute('style')) {
            const inlineStyle = obliqueImage.getAttribute('style');
            obliqueImage.removeAttribute('style');
            obliqueImage.classList.add('oblique-image-styled');
            
            // Create a style element with the nonce
            const styleElement = document.createElement('style');
            styleElement.setAttribute('nonce', nonce);
            styleElement.textContent = '.oblique-image-styled {' + inlineStyle + '}';
            document.head.appendChild(styleElement);
        }
    }
    
    // Run immediately
    fixSignInPageIssues();
    
    // Set up a MutationObserver to watch for changes
    const observer = new MutationObserver(mutations => {
        let needsUpdate = false;
        
        mutations.forEach(mutation => {
            if (mutation.type === 'childList' || 
                (mutation.type === 'attributes' && 
                 (mutation.attributeName === 'style' || mutation.attributeName === 'onclick'))) {
                needsUpdate = true;
            }
        });
        
        if (needsUpdate) {
            fixSignInPageIssues();
        }
    });
    
    // Start observing the document
    observer.observe(document.documentElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'onclick']
    });
    
    // Run periodically to catch any missed elements
    setInterval(fixSignInPageIssues, 500);
});