<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Cache;
use App\Models\Lecture;
use App\Models\UserCourse;
use Carbon\Carbon;

class VideoTokenController extends Controller
{
    /**
     * Generate a secure video token for authenticated users
     */
    public function generateVideoToken(Request $request)
    {
        $request->validate([
            'lecture_id' => 'required|integer|exists:lectures,id',
            'course_id' => 'required|integer|exists:courses,id'
        ]);

        $user = Auth::user();
        $lectureId = $request->lecture_id;
        $courseId = $request->course_id;

        // Verify user has access to this lecture
        $hasAccess = UserCourse::where('user_id', $user->id)
            ->where(function($query) use ($courseId, $lectureId) {
                $query->where('course_id', $courseId)
                      ->where(function($q) use ($lectureId) {
                          $q->whereNull('lecture_id')
                            ->orWhere('lecture_id', $lectureId);
                      });
            })
            ->where('status', 'active')
            ->exists();

        if (!$hasAccess) {
            return response()->json(['error' => 'Access denied'], 403);
        }

        $lecture = Lecture::findOrFail($lectureId);
        
        if (!$lecture->youtube_url) {
            return response()->json(['error' => 'No video available'], 404);
        }

        // Extract YouTube ID
        $youtubeId = $this->extractYouTubeId($lecture->youtube_url);
        
        if (!$youtubeId) {
            return response()->json(['error' => 'Invalid video URL'], 400);
        }

        // Create a time-limited encrypted token
        $tokenData = [
            'youtube_id' => $youtubeId,
            'user_id' => $user->id,
            'lecture_id' => $lectureId,
            'expires_at' => Carbon::now()->addHours(2)->timestamp,
            'session_id' => session()->getId(),
            'user_agent' => $request->userAgent(),
            'ip' => $request->ip()
        ];

        // Multiple layers of encryption
        $encryptedToken = Crypt::encrypt($tokenData);
        $obfuscatedToken = base64_encode(str_rot13($encryptedToken));
        
        // Store token in cache with expiration
        $cacheKey = 'video_token_' . $user->id . '_' . $lectureId . '_' . time();
        Cache::put($cacheKey, $obfuscatedToken, 120); // 2 hours

        return response()->json([
            'token' => $cacheKey,
            'expires_in' => 7200 // 2 hours in seconds
        ]);
    }

    /**
     * Get video data using token
     */
    public function getVideoData(Request $request)
    {
        $request->validate([
            'token' => 'required|string'
        ]);

        $user = Auth::user();
        $token = $request->token;

        // Get token from cache
        $obfuscatedToken = Cache::get($token);
        
        if (!$obfuscatedToken) {
            return response()->json(['error' => 'Token expired or invalid'], 401);
        }

        try {
            // Decrypt token
            $encryptedToken = str_rot13(base64_decode($obfuscatedToken));
            $tokenData = Crypt::decrypt($encryptedToken);

            // Validate token
            if ($tokenData['expires_at'] < time()) {
                Cache::forget($token);
                return response()->json(['error' => 'Token expired'], 401);
            }

            if ($tokenData['user_id'] !== $user->id) {
                return response()->json(['error' => 'Invalid token'], 401);
            }

            if ($tokenData['session_id'] !== session()->getId()) {
                return response()->json(['error' => 'Session mismatch'], 401);
            }

            // Additional security checks
            if ($tokenData['user_agent'] !== $request->userAgent()) {
                return response()->json(['error' => 'Security violation'], 401);
            }

            // Return obfuscated video data
            $videoData = [
                'provider' => 'yt',
                'id' => base64_encode(str_rot13($tokenData['youtube_id'])),
                'timestamp' => time()
            ];

            return response()->json($videoData);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Invalid token'], 401);
        }
    }

    /**
     * Extract YouTube ID from URL
     */
    private function extractYouTubeId($url)
    {
        if (strpos($url, 'youtube.com/watch?v=') !== false) {
            $parts = parse_url($url);
            parse_str($parts['query'], $query);
            return $query['v'] ?? null;
        } elseif (strpos($url, 'youtu.be/') !== false) {
            return substr(parse_url($url, PHP_URL_PATH), 1);
        }
        
        return null;
    }
}
