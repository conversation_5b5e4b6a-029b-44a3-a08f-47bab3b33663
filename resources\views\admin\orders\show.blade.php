@extends('admin.layout')

@section('title', 'Order Details')

@section('header', 'Order Details #' . $order->id)

@section('content')
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Order Information</h6>
                    <div>
                        <a href="{{ route('admin.orders') }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-arrow-left mr-1"></i> Back to Orders
                        </a>

                        @if($order->status === 'pending' || $order->status === 'awaiting_payment')
                            <form action="{{ route('admin.order.approve', $order->id) }}" method="POST" class="d-inline">
                                @csrf
                                <button type="submit" class="btn btn-success btn-sm">
                                    <i class="fas fa-check mr-1"></i> Approve Payment
                                </button>
                            </form>

                            <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#rejectModal">
                                <i class="fas fa-times mr-1"></i> Reject Payment
                            </button>

                            <!-- Reject Modal -->
                            <div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="rejectModalLabel">Reject Order #{{ $order->id }}</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <form action="{{ route('admin.order.reject', $order->id) }}" method="POST">
                                            @csrf
                                            <div class="modal-body">
                                                <div class="mb-3">
                                                    <label for="reason" class="form-label">Reason for Rejection (Optional)</label>
                                                    <textarea class="form-control" id="reason" name="reason" rows="3"></textarea>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <button type="submit" class="btn btn-danger">Reject Order</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="mb-3">Order Details</h5>
                            <table class="table table-bordered table-sm">
                                <tr>
                                    <th width="40%">Order ID</th>
                                    <td>{{ $order->id }}</td>
                                </tr>
                                <tr>
                                    <th>Order Date</th>
                                    <td>{{ $order->created_at->format('M d, Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <th>Status</th>
                                    <td>
                                        <span class="badge {{ $order->status === 'pending' || $order->status === 'awaiting_payment' ? 'bg-warning' : ($order->status === 'paid' || $order->status === 'completed' ? 'bg-success' : ($order->status === 'rejected' ? 'bg-danger' : 'bg-secondary')) }}">
                                            {{ ucfirst(str_replace('_', ' ', $order->status)) }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Payment Method</th>
                                    <td>{{ ucfirst($order->payment_method) }}</td>
                                </tr>
                                <tr>
                                    <th>Subtotal</th>
                                    <td>${{ number_format($order->total, 2) }}</td>
                                </tr>
                                @if($order->discount > 0)
                                <tr>
                                    <th>Discount</th>
                                    <td>${{ number_format($order->discount, 2) }}</td>
                                </tr>
                                @endif
                                @if($order->coupon_code)
                                <tr>
                                    <th>Coupon Code</th>
                                    <td>{{ $order->coupon_code }}</td>
                                </tr>
                                @endif
                                <tr>
                                    <th>Final Total</th>
                                    <td>${{ number_format($order->final_total ?? ($order->total - ($order->discount ?? 0)), 2) }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5 class="mb-3">Customer Information</h5>
                            @if($billingAddress)
                                <table class="table table-bordered table-sm">
                                    <tr>
                                        <th width="40%">Name</th>
                                        <td>{{ $billingAddress['first_name'] ?? '' }} {{ $billingAddress['last_name'] ?? '' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Email</th>
                                        <td>{{ $billingAddress['email'] ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Phone</th>
                                        <td>{{ $billingAddress['phone'] ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Address</th>
                                        <td>{{ $billingAddress['address'] ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>City</th>
                                        <td>{{ $billingAddress['city'] ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>State</th>
                                        <td>{{ $billingAddress['state'] ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Country</th>
                                        <td>{{ $billingAddress['country'] ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Postal Code</th>
                                        <td>{{ $billingAddress['postal_code'] ?? 'N/A' }}</td>
                                    </tr>
                                </table>
                            @else
                                <p class="text-muted">No billing address information available.</p>
                            @endif
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="mb-3">Ordered Items</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th width="10%">#</th>
                                            <th width="15%">Type</th>
                                            <th>Item</th>
                                            @if(count(array_filter($items, function($item) { return isset($item['course']); })))
                                                <th>Course</th>
                                            @endif
                                            <th class="text-end" width="15%">Price</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($items as $index => $item)
                                            <tr>
                                                <td>{{ $index + 1 }}</td>
                                                <td>{{ $item['type'] }}</td>
                                                <td>{{ $item['name'] }}</td>
                                                @if(count(array_filter($items, function($item) { return isset($item['course']); })))
                                                    <td>{{ $item['course'] ?? 'N/A' }}</td>
                                                @endif
                                                <td class="text-end">${{ number_format($item['price'], 2) }}</td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="5" class="text-center">No items found</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th colspan="{{ count(array_filter($items, function($item) { return isset($item['course']); })) ? '4' : '3' }}" class="text-end">Subtotal:</th>
                                            <th class="text-end">${{ number_format($order->total, 2) }}</th>
                                        </tr>
                                        @if($order->discount > 0)
                                            <tr>
                                                <th colspan="{{ count(array_filter($items, function($item) { return isset($item['course']); })) ? '4' : '3' }}" class="text-end">Discount:</th>
                                                <th class="text-end text-danger">-${{ number_format($order->discount, 2) }}</th>
                                            </tr>
                                        @endif
                                        <tr>
                                            <th colspan="{{ count(array_filter($items, function($item) { return isset($item['course']); })) ? '4' : '3' }}" class="text-end">Final Total:</th>
                                            <th class="text-end">${{ number_format($order->final_total ?? ($order->total - ($order->discount ?? 0)), 2) }}</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
