/**
 * Minimal CSP Fix
 * A safe, minimal solution that only fixes violations without dangerous overrides
 */

(function() {
    'use strict';
    
    // Get CSP nonce
    const nonce = document.querySelector('meta[name="csp-nonce"]')?.getAttribute('content');
    
    if (!nonce) {
        console.warn('Minimal CSP Fix: CSP nonce not found');
        return;
    }
    
    console.log('Minimal CSP Fix: Initialized with nonce:', nonce);
    
    let fixCounter = 0;
    
    // Function to create a unique class name
    function createUniqueClass() {
        return 'mcf-' + Date.now() + '-' + (++fixCounter);
    }
    
    // Function to create a nonce-enabled style
    function createNoncedStyle(css) {
        const style = document.createElement('style');
        style.setAttribute('nonce', nonce);
        style.textContent = css;
        document.head.appendChild(style);
        return style;
    }
    
    // Function to fix all CSP violations
    function fixAllViolations() {
        let totalFixed = 0;
        
        // Fix inline styles
        const elementsWithStyle = document.querySelectorAll('[style]');
        elementsWithStyle.forEach(element => {
            const inlineStyle = element.getAttribute('style');
            if (inlineStyle && inlineStyle.trim()) {
                try {
                    element.removeAttribute('style');
                    const uniqueClass = createUniqueClass();
                    element.classList.add(uniqueClass);
                    createNoncedStyle('.' + uniqueClass + ' { ' + inlineStyle + ' }');
                    totalFixed++;
                } catch (error) {
                    console.error('Minimal CSP Fix: Error fixing inline style:', error);
                }
            }
        });
        
        // Fix inline event handlers
        const eventTypes = ['click', 'change', 'submit', 'load', 'error', 'mouseover', 'mouseout'];
        eventTypes.forEach(eventType => {
            const handlerAttr = 'on' + eventType;
            const elements = document.querySelectorAll('[' + handlerAttr + ']');
            elements.forEach(element => {
                const handlerCode = element.getAttribute(handlerAttr);
                if (handlerCode && handlerCode.trim()) {
                    try {
                        element.removeAttribute(handlerAttr);
                        element.addEventListener(eventType, function(event) {
                            try {
                                const func = new Function('event', handlerCode);
                                func.call(this, event);
                            } catch (error) {
                                console.error('Minimal CSP Fix: Error executing event handler:', error);
                            }
                        });
                        totalFixed++;
                    } catch (error) {
                        console.error('Minimal CSP Fix: Error fixing event handler:', error);
                    }
                }
            });
        });
        
        // Fix inline scripts without nonce
        const inlineScripts = document.querySelectorAll('script:not([src]):not([nonce])');
        inlineScripts.forEach(script => {
            try {
                script.setAttribute('nonce', nonce);
                totalFixed++;
            } catch (error) {
                console.error('Minimal CSP Fix: Error fixing inline script:', error);
            }
        });
        
        // Fix styles without nonce
        const stylesWithoutNonce = document.querySelectorAll('style:not([nonce])');
        stylesWithoutNonce.forEach(style => {
            try {
                style.setAttribute('nonce', nonce);
                totalFixed++;
            } catch (error) {
                console.error('Minimal CSP Fix: Error fixing style element:', error);
            }
        });
        
        if (totalFixed > 0) {
            console.log('Minimal CSP Fix: Fixed', totalFixed, 'violations');
        }
        
        return totalFixed;
    }
    
    // Initialize
    function initialize() {
        // Initial fix
        fixAllViolations();
        
        // Set up observer for dynamic content
        const observer = new MutationObserver(function(mutations) {
            let needsFix = false;
            
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            if (node.hasAttribute && (
                                node.hasAttribute('style') ||
                                node.hasAttribute('onclick') ||
                                (node.tagName === 'SCRIPT' && !node.hasAttribute('nonce') && !node.hasAttribute('src')) ||
                                (node.tagName === 'STYLE' && !node.hasAttribute('nonce'))
                            )) {
                                needsFix = true;
                            }
                            
                            // Check child elements
                            const problematicChildren = node.querySelectorAll ? 
                                node.querySelectorAll('[style], [onclick], script:not([nonce]), style:not([nonce])') : [];
                            if (problematicChildren.length > 0) {
                                needsFix = true;
                            }
                        }
                    });
                } else if (mutation.type === 'attributes') {
                    if (mutation.attributeName === 'style' || mutation.attributeName.startsWith('on')) {
                        needsFix = true;
                    }
                }
            });
            
            if (needsFix) {
                setTimeout(fixAllViolations, 10);
            }
        });
        
        observer.observe(document.documentElement, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'onclick', 'onchange', 'onsubmit', 'onload', 'onerror']
        });
        
        // Periodic fixes
        setInterval(fixAllViolations, 2000);
        
        console.log('Minimal CSP Fix: Initialization complete');
    }
    
    // Start when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    // Expose global function
    window.minimalCSPFix = {
        fix: fixAllViolations,
        nonce: nonce
    };
    
})();