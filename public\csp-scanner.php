<?php
/**
 * CSP Compliance Scanner
 * Scans Laravel view files for Content Security Policy violations
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

class CSPScanner {
    private $viewsPath;
    private $violations = [];
    private $stats = [
        'total_files' => 0,
        'files_scanned' => 0,
        'inline_styles' => 0,
        'inline_scripts' => 0,
        'event_handlers' => 0,
        'style_blocks' => 0,
        'script_blocks' => 0
    ];

    public function __construct() {
        $this->viewsPath = dirname(__DIR__) . '/resources/views';
    }

    public function scan() {
        $this->scanDirectory($this->viewsPath);
        return [
            'stats' => $this->stats,
            'violations' => $this->violations,
            'summary' => $this->generateSummary()
        ];
    }

    private function scanDirectory($dir) {
        $files = glob($dir . '/*.blade.php');
        $subdirs = glob($dir . '/*', GLOB_ONLYDIR);

        foreach ($files as $file) {
            $this->scanFile($file);
        }

        foreach ($subdirs as $subdir) {
            $this->scanDirectory($subdir);
        }
    }

    private function scanFile($filePath) {
        $this->stats['total_files']++;
        $this->stats['files_scanned']++;
        
        $content = file_get_contents($filePath);
        $lines = explode("\n", $content);
        $relativePath = str_replace(dirname(__DIR__) . '/', '', $filePath);

        foreach ($lines as $lineNumber => $line) {
            $lineNum = $lineNumber + 1;
            
            // Check for inline styles
            if (preg_match('/style\s*=\s*["\'][^"\']*["\']/', $line)) {
                $this->stats['inline_styles']++;
                $this->violations[] = [
                    'type' => 'inline_style',
                    'file' => $relativePath,
                    'line' => $lineNum,
                    'content' => trim($line),
                    'severity' => 'medium'
                ];
            }

            // Check for inline event handlers
            if (preg_match('/on\w+\s*=\s*["\'][^"\']*["\']/', $line)) {
                $this->stats['event_handlers']++;
                $this->violations[] = [
                    'type' => 'event_handler',
                    'file' => $relativePath,
                    'line' => $lineNum,
                    'content' => trim($line),
                    'severity' => 'high'
                ];
            }

            // Check for style blocks
            if (preg_match('/<style[^>]*>/', $line)) {
                $this->stats['style_blocks']++;
                $this->violations[] = [
                    'type' => 'style_block',
                    'file' => $relativePath,
                    'line' => $lineNum,
                    'content' => trim($line),
                    'severity' => 'high'
                ];
            }

            // Check for script blocks (excluding external scripts)
            if (preg_match('/<script(?![^>]*src=)[^>]*>/', $line)) {
                $this->stats['script_blocks']++;
                $this->violations[] = [
                    'type' => 'script_block',
                    'file' => $relativePath,
                    'line' => $lineNum,
                    'content' => trim($line),
                    'severity' => 'high'
                ];
            }
        }
    }

    private function generateSummary() {
        $totalViolations = count($this->violations);
        $highSeverity = count(array_filter($this->violations, function($v) { return $v['severity'] === 'high'; }));
        $mediumSeverity = count(array_filter($this->violations, function($v) { return $v['severity'] === 'medium'; }));

        $filesWithViolations = array_unique(array_column($this->violations, 'file'));
        $cleanFiles = $this->stats['total_files'] - count($filesWithViolations);

        return [
            'total_violations' => $totalViolations,
            'high_severity' => $highSeverity,
            'medium_severity' => $mediumSeverity,
            'files_with_violations' => count($filesWithViolations),
            'clean_files' => $cleanFiles,
            'compliance_percentage' => $totalViolations > 0 ? round(($cleanFiles / $this->stats['total_files']) * 100, 2) : 100
        ];
    }

    public function getViolationsByType($type) {
        return array_filter($this->violations, function($v) use ($type) {
            return $v['type'] === $type;
        });
    }

    public function getViolationsByFile($file) {
        return array_filter($this->violations, function($v) use ($file) {
            return $v['file'] === $file;
        });
    }
}

// Handle API requests
$action = $_GET['action'] ?? 'scan';

try {
    $scanner = new CSPScanner();
    
    switch ($action) {
        case 'scan':
            $result = $scanner->scan();
            break;
            
        case 'violations_by_type':
            $type = $_GET['type'] ?? '';
            $scanner->scan(); // Need to scan first
            $result = $scanner->getViolationsByType($type);
            break;
            
        case 'violations_by_file':
            $file = $_GET['file'] ?? '';
            $scanner->scan(); // Need to scan first
            $result = $scanner->getViolationsByFile($file);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
    echo json_encode([
        'success' => true,
        'data' => $result,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
