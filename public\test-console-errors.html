<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Console <PERSON>rror Test - CSP Compliance</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #5a6fd8;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .warning {
            background-color: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .console-log {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .page-link {
            display: inline-block;
            margin: 5px;
            padding: 8px 15px;
            background: #f0f0f0;
            color: #333;
            text-decoration: none;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .page-link:hover {
            background: #e0e0e0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Console Error Test Suite</h1>
            <p>Test pages for console errors after CSP compliance changes</p>
        </div>

        <div class="test-section">
            <h3>📊 Test Summary</h3>
            <div id="test-summary">
                <div class="success">✅ All PHP files passed syntax check</div>
                <div class="success">✅ All JavaScript files created successfully</div>
                <div class="success">✅ All CSS files created successfully</div>
                <div class="warning">⚠️ Ready to test pages for console errors</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🌐 Fixed Pages to Test</h3>
            <p>Click on these links to test the pages we've fixed. Check browser console (F12) for errors:</p>
            
            <div>
                <h4>✅ Fully Fixed Pages:</h4>
                <a href="/course/purchased-lecture-detail" class="page-link" target="_blank">📹 Purchased Lecture Detail</a>
                <a href="/signup" class="page-link" target="_blank">📝 Signup Page</a>
                <a href="/course/lecture-detail" class="page-link" target="_blank">📚 Lecture Detail</a>
                <a href="/course/course-detail" class="page-link" target="_blank">🎓 Course Detail</a>
            </div>

            <div style="margin-top: 15px;">
                <h4>⚠️ Still Need Fixing:</h4>
                <a href="/course/purchased-course-detail" class="page-link" target="_blank">📖 Purchased Course Detail (11 violations)</a>
                <a href="/" class="page-link" target="_blank">🏠 Home/Navbar (9 violations)</a>
                <a href="/rtl" class="page-link" target="_blank">🔄 RTL Page (7 violations)</a>
                <a href="/payment/pending" class="page-link" target="_blank">💳 Payment Pending (7 violations)</a>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 Console Error Checker</h3>
            <button class="test-button" onclick="startConsoleMonitoring()">Start Console Monitoring</button>
            <button class="test-button" onclick="stopConsoleMonitoring()">Stop Console Monitoring</button>
            <button class="test-button" onclick="clearConsoleLog()">Clear Log</button>
            
            <div class="results">
                <h4>Console Output:</h4>
                <div class="console-log" id="console-output">
                    Console monitoring not started. Click "Start Console Monitoring" to begin.
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 Testing Checklist</h3>
            <div id="testing-checklist">
                <div class="warning">
                    <h4>Manual Testing Steps:</h4>
                    <ol>
                        <li>Open browser Developer Tools (F12)</li>
                        <li>Go to Console tab</li>
                        <li>Click on each "Fixed Pages" link above</li>
                        <li>Check for JavaScript errors in console</li>
                        <li>Verify all functionality works (buttons, forms, etc.)</li>
                        <li>Test password toggles on signup page</li>
                        <li>Test video controls on lecture pages</li>
                        <li>Test form submissions</li>
                    </ol>
                </div>
                
                <div class="success">
                    <h4>Expected Results:</h4>
                    <ul>
                        <li>✅ No JavaScript errors in console</li>
                        <li>✅ All CSS styles applied correctly</li>
                        <li>✅ All interactive elements working</li>
                        <li>✅ Forms submitting properly</li>
                        <li>✅ Password toggles functioning</li>
                        <li>✅ Video controls responsive</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🛠️ Quick Fixes</h3>
            <div class="warning">
                <h4>If you find console errors:</h4>
                <ol>
                    <li>Note the error message and file</li>
                    <li>Check if external CSS/JS files are loading</li>
                    <li>Verify file paths are correct</li>
                    <li>Check for missing CSS classes</li>
                    <li>Ensure JavaScript functions are defined</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        let consoleMonitoring = false;
        let originalConsole = {};

        function startConsoleMonitoring() {
            if (consoleMonitoring) return;
            
            consoleMonitoring = true;
            const output = document.getElementById('console-output');
            output.innerHTML = 'Console monitoring started...\n';
            
            // Store original console methods
            originalConsole.log = console.log;
            originalConsole.error = console.error;
            originalConsole.warn = console.warn;
            
            // Override console methods
            console.log = function(...args) {
                originalConsole.log.apply(console, args);
                output.innerHTML += '[LOG] ' + args.join(' ') + '\n';
                output.scrollTop = output.scrollHeight;
            };
            
            console.error = function(...args) {
                originalConsole.error.apply(console, args);
                output.innerHTML += '[ERROR] ' + args.join(' ') + '\n';
                output.scrollTop = output.scrollHeight;
            };
            
            console.warn = function(...args) {
                originalConsole.warn.apply(console, args);
                output.innerHTML += '[WARN] ' + args.join(' ') + '\n';
                output.scrollTop = output.scrollHeight;
            };
            
            // Listen for unhandled errors
            window.addEventListener('error', function(e) {
                output.innerHTML += '[ERROR] ' + e.message + ' at ' + e.filename + ':' + e.lineno + '\n';
                output.scrollTop = output.scrollHeight;
            });
        }

        function stopConsoleMonitoring() {
            if (!consoleMonitoring) return;
            
            consoleMonitoring = false;
            
            // Restore original console methods
            console.log = originalConsole.log;
            console.error = originalConsole.error;
            console.warn = originalConsole.warn;
            
            const output = document.getElementById('console-output');
            output.innerHTML += '\nConsole monitoring stopped.\n';
        }

        function clearConsoleLog() {
            document.getElementById('console-output').innerHTML = 'Console log cleared.\n';
        }

        // Test basic functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Console Error Test Suite loaded successfully');
        });
    </script>
</body>
</html>
