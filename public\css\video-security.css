/* Video Security Enhancements */

/* Prevent text selection on video containers */
.video-container,
.video-player,
#youtube-player {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
}

/* Disable right-click context menu */
.video-container,
.video-player {
    -webkit-context-menu: none;
    -moz-context-menu: none;
    context-menu: none;
}

/* Prevent drag and drop */
.video-container *,
.video-player * {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
    pointer-events: auto;
}

/* Security overlay to prevent easy inspection */
.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    z-index: 1;
    pointer-events: none;
}

/* Loading state styling */
.video-player .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Secure video player container */
.secure-video-container {
    position: relative;
    overflow: hidden;
    background: #000;
    border-radius: 8px;
}

/* Hide any potential iframe elements during loading */
.video-player iframe {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-player.loaded iframe {
    opacity: 1;
}

/* Anti-debugging styles */
.no-devtools {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: #000;
    color: #fff;
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    font-family: Arial, sans-serif;
}

/* Error message styling */
.video-error {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
}

.video-error i {
    color: #ffc107;
    margin-bottom: 1rem;
}

/* Plyr customizations for security */
.plyr {
    border-radius: 8px;
    overflow: hidden;
}

.plyr__video-wrapper {
    background: #000;
}

/* Hide any data attributes in developer tools */
[data-plyr-embed-id],
[data-fallback-url] {
    position: relative;
}

/* Security notice styling */
.security-notice {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #dc3545;
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    z-index: 10000;
    font-size: 14px;
    display: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .video-container {
        border-radius: 4px;
    }
    
    .security-notice {
        top: 10px;
        right: 10px;
        left: 10px;
        text-align: center;
    }
}

/* Print protection */
@media print {
    .video-container,
    .video-player,
    #youtube-player {
        display: none !important;
    }
    
    .video-container::after {
        content: "Video content is not available in print mode";
        display: block;
        padding: 2rem;
        text-align: center;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
    }
}

/* Additional security measures */
.video-protected::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    z-index: 0;
    pointer-events: none;
}

/* Disable image saving */
.video-container img,
.video-player img {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
}

/* Loading animation */
@keyframes secureLoading {
    0% { opacity: 0.5; }
    50% { opacity: 1; }
    100% { opacity: 0.5; }
}

.video-player .spinner-border {
    animation: secureLoading 1.5s ease-in-out infinite;
}
