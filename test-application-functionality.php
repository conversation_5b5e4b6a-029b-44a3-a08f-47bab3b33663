<?php
/**
 * Application Functionality Test
 * Verifies the application works correctly after security fixes
 */

$baseUrl = 'http://127.0.0.1:8000';

echo "🧪 APPLICATION FUNCTIONALITY TEST\n";
echo "=================================\n\n";

// Test legitimate application endpoints
$legitimateEndpoints = [
    '/' => 'Home Page',
    '/dashboard' => 'Dashboard',
    '/sign-in' => 'Sign In Page',
    '/sign-up' => 'Sign Up Page',
    '/courses' => 'Courses Page'
];

$totalTests = 0;
$passedTests = 0;

foreach ($legitimateEndpoints as $endpoint => $description) {
    echo "Testing: $description ($endpoint)\n";
    $totalTests++;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $baseUrl . $endpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "  ❌ Error: $error\n";
    } elseif ($httpCode === 200) {
        echo "  ✅ Working correctly (200 OK)\n";
        $passedTests++;
    } elseif ($httpCode === 302 || $httpCode === 301) {
        echo "  ✅ Redirecting correctly ($httpCode)\n";
        $passedTests++;
    } else {
        echo "  ⚠️  HTTP $httpCode - May need review\n";
    }
    echo "\n";
}

echo "📊 FUNCTIONALITY TEST SUMMARY\n";
echo "=============================\n";
echo "Total Tests: $totalTests\n";
echo "Passed: $passedTests ✅\n";
echo "Success Rate: " . round(($passedTests / $totalTests) * 100, 1) . "%\n\n";

if ($passedTests === $totalTests) {
    echo "🎉 ALL FUNCTIONALITY TESTS PASSED!\n";
    echo "Your application is working correctly after security fixes.\n";
} else {
    echo "⚠️  Some functionality issues detected.\n";
    echo "Please review the failed tests above.\n";
}
?>
