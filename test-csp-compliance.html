<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSP Compliance Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .console-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>CSP Compliance Test for Course Detail Page</h1>
        <p>This page will help you test CSP compliance by checking for violations when loading the course detail page.</p>
        
        <div class="test-result" id="test-status">
            <strong>Status:</strong> Ready to test
        </div>

        <button class="test-button" onclick="testCoursePage()">Test Course Page CSP Compliance</button>
        <button class="test-button" onclick="clearConsole()">Clear Console</button>
        <button class="test-button" onclick="checkYouTubeEmbeds()">Test YouTube Embeds</button>

        <h3>Console Output:</h3>
        <div class="console-output" id="console-output">
            Console messages will appear here...
        </div>

        <h3>Instructions:</h3>
        <ol>
            <li>Open your browser's Developer Tools (F12)</li>
            <li>Go to the Console tab</li>
            <li>Click "Test Course Page CSP Compliance" button</li>
            <li>Check for any CSP violation messages in the console</li>
            <li>If you see violations, they will be displayed below</li>
        </ol>

        <h3>Common CSP Violations to Look For:</h3>
        <ul>
            <li><strong>Inline styles:</strong> style="..." attributes</li>
            <li><strong>Inline scripts:</strong> onclick="..." or similar event handlers</li>
            <li><strong>Unsafe eval:</strong> Use of eval() or similar functions</li>
            <li><strong>Blocked resources:</strong> External scripts, styles, or images from non-whitelisted domains</li>
        </ul>
    </div>

    <script>
        let consoleOutput = document.getElementById('console-output');
        let testStatus = document.getElementById('test-status');

        // Capture console messages
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;

        function logToOutput(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            consoleOutput.textContent += logEntry;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            logToOutput(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            logToOutput(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            logToOutput(args.join(' '), 'warn');
        };

        // Listen for CSP violations
        document.addEventListener('securitypolicyviolation', function(e) {
            const violation = {
                blockedURI: e.blockedURI,
                violatedDirective: e.violatedDirective,
                originalPolicy: e.originalPolicy,
                sourceFile: e.sourceFile,
                lineNumber: e.lineNumber
            };
            
            logToOutput(`CSP VIOLATION: ${e.violatedDirective} - ${e.blockedURI}`, 'error');
            testStatus.innerHTML = '<strong>Status:</strong> <span style="color: red;">CSP Violations Detected!</span>';
            testStatus.className = 'test-result error';
        });

        function testCoursePage() {
            testStatus.innerHTML = '<strong>Status:</strong> Testing in progress...';
            testStatus.className = 'test-result warning';
            
            logToOutput('Starting CSP compliance test...', 'log');
            logToOutput('Opening course detail page in new window...', 'log');
            
            // Open the course page in a new window
            const courseWindow = window.open('http://127.0.0.1:8000/my-lecture-detail/1/1', '_blank');
            
            if (courseWindow) {
                logToOutput('Course page opened successfully', 'log');
                logToOutput('Check the browser console for any CSP violations', 'log');
                logToOutput('Also check the Network tab for blocked resources', 'log');
                
                setTimeout(() => {
                    if (testStatus.className.includes('error')) {
                        // Already detected violations
                        return;
                    }
                    testStatus.innerHTML = '<strong>Status:</strong> <span style="color: green;">No CSP violations detected in this window</span>';
                    testStatus.className = 'test-result success';
                    logToOutput('Test completed - check the opened page for any console errors', 'log');
                }, 3000);
            } else {
                logToOutput('Failed to open course page - popup blocked?', 'error');
                testStatus.innerHTML = '<strong>Status:</strong> <span style="color: red;">Failed to open test page</span>';
                testStatus.className = 'test-result error';
            }
        }

        function clearConsole() {
            consoleOutput.textContent = 'Console cleared...\n';
            testStatus.innerHTML = '<strong>Status:</strong> Ready to test';
            testStatus.className = 'test-result';
        }

        function checkYouTubeEmbeds() {
            logToOutput('Testing YouTube embed functionality...', 'log');
            
            // Create a test YouTube iframe
            const testFrame = document.createElement('iframe');
            testFrame.src = 'https://www.youtube.com/embed/dQw4w9WgXcQ';
            testFrame.width = '560';
            testFrame.height = '315';
            testFrame.style.display = 'none';
            
            testFrame.onload = function() {
                logToOutput('YouTube iframe loaded successfully', 'log');
                document.body.removeChild(testFrame);
            };
            
            testFrame.onerror = function() {
                logToOutput('YouTube iframe failed to load - possible CSP issue', 'error');
                document.body.removeChild(testFrame);
            };
            
            document.body.appendChild(testFrame);
        }

        // Initial log
        logToOutput('CSP Compliance Test Tool Ready', 'log');
        logToOutput('Click "Test Course Page CSP Compliance" to begin testing', 'log');
    </script>
</body>
</html>
