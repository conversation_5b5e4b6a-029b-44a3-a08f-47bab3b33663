# Design Document

## Overview

This document outlines the design approach for resolving the remaining Content Security Policy (CSP) violations in the application. The focus is on eliminating inline script and style violations by refactoring code to use CSP-compliant alternatives.

## Architecture

The solution will maintain the existing CSP architecture while addressing specific violations:

1. **CSP Configuration**: The existing CSP configuration in `config/csp.php` will remain unchanged, as it's already properly set up with nonces.

2. **Middleware**: The existing middleware (`EnhancedContentSecurityPolicy`) will continue to handle nonce generation and CSP header application.

3. **Code Refactoring**: The focus will be on refactoring specific code patterns that violate CSP rules:
   - Replace `javascript:void(0)` links with proper event handling
   - Replace inline style manipulation with CSS class-based approaches
   - Configure or patch third-party libraries that inject inline styles

## Components and Interfaces

### HTML Templates

Templates need to be modified to remove CSP-violating patterns:

1. **Link Elements**: 
   - Replace `href="javascript:void(0)"` with `href="#"` and add `event.preventDefault()` in JavaScript event handlers
   - Remove any inline event handlers (e.g., `onclick`, `onchange`)

2. **Style Elements**:
   - Ensure all `<style>` tags have proper nonce attributes
   - Move any remaining inline styles to external CSS files

### JavaScript Files

JavaScript files need to be updated to handle dynamic UI changes in a CSP-compliant way:

1. **Event Handling**:
   - Add proper event listeners in external JS files
   - Ensure all event handlers prevent default behavior for links that should not navigate

2. **Dynamic Styling**:
   - Replace direct style manipulation (`element.style.property = value`) with CSS class manipulation
   - Create predefined CSS classes for all possible style variations

3. **Third-Party Library Configuration**:
   - Identify libraries that inject inline styles (e.g., Perfect Scrollbar)
   - Configure these libraries to use CSP-compliant methods or disable problematic features

### CSS Files

CSS files need to be updated to support the class-based approach:

1. **Theme Classes**:
   - Ensure all theme variations have corresponding CSS classes
   - Create CSS classes for all dynamically applied styles

## Data Models

No changes to data models are required for this feature.

## Error Handling

1. **CSP Violation Monitoring**:
   - Continue using the existing CSP reporting mechanism
   - Monitor for any new violations after implementing changes

2. **Graceful Degradation**:
   - Ensure the application functions correctly even if some dynamic styling features are limited by CSP

## Testing Strategy

1. **Manual Testing**:
   - Test all pages with browser developer tools to check for CSP violations
   - Verify that all dynamic UI features still work correctly

2. **Automated Testing**:
   - Update existing CSP tests to verify no violations occur
   - Add specific tests for the refactored components

## Implementation Approach

The implementation will follow these steps:

1. First, address the most critical violations (those appearing in frequently used pages)
2. Refactor code in a way that maintains existing functionality
3. Create reusable patterns for CSP-compliant dynamic styling
4. Document the approach for future development