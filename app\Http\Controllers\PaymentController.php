<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\UserCourse;
use App\Models\PaymentMethod;
use App\Models\Lecture;

class PaymentController extends Controller
{
    public function success($order)
    {
        $order = Order::findOrFail($order);
        $userId = auth()->id();

        // Update order status to 'paid' or 'completed'
        if ($order->status !== 'completed') {
            $order->update(['status' => 'paid']);
        }

        // Grant user access to the purchased courses and lectures
        $cartItems = json_decode($order->cart_items, true);

        // Log the cart items for debugging
        \Illuminate\Support\Facades\Log::info('Cart items in order success:', ['items' => $cartItems]);

        if (is_array($cartItems)) {
            foreach ($cartItems as $item) {
                \Illuminate\Support\Facades\Log::info('Processing item:', ['item' => $item]);

                if (isset($item['course_id'])) {
                    // Check if user already has access to this course
                    $existingAccess = UserCourse::where([
                        'user_id' => $userId,
                        'course_id' => $item['course_id'],
                        'lecture_id' => null,
                        'status' => 'active'
                    ])->first();

                    if (!$existingAccess) {
                        $courseAccess = UserCourse::create([
                            'user_id' => $userId,
                            'course_id' => $item['course_id'],
                            'lecture_id' => null,
                            'status' => 'active',
                            'order_id' => $order->id
                        ]);
                        \Illuminate\Support\Facades\Log::info('Created course access:', ['access' => $courseAccess]);
                    } else {
                        \Illuminate\Support\Facades\Log::info('User already has course access:', ['course_id' => $item['course_id']]);
                    }
                } elseif (isset($item['lecture_id'])) {
                    // For lecture purchases, we need to get the associated course_id
                    $lecture = Lecture::find($item['lecture_id']);

                    if ($lecture) {
                        // Check if user already has access to this lecture
                        $existingAccess = UserCourse::where([
                            'user_id' => $userId,
                            'course_id' => $lecture->course_id,
                            'lecture_id' => $item['lecture_id'],
                            'status' => 'active'
                        ])->first();

                        if (!$existingAccess) {
                            $lectureAccess = UserCourse::create([
                                'user_id' => $userId,
                                'course_id' => $lecture->course_id,
                                'lecture_id' => $item['lecture_id'],
                                'status' => 'active',
                                'order_id' => $order->id
                            ]);
                            \Illuminate\Support\Facades\Log::info('Created lecture access:', ['access' => $lectureAccess]);
                        } else {
                            \Illuminate\Support\Facades\Log::info('User already has lecture access:', ['lecture_id' => $item['lecture_id']]);
                        }
                    } else {
                        \Illuminate\Support\Facades\Log::warning('Lecture not found', ['lecture_id' => $item['lecture_id']]);
                    }
                } else {
                    \Illuminate\Support\Facades\Log::warning('Item has no course_id or lecture_id', ['item' => $item]);
                }
            }
        } else {
            \Illuminate\Support\Facades\Log::error('Cart items is not an array', ['cart_items' => $order->cart_items]);
        }

        // Clear the user's cart items
        \App\Models\Shoppingcart::where('user_id', $userId)->delete();

        return redirect()->route('user.dashboard')
            ->with('success', 'Payment successful! You now have access to the courses.');
    }

    public function cancel($order)
    {
        $order = Order::findOrFail($order);

        // Update order status to 'failed'
        $order->update(['status' => 'failed']);

        return redirect()->route('checkout')->with('error', 'Payment was cancelled. Please try again.');
    }

    public function pending($order)
    {
        $order = Order::findOrFail($order);

        // Get payment method details from database
        $paymentMethod = \App\Models\PaymentMethod::where('key', $order->payment_method)->first();

        // Get payment instructions
        $instructions = $paymentMethod ? $paymentMethod->instructions : $this->getDefaultInstructions($order->payment_method);

        return view('payment.pending', [
            'order' => $order,
            'message' => $instructions,
            'paymentMethod' => $paymentMethod,
        ]);
    }

    private function getDefaultInstructions($paymentMethod)
    {
        switch ($paymentMethod) {
            case 'cash':
                return 'Please visit our office to make your cash payment. Remember to bring your order number.';

            case 'jazzcash':
                return 'Please send your payment to our Jazz Cash account: +92 333 1234567 and send the screenshot to +92 312 9876543.';

            case 'easypaisa':
                return 'Please send your payment to our Easypaisa account: +92 345 1234567 and send the screenshot to +92 312 9876543.';

            case 'banktransfer':
                return 'Please transfer the amount to our bank account: 1234-5678-9012-3456 (HBL Pakistan) and send the payment confirmation to +92 312 9876543.';

            default:
                return 'Your order is being processed. You will receive further instructions shortly.';
        }
    }
}
