@extends('admin.layout')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Payment Methods</h1>
    </div>

    @if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif

    @if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Manage Payment Methods</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                Drag and drop to change the order of payment methods. Toggle the status to enable/disable a payment method.
            </div>

            <div class="table-responsive">
                <table class="table table-bordered" id="paymentMethodsTable">
                    <thead>
                        <tr>
                            <th width="5%">#</th>
                            <th width="15%">Name</th>
                            <th width="20%">Description</th>
                            <th width="30%">Details</th>
                            <th width="10%">Status</th>
                            <th width="20%">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="sortable-payment-methods">
                        @foreach($paymentMethods as $method)
                        <tr data-id="{{ $method->id }}" class="cursor-move">
                            <td><i class="fas fa-grip-vertical text-muted"></i></td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="me-2">
                                        @if(Str::startsWith($method->icon, 'fas '))
                                            <i class="{{ $method->icon }} fa-lg {{ $method->details['color'] ?? '' }}"></i>
                                        @else
                                            <img src="{{ asset($method->icon) }}" alt="{{ $method->name }}" width="24">
                                        @endif
                                    </div>
                                    <span>{{ $method->name }}</span>
                                </div>
                            </td>
                            <td>{{ $method->description }}</td>
                            <td>
                                @if($method->key === 'cash')
                                    <small class="text-muted">Cash payment at office</small>
                                @elseif($method->key === 'jazzcash')
                                    <small class="text-muted">Account: {{ $method->details['account'] ?? 'Not set' }}</small>
                                @elseif($method->key === 'easypaisa')
                                    <small class="text-muted">Account: {{ $method->details['account'] ?? 'Not set' }}</small>
                                @elseif($method->key === 'banktransfer')
                                    <small class="text-muted">
                                        Bank: {{ $method->details['bank_name'] ?? 'Not set' }}<br>
                                        Account: {{ $method->details['account_number'] ?? 'Not set' }}
                                    </small>
                                @elseif($method->key === 'card')
                                    <small class="text-muted">Payment processor: {{ ucfirst($method->details['processor'] ?? 'stripe') }}</small>
                                @endif
                            </td>
                            <td>
                                <div class="form-check form-switch">
                                    <form action="{{ route('admin.payment-methods.toggle-status', $method->id) }}" method="POST" class="toggle-status-form">
                                        @csrf
                                        <input class="form-check-input" type="checkbox" role="switch" id="status-{{ $method->id }}"
                                            {{ $method->is_active ? 'checked' : '' }} onChange="this.form.submit()">
                                        <label class="form-check-label" for="status-{{ $method->id }}">
                                            {{ $method->is_active ? 'Active' : 'Inactive' }}
                                        </label>
                                    </form>
                                </div>
                            </td>
                            <td>
                                <a href="{{ route('admin.payment-methods.edit', $method->id) }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.13.2/jquery-ui.min.js"></script>
<script>
    $(function() {
        $("#sortable-payment-methods").sortable({
            handle: '.cursor-move',
            update: function(event, ui) {
                const order = [];
                $('#sortable-payment-methods tr').each(function() {
                    order.push($(this).data('id'));
                });

                // Send the new order to the server
                $.ajax({
                    url: '{{ route("admin.payment-methods.update-order") }}',
                    method: 'POST',
                    data: {
                        order: order,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            // Show success message
                            const alert = $('<div class="alert alert-success alert-dismissible fade show" role="alert">')
                                .text('Payment method order updated successfully')
                                .append('<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>');

                            $('.card-body').prepend(alert);

                            // Auto-dismiss the alert after 3 seconds
                            setTimeout(function() {
                                alert.alert('close');
                            }, 3000);
                        }
                    },
                    error: function() {
                        alert('An error occurred while updating the order');
                    }
                });
            }
        });
    });
</script>
@endpush

@push('styles')
<style>
    .cursor-move {
        cursor: move;
    }
    .ui-sortable-helper {
        display: table;
        background-color: #f8f9fc;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
</style>
@endpush
