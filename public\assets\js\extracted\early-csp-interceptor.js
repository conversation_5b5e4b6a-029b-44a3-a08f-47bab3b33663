/**
 * Early CSP Interceptor
 * This script runs immediately and intercepts any inline content before it causes violations
 */

(function() {
    'use strict';
    
    // Get nonce immediately
    const nonce = document.querySelector('meta[name="csp-nonce"]')?.getAttribute('content');
    
    if (!nonce) {
        // Try to get nonce from script tag itself
        const currentScript = document.currentScript;
        if (currentScript && currentScript.hasAttribute('nonce')) {
            window.CSP_NONCE = currentScript.getAttribute('nonce');
        }
        return;
    }
    
    window.CSP_NONCE = nonce;
    
    let styleCounter = 0;
    
    // Function to create safe styles
    function createSafeStyle(css) {
        const style = document.createElement('style');
        style.setAttribute('nonce', nonce);
        style.textContent = css;
        document.head.appendChild(style);
        return style;
    }
    
    // Override document.createElement to add nonces automatically
    const originalCreateElement = document.createElement;
    document.createElement = function(tagName) {
        const element = originalCreateElement.call(this, tagName);
        
        if (tagName.toLowerCase() === 'style') {
            element.setAttribute('nonce', nonce);
        } else if (tagName.toLowerCase() === 'script') {
            element.setAttribute('nonce', nonce);
        }
        
        return element;
    };
    
    // Override setAttribute to intercept style attributes
    const originalSetAttribute = Element.prototype.setAttribute;
    Element.prototype.setAttribute = function(name, value) {
        if (name === 'style' && value && value.trim()) {
            // Instead of setting inline style, create a class
            const className = 'eci-' + Date.now() + '-' + (++styleCounter);
            this.classList.add(className);
            createSafeStyle('.' + className + ' { ' + value + ' }');
            return;
        }
        
        return originalSetAttribute.call(this, name, value);
    };
    
    // Override innerHTML to catch script injections
    const originalInnerHTMLDescriptor = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML');
    if (originalInnerHTMLDescriptor && originalInnerHTMLDescriptor.set) {
        Object.defineProperty(Element.prototype, 'innerHTML', {
            set: function(value) {
                if (typeof value === 'string') {
                    // Add nonce to any script tags in the HTML
                    value = value.replace(/<script(?![^>]*nonce=)/gi, '<script nonce="' + nonce + '"');
                    // Add nonce to any style tags in the HTML
                    value = value.replace(/<style(?![^>]*nonce=)/gi, '<style nonce="' + nonce + '"');
                }
                return originalInnerHTMLDescriptor.set.call(this, value);
            },
            get: originalInnerHTMLDescriptor.get
        });
    }
    
    // Override insertAdjacentHTML
    const originalInsertAdjacentHTML = Element.prototype.insertAdjacentHTML;
    Element.prototype.insertAdjacentHTML = function(position, text) {
        if (typeof text === 'string') {
            // Add nonce to any script tags
            text = text.replace(/<script(?![^>]*nonce=)/gi, '<script nonce="' + nonce + '"');
            // Add nonce to any style tags
            text = text.replace(/<style(?![^>]*nonce=)/gi, '<style nonce="' + nonce + '"');
        }
        return originalInsertAdjacentHTML.call(this, position, text);
    };
    
    console.log('Early CSP Interceptor: Initialized with nonce:', nonce);
    
})();