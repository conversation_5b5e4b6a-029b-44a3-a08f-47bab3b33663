@extends('admin.layout')

@section('title', 'Orders Management')

@section('header', 'Orders Management')

@section('content')
    <div class="row mb-4">
        <div class="col-12">
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">All Orders</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Order ID</th>
                            <th>Customer</th>
                            <th>Total</th>
                            <th>Discount</th>
                            <th>Final Amount</th>
                            <th>Status</th>
                            <th>Payment Method</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($orders as $order)
                            <tr class="{{ $order->status === 'pending' || $order->status === 'awaiting_payment' ? 'table-warning' : ($order->status === 'paid' || $order->status === 'completed' ? 'table-success' : ($order->status === 'rejected' ? 'table-danger' : '')) }}">
                                <td>{{ $order->id }}</td>
                                <td>
                                    @if($order->user)
                                        {{ $order->user->name ?? ($order->user->first_name . ' ' . $order->user->last_name) }}
                                    @else
                                        Unknown User
                                    @endif
                                </td>
                                <td>${{ number_format($order->total, 2) }}</td>
                                <td>${{ number_format($order->discount ?? 0, 2) }}</td>
                                <td>${{ number_format($order->final_total ?? ($order->total - ($order->discount ?? 0)), 2) }}</td>
                                <td>
                                    <span class="badge {{ $order->status === 'pending' || $order->status === 'awaiting_payment' ? 'bg-warning' : ($order->status === 'paid' || $order->status === 'completed' ? 'bg-success' : ($order->status === 'rejected' ? 'bg-danger' : 'bg-secondary')) }}">
                                        {{ ucfirst(str_replace('_', ' ', $order->status)) }}
                                    </span>
                                </td>
                                <td>{{ ucfirst($order->payment_method) }}</td>
                                <td>{{ $order->created_at->format('M d, Y H:i') }}</td>
                                <td class="text-center">
                                    <a href="{{ route('admin.order.show', $order->id) }}" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i> View
                                    </a>

                                    @if($order->status === 'pending' || $order->status === 'awaiting_payment')
                                        <form action="{{ route('admin.order.approve', $order->id) }}" method="POST" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-success btn-sm">
                                                <i class="fas fa-check"></i> Approve
                                            </button>
                                        </form>

                                        <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#rejectModal{{ $order->id }}">
                                            <i class="fas fa-times"></i> Reject
                                        </button>

                                        <!-- Reject Modal -->
                                        <div class="modal fade" id="rejectModal{{ $order->id }}" tabindex="-1" aria-labelledby="rejectModalLabel{{ $order->id }}" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="rejectModalLabel{{ $order->id }}">Reject Order #{{ $order->id }}</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <form action="{{ route('admin.order.reject', $order->id) }}" method="POST">
                                                        @csrf
                                                        <div class="modal-body">
                                                            <div class="mb-3">
                                                                <label for="reason" class="form-label">Reason for Rejection (Optional)</label>
                                                                <textarea class="form-control" id="reason" name="reason" rows="3"></textarea>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                            <button type="submit" class="btn btn-danger">Reject Order</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="text-center">No orders found</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-center mt-4">
                {{ $orders->links() }}
            </div>
        </div>
    </div>
@endsection
