/**
 * Pre-CSP Fix
 * Runs immediately to catch early violations
 */

(function() {
    'use strict';
    
    // Get nonce from current script or meta tag
    let nonce = document.currentScript?.getAttribute('nonce');
    if (!nonce) {
        const metaTag = document.querySelector('meta[name="csp-nonce"]');
        nonce = metaTag?.getAttribute('content');
    }
    
    if (!nonce) {
        return; // Can't fix without nonce
    }
    
    // Store nonce globally for other scripts
    window.CSP_NONCE = nonce;
    
    let counter = 0;
    
    // Function to fix violations immediately
    function immediateCSPFix() {
        // Fix inline styles
        const elementsWithStyle = document.querySelectorAll('[style]');
        elementsWithStyle.forEach(element => {
            const inlineStyle = element.getAttribute('style');
            if (inlineStyle && inlineStyle.trim()) {
                element.removeAttribute('style');
                const className = 'pcf-' + Date.now() + '-' + (++counter);
                element.classList.add(className);
                
                const style = document.createElement('style');
                style.setAttribute('nonce', nonce);
                style.textContent = '.' + className + ' { ' + inlineStyle + ' }';
                document.head.appendChild(style);
            }
        });
        
        // Fix scripts without nonce
        const scripts = document.querySelectorAll('script:not([src]):not([nonce])');
        scripts.forEach(script => {
            script.setAttribute('nonce', nonce);
        });
        
        // Fix styles without nonce
        const styles = document.querySelectorAll('style:not([nonce])');
        styles.forEach(style => {
            style.setAttribute('nonce', nonce);
        });
    }
    
    // Run immediately
    immediateCSPFix();
    
    // Run on DOM changes
    if (typeof MutationObserver !== 'undefined') {
        const observer = new MutationObserver(immediateCSPFix);
        observer.observe(document.documentElement, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style']
        });
    }
    
    // Run periodically
    setInterval(immediateCSPFix, 50);
    
})();