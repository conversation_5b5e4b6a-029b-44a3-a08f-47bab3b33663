<div class="container">
    <div class="d-flex justify-content-between align-items-center my-4">
        <h2 class="mb-0">
            @if($selectedInstructor)
                Courses by {{ $selectedInstructor }}
            @else
                Explore Courses & Lectures
            @endif
        </h2>
        @if($selectedInstructor)
            <a href="{{ route('courses') }}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i> Clear Instructor Filter
            </a>
        @endif
    </div>

    @if($selectedInstructor)
        <div class="alert alert-info mb-4">
            <div class="d-flex align-items-center">
                <i class="fas fa-filter me-2"></i>
                <span>Showing courses and lectures by instructor: <strong>{{ $selectedInstructor }}</strong></span>
            </div>
        </div>
    @endif

    <!-- Pricing Toggle -->
    <div class="d-flex justify-content-end mb-4">
        <div class="btn-group pricing-toggle" role="group">
            <button type="button" class="btn {{ $priceToggle === 'weekly' ? 'btn-primary' : 'btn-outline-primary' }}"
                wire:click="$set('priceToggle', 'weekly')">
                Weekly Price
            </button>
            <button type="button" class="btn {{ $priceToggle === 'monthly' ? 'btn-primary' : 'btn-outline-primary' }}"
                wire:click="$set('priceToggle', 'monthly')">
                Monthly Price
            </button>
        </div>
    </div>

    <!-- Filter/Category Section -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Filter Courses</h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6 class="fw-bold">Course Type</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" id="all" name="courseType" value="all"
                                wire:click="filterByType('all')" {{ $selectedType === 'all' ? 'checked' : '' }}>
                            <label class="form-check-label" for="all">All Courses</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" id="fullCourses" name="courseType" value="fullCourses"
                                wire:click="filterByType('fullCourses')" {{ $selectedType === 'fullCourses' ? 'checked' : '' }}>
                            <label class="form-check-label" for="fullCourses">Full Courses</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" id="lectures" name="courseType" value="lectures"
                                wire:click="filterByType('lectures')" {{ $selectedType === 'lectures' ? 'checked' : '' }}>
                            <label class="form-check-label" for="lectures">Individual Lectures</label>
                        </div>
                    </div>

                    <div>
                        <h6 class="fw-bold">Categories</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" id="catAll" name="categoryFilter" value="all"
                                wire:click="filterByCategory('all')" {{ $selectedCategory === 'all' ? 'checked' : '' }}>
                            <label class="form-check-label" for="catAll">All Categories</label>
                        </div>
                        @foreach($categories as $category)
                        <div class="form-check">
                            <input class="form-check-input" type="radio" id="cat{{$category->id}}" name="categoryFilter" value="{{$category->id}}"
                                wire:click="filterByCategory({{$category->id}})" {{ $selectedCategory == $category->id ? 'checked' : '' }}>
                            <label class="form-check-label" for="cat{{$category->id}}">{{$category->name}}</label>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-9">
            <!-- All Courses Grid -->
            <div class="row">
                <!-- Full Courses -->
                @if($selectedType === 'all' || $selectedType === 'fullCourses')
                    @foreach ($courses as $course)
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 course-card shadow-sm">
                            <div class="course-image-container">
                                <img src="{{ Storage::url($course->image_path) }}"
                                    alt="{{ $course->name }}"
                                    class="card-img-top course-image">
                                <div class="course-badge badge bg-primary">Full Course</div>
                            </div>
                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title">{{ $course->name }}</h5>
                                <p class="card-text small text-muted">
                                    <!-- We could add instructor name here if available -->
                                    <span class="d-block mb-2">
                                        <i class="fas fa-user-tie"></i> 
                                        @if($course->instructor)
                                            {{ $course->instructor }}
                                        @else
                                            Instructor
                                        @endif
                                    </span>
                                    <span class="d-block text-truncate mb-2" title="{{ strip_tags($course->description) }}">
                                        {{ Str::limit(strip_tags($course->description), 60) }}
                                    </span>
                                </p>

                                <!-- Ratings - Real ratings from database -->
                                <div class="ratings mb-2">
                                    @for ($i = 1; $i <= 5; $i++)
                                        @if ($i <= $course->average_rating)
                                            <i class="fas fa-star text-warning"></i>
                                        @elseif ($i - 0.5 <= $course->average_rating)
                                            <i class="fas fa-star-half-alt text-warning"></i>
                                        @else
                                            <i class="far fa-star text-warning"></i>
                                        @endif
                                    @endfor
                                    <span class="small text-muted ms-1">({{ number_format($course->average_rating, 1) }})</span>
                                </div>

                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="fw-bold text-primary">${{ $priceToggle === 'weekly' ? $course->weekly_price : $course->monthly_price }}</span>
                                        <a href="{{ route('course.detail', $course->id) }}" class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-info-circle me-1"></i> View Details
                                        </a>
                                    </div>
                                    <div class="d-grid">
                                        @if(auth()->check() && $this->isPurchased($course->id, 'course'))
                                            <button type="button" class="btn btn-sm btn-success" disabled>
                                                <i class="fas fa-check-circle me-1"></i> Purchased
                                            </button>
                                        @elseif(auth()->check() && $this->isPending($course->id, 'course'))
                                            <button type="button" class="btn btn-sm btn-warning" disabled>
                                                <i class="fas fa-clock me-1"></i> Pending Approval
                                            </button>
                                        @elseif(auth()->check() && $this->isInCart($course->id, 'course'))
                                            <button type="button" class="btn btn-sm btn-secondary" disabled>
                                                <i class="fas fa-check me-1"></i> Added to Cart
                                            </button>
                                        @else
                                            <button type="button" wire:click="addToCart({{ $course->id }}, '{{ $priceToggle }}')" class="btn btn-sm btn-primary">
                                                <i class="fas fa-cart-plus me-1"></i> Add to Cart
                                            </button>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                @endif

                <!-- Individual Lectures -->
                @if($selectedType === 'all' || $selectedType === 'lectures')
                    @foreach ($courses as $course)
                        @foreach ($course->lectures as $lecture)
                        <div class="col-md-4 mb-4">
                            <div class="card h-100 course-card shadow-sm">
                                <div class="course-image-container">
                                    <img src="{{ $lecture->image_path ? Storage::url($lecture->image_path) : 'https://via.placeholder.com/300x200' }}"
                                        alt="{{ $lecture->name }}"
                                        class="card-img-top course-image">
                                    <div class="course-badge badge bg-success">Lecture</div>
                                </div>
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title">{{ $lecture->name }}</h5>
                                    <p class="card-text small text-muted">
                                        <span class="d-block mb-1">
                                            <i class="fas fa-book"></i> Part of: {{ $course->name }}
                                        </span>
                                        <span class="d-block mb-1">
                                            <i class="fas fa-user-tie"></i> 
                                            @if($lecture->instructor)
                                                {{ $lecture->instructor }}
                                            @elseif($course->instructor)
                                                {{ $course->instructor }}
                                            @else
                                                Instructor
                                            @endif
                                        </span>
                                        <span class="d-block text-truncate mb-2" title="{{ strip_tags($lecture->description) }}">
                                            {{ Str::limit(strip_tags($lecture->description), 60) }}
                                        </span>
                                    </p>

                                    <!-- Ratings - Real ratings from database -->
                                    <div class="ratings mb-2">
                                        @for ($i = 1; $i <= 5; $i++)
                                            @if ($i <= $lecture->average_rating)
                                                <i class="fas fa-star text-warning"></i>
                                            @elseif ($i - 0.5 <= $lecture->average_rating)
                                                <i class="fas fa-star-half-alt text-warning"></i>
                                            @else
                                                <i class="far fa-star text-warning"></i>
                                            @endif
                                        @endfor
                                        <span class="small text-muted ms-1">({{ number_format($lecture->average_rating, 1) }})</span>
                                    </div>

                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="fw-bold text-primary">${{ $priceToggle === 'weekly' ? $lecture->weekly_price : $lecture->monthly_price }}</span>
                                            <a href="{{ route('lecture.detail', ['course' => $course->id, 'lecture' => $lecture->id]) }}" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-info-circle me-1"></i> View Details
                                            </a>
                                        </div>
                                        <div class="d-grid">
                                            @if(auth()->check() && $this->isPurchased($lecture->id, 'lecture'))
                                                <button type="button" class="btn btn-sm btn-success" disabled>
                                                    <i class="fas fa-check-circle me-1"></i> Purchased
                                                </button>
                                            @elseif(auth()->check() && $this->isPending($lecture->id, 'lecture'))
                                                <button type="button" class="btn btn-sm btn-warning" disabled>
                                                    <i class="fas fa-clock me-1"></i> Pending Approval
                                                </button>
                                            @elseif(auth()->check() && $this->isInCart($lecture->id, 'lecture'))
                                                <button type="button" class="btn btn-sm btn-secondary" disabled>
                                                    <i class="fas fa-check me-1"></i> Added to Cart
                                                </button>
                                            @else
                                                <button type="button" wire:click="addToCart({{ $lecture->id }}, '{{ $priceToggle }}', 'lecture')" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-cart-plus me-1"></i> Add to Cart
                                                </button>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    @endforeach
                @endif
            </div>
        </div>
    </div>

    <!-- Success and Error Messages -->
    @if (session()->has('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif
    <style>
    .course-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border-radius: 8px;
        overflow: hidden;
    }

    .course-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .course-image-container {
        position: relative;
        height: 160px;
        overflow: hidden;
    }

    .course-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .course-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        font-size: 0.7rem;
    }

    .pricing-toggle .btn {
        padding: 0.375rem 1rem;
    }
</style>
</div>


