<!-- resources/views/auth/verify-phone.blade.php -->
@extends('layouts.verify')

@section('main-content')
<div class="verification-container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="verification-card">
                <div class="verification-header">
                    <h3>Two-Factor Verification</h3>
                    <div class="timer-container">
                        <span id="timer-label">Time remaining:</span> <span id="timer">--:--</span>
                    </div>
                </div>

                <div class="verification-body">
                    @if (session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    @if (session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="alert alert-warning">
                        <strong>Important:</strong> You must verify both your email and phone number to access the rest of the site. You will be automatically redirected to this page until verification is complete.
                    </div>

                    <div class="verification-tabs">
                        <button class="tab-btn {{ $is_pakistan_number ? 'active' : '' }}" data-target="phone-tab" data-timer-id="phone">Phone Verification</button>
                        <button class="tab-btn {{ !$is_pakistan_number ? 'active' : '' }}" data-target="email-tab" data-timer-id="email">Email Verification</button>
                    </div>

                    <div id="phone-tab" class="tab-content {{ $is_pakistan_number ? 'active' : '' }}">
                        @if(!$phone_verified)
                            <form method="POST" action="{{ route('phone.verify') }}">
                                @csrf
                                <div class="verification-input-group">
                                    <label for="phone_code">Enter Phone Verification Code</label>
                                    <div class="otp-input-container">
                                        <input type="text" maxlength="1" class="otp-input" name="code[]" required>
                                        <input type="text" maxlength="1" class="otp-input" name="code[]" required>
                                        <input type="text" maxlength="1" class="otp-input" name="code[]" required>
                                        <input type="text" maxlength="1" class="otp-input" name="code[]" required>
                                        <input type="text" maxlength="1" class="otp-input" name="code[]" required>
                                        <input type="text" maxlength="1" class="otp-input" name="code[]" required>
                                    </div>
                                    <div class="code-hint">Enter the 6-digit code sent to your phone</div>
                                    @error('code')
                                        <div class="error-message">{{ $message }}</div>
                                    @enderror
                                </div>
                                <button type="submit" class="verify-btn">Verify Phone</button>
                            </form>

                            <div class="resend-container">
                                <form method="POST" action="{{ route('verification.resend') }}" id="resendPhoneForm">
                                    @csrf
                                    <input type="hidden" name="type" value="phone">
                                    <button type="submit" class="resend-btn" id="resendPhoneButton" {{ $phone_time_remaining ? 'disabled' : '' }}>
                                        Resend Phone Code
                                    </button>
                                </form>
                            </div>
                        @else
                            <div class="alert alert-success">
                                <strong>Phone verified!</strong> Your phone number has been successfully verified.
                            </div>
                        @endif
                    </div>

                    <div id="email-tab" class="tab-content {{ !$is_pakistan_number ? 'active' : '' }}">
                        @if(!$email_verified)
                            <form method="POST" action="{{ route('email.verify') }}">
                                @csrf
                                <div class="verification-input-group">
                                    <label for="email_code">Enter Email Verification Code</label>
                                    <div class="otp-input-container">
                                        <input type="text" maxlength="1" class="otp-input" name="email_code[]" required>
                                        <input type="text" maxlength="1" class="otp-input" name="email_code[]" required>
                                        <input type="text" maxlength="1" class="otp-input" name="email_code[]" required>
                                        <input type="text" maxlength="1" class="otp-input" name="email_code[]" required>
                                        <input type="text" maxlength="1" class="otp-input" name="email_code[]" required>
                                        <input type="text" maxlength="1" class="otp-input" name="email_code[]" required>
                                    </div>
                                    <div class="code-hint">Enter the 6-digit code sent to your email</div>
                                    @error('email_code')
                                        <div class="error-message">{{ $message }}</div>
                                    @enderror
                                </div>
                                <button type="submit" class="verify-btn">Verify Email</button>
                            </form>

                            <div class="resend-container">
                                <form method="POST" action="{{ route('verification.resend') }}" id="resendEmailForm">
                                    @csrf
                                    <input type="hidden" name="type" value="email">
                                    <button type="submit" class="resend-btn" id="resendEmailButton" {{ $email_time_remaining ? 'disabled' : '' }}>
                                        Resend Email Code
                                    </button>
                                </form>
                            </div>
                        @else
                            <div class="alert alert-success">
                                <strong>Email verified!</strong> Your email address has been successfully verified.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.verification-container {
    min-height: 100vh;
    padding: 40px 15px;
    background: #f8f9fa;
}

.verification-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.verification-header {
    background: #4a90e2;
    color: white;
    padding: 20px;
    text-align: center;
}

.verification-header h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.timer-container {
    margin-top: 10px;
    font-size: 18px;
    color: #fff;
    font-weight: 600;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 20px;
    display: inline-block;
}

#timer {
    font-family: 'Courier New', monospace;
    font-size: 20px;
    color: #ffeb3b;
    text-shadow: 0 0 5px rgba(255, 235, 59, 0.5);
}

.verification-body {
    padding: 30px;
}

.verification-tabs {
    display: flex;
    margin-bottom: 25px;
    border-bottom: 2px solid #eee;
}

.tab-btn {
    flex: 1;
    padding: 15px;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    margin-bottom: -2px;
    color: #666;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
}

.tab-btn.active {
    color: #4a90e2;
    border-bottom-color: #4a90e2;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.otp-input-container {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.otp-input {
    width: 45px;
    height: 45px;
    padding: 0;
    border: 2px solid #e1e1e1;
    border-radius: 8px;
    font-size: 20px;
    text-align: center;
    transition: border-color 0.3s;
}

.otp-input:focus {
    border-color: #4a90e2;
    outline: none;
}

.verification-input-group {
    margin-bottom: 25px;
}

.verification-input-group label {
    display: block;
    margin-bottom: 15px;
    color: #333;
    font-weight: 500;
}

.code-hint {
    font-size: 12px;
    color: #666;
    margin-top: 8px;
}

.error-message {
    color: #dc2626;
    font-size: 12px;
    margin-top: 8px;
}

.verify-btn {
    width: 100%;
    padding: 12px;
    background: #4a90e2;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
}

.verify-btn:hover {
    background: #357abd;
}

.resend-container {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.resend-btn {
    background: none;
    border: none;
    color: #4a90e2;
    font-size: 14px;
    cursor: pointer;
    padding: 0;
    text-decoration: underline;
}

.resend-btn:disabled {
    color: #999;
    cursor: not-allowed;
    text-decoration: none;
    font-size: 12px;
}

.resend-btn:not(:disabled) {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.alert {
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.alert-danger {
    background-color: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.alert-success {
    background-color: #dcfce7;
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeeba;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Phone and email timers - get values from PHP
    let phoneTimeRemaining = Math.floor({!! $phone_time_remaining ?? 0 !!});
    let emailTimeRemaining = Math.floor({!! $email_time_remaining ?? 0 !!});

    // Set default timer type based on user's phone number origin
    let currentTimerType = {!! $is_pakistan_number ? "'phone'" : "'email'" !!};

    // Force fresh timer values - prioritize server values over stored values
    let phoneTimeLeft = phoneTimeRemaining > 0 ? phoneTimeRemaining : 0;
    let emailTimeLeft = emailTimeRemaining > 0 ? emailTimeRemaining : 0;

    // If we have fresh timer values from server (after resend), use them
    if (phoneTimeRemaining > 0) {
        phoneTimeLeft = phoneTimeRemaining;
    }
    if (emailTimeRemaining > 0) {
        emailTimeLeft = emailTimeRemaining;
    }

    console.log('=== TIMER DEBUG INFO ===');
    console.log('Raw PHP values:', {
        phoneTimeRemaining: {!! $phone_time_remaining !== null ? $phone_time_remaining : 'null' !!},
        emailTimeRemaining: {!! $email_time_remaining !== null ? $email_time_remaining : 'null' !!}
    });

    console.log('JavaScript calculated values:', {
        phoneTimer: phoneTimeLeft,
        emailTimer: emailTimeLeft
    });

    console.log('Session flash data check:', {
        hasPhoneSession: {!! session('phone_time_remaining') ? 'true' : 'false' !!},
        hasEmailSession: {!! session('email_time_remaining') ? 'true' : 'false' !!},
        phoneSessionValue: {!! session('phone_time_remaining') !== null ? session('phone_time_remaining') : 'null' !!},
        emailSessionValue: {!! session('email_time_remaining') !== null ? session('email_time_remaining') : 'null' !!}
    });

    console.log('User verification status:', {
        phoneVerified: {!! $phone_verified ? 'true' : 'false' !!},
        emailVerified: {!! $email_verified ? 'true' : 'false' !!},
        isPakistanNumber: {!! $is_pakistan_number ? 'true' : 'false' !!}
    });
    console.log('=== END DEBUG INFO ===');

    const timerDisplay = document.getElementById('timer');
    const resendPhoneButton = document.getElementById('resendPhoneButton');
    const resendEmailButton = document.getElementById('resendEmailButton');
    let timerInterval;

    // Tab switching functionality
    const tabs = document.querySelectorAll('.tab-btn');
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // Remove active class from all tabs and contents
            document.querySelectorAll('.tab-btn').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

            // Add active class to clicked tab and its content
            tab.classList.add('active');
            document.getElementById(tab.dataset.target).classList.add('active');

            // Update current timer type
            currentTimerType = tab.dataset.timerId;
            updateTimerDisplay();
        });
    });

    // OTP input handling
    const setupOTPInputs = (containerSelector) => {
        const container = document.querySelector(containerSelector);
        if (!container) return;

        const inputs = container.querySelectorAll('.otp-input');

        inputs.forEach((input, index) => {
            input.addEventListener('input', (e) => {
                if (e.target.value.length === 1) {
                    if (index < inputs.length - 1) {
                        inputs[index + 1].focus();
                    }
                }
            });

            input.addEventListener('keydown', (e) => {
                if (e.key === 'Backspace' && !e.target.value && index > 0) {
                    inputs[index - 1].focus();
                }
            });
        });
    };

    setupOTPInputs('#phone-tab .otp-input-container');
    setupOTPInputs('#email-tab .otp-input-container');

    // Timer functionality
    function updateTimerDisplay() {
        const timeLeft = currentTimerType === 'phone' ? phoneTimeLeft : emailTimeLeft;

        if (timeLeft > 0) {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            timerDisplay.textContent = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
            timerDisplay.style.color = '#fff';
        } else {
            timerDisplay.textContent = '00:00';
            timerDisplay.style.color = '#ffcccc';
        }

        console.log('Timer updated:', {
            currentType: currentTimerType,
            timeLeft: timeLeft,
            display: timerDisplay.textContent
        });
    }

    function updateTimers() {
        if (phoneTimeLeft > 0) {
            phoneTimeLeft--;
            if (resendPhoneButton) {
                if (phoneTimeLeft === 0) {
                    resendPhoneButton.disabled = false;
                    resendPhoneButton.textContent = 'Resend Phone Code';
                } else {
                    const minutes = Math.floor(phoneTimeLeft / 60);
                    const seconds = phoneTimeLeft % 60;
                    resendPhoneButton.textContent = `Resend in ${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
                }
            }
        }

        if (emailTimeLeft > 0) {
            emailTimeLeft--;
            if (resendEmailButton) {
                if (emailTimeLeft === 0) {
                    resendEmailButton.disabled = false;
                    resendEmailButton.textContent = 'Resend Email Code';
                } else {
                    const minutes = Math.floor(emailTimeLeft / 60);
                    const seconds = emailTimeLeft % 60;
                    resendEmailButton.textContent = `Resend in ${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
                }
            }
        }

        updateTimerDisplay();
    }

    // Start the timer immediately
    updateTimerDisplay();

    // Clear any existing timer and start a new one
    if (timerInterval) {
        clearInterval(timerInterval);
    }
    timerInterval = setInterval(updateTimers, 1000);

    // Force immediate update of button states
    setTimeout(function() {
        updateTimerDisplay();

        // Update button states immediately
        if (resendPhoneButton) {
            resendPhoneButton.disabled = (phoneTimeLeft > 0);
            if (phoneTimeLeft > 0) {
                const minutes = Math.floor(phoneTimeLeft / 60);
                const seconds = phoneTimeLeft % 60;
                resendPhoneButton.textContent = `Resend in ${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
            } else {
                resendPhoneButton.textContent = 'Resend Phone Code';
            }
        }

        if (resendEmailButton) {
            resendEmailButton.disabled = (emailTimeLeft > 0);
            if (emailTimeLeft > 0) {
                const minutes = Math.floor(emailTimeLeft / 60);
                const seconds = emailTimeLeft % 60;
                resendEmailButton.textContent = `Resend in ${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
            } else {
                resendEmailButton.textContent = 'Resend Email Code';
            }
        }
    }, 100);

    // Disable resend buttons if timer is still active and update button text
    if (resendPhoneButton) {
        resendPhoneButton.disabled = (phoneTimeLeft > 0);
        if (phoneTimeLeft > 0) {
            const minutes = Math.floor(phoneTimeLeft / 60);
            const seconds = phoneTimeLeft % 60;
            resendPhoneButton.textContent = `Resend in ${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
        } else {
            resendPhoneButton.textContent = 'Resend Phone Code';
        }
    }

    if (resendEmailButton) {
        resendEmailButton.disabled = (emailTimeLeft > 0);
        if (emailTimeLeft > 0) {
            const minutes = Math.floor(emailTimeLeft / 60);
            const seconds = emailTimeLeft % 60;
            resendEmailButton.textContent = `Resend in ${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
        } else {
            resendEmailButton.textContent = 'Resend Email Code';
        }
    }

    // Reset timer when resend buttons are clicked
    if (document.getElementById('resendPhoneForm')) {
        document.getElementById('resendPhoneForm').addEventListener('submit', function(e) {
            // Show loading state
            if (resendPhoneButton) {
                resendPhoneButton.disabled = true;
                resendPhoneButton.textContent = 'Sending...';
            }

            // Store current timestamp for timer reset detection
            localStorage.setItem('resend_phone_timestamp', Date.now());
        });
    }

    if (document.getElementById('resendEmailForm')) {
        document.getElementById('resendEmailForm').addEventListener('submit', function(e) {
            // Show loading state
            if (resendEmailButton) {
                resendEmailButton.disabled = true;
                resendEmailButton.textContent = 'Sending...';
            }

            // Store current timestamp for timer reset detection
            localStorage.setItem('resend_email_timestamp', Date.now());
        });
    }

    // Check if we just resent a code and need to reset timer
    const phoneResendTime = localStorage.getItem('resend_phone_timestamp');
    const emailResendTime = localStorage.getItem('resend_email_timestamp');
    const currentTime = Date.now();

    // If resend was within last 5 seconds, force timer reset
    if (phoneResendTime && (currentTime - phoneResendTime) < 5000) {
        phoneTimeLeft = phoneTimeRemaining > 0 ? Math.floor(phoneTimeRemaining) : 300;
        localStorage.removeItem('resend_phone_timestamp');
        console.log('Phone timer reset after resend:', phoneTimeLeft);
    }

    if (emailResendTime && (currentTime - emailResendTime) < 5000) {
        emailTimeLeft = emailTimeRemaining > 0 ? Math.floor(emailTimeRemaining) : 300;
        localStorage.removeItem('resend_email_timestamp');
        console.log('Email timer reset after resend:', emailTimeLeft);
    }
});
</script>
@endsection
