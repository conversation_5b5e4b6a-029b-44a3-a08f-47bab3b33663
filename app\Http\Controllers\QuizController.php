<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Quiz;
use App\Models\Lecture;
use App\Models\QuizAttempt;
use App\Models\QuizAnswer;
use App\Models\QuizQuestion;
use App\Models\Course;
use Illuminate\Support\Facades\Auth;

class QuizController extends Controller
{
    /**
     * Show a quiz for a specific lecture for user to take
     *
     * @param  int  $courseId
     * @param  int  $lectureId
     * @param  int  $quizId
     * @return \Illuminate\Http\Response
     */
    public function show($courseId, $lectureId, $quizId)
    {
        $course = Course::findOrFail($courseId);
        $lecture = Lecture::findOrFail($lectureId);
        $quiz = Quiz::with('questions.options')->findOrFail($quizId);
        
        // Make sure the quiz belongs to the lecture
        if ($quiz->lecture_id != $lectureId) {
            return redirect()->route('user.lecture.purchased', ['course' => $courseId, 'lecture' => $lectureId])
                ->with('error', 'The requested quiz does not belong to this lecture.');
        }
        
        // Check if the user has an in-progress attempt
        $userId = Auth::id();
        $attempt = $quiz->getInProgressAttempt($userId);
        
        // If no in-progress attempt, create a new one
        if (!$attempt) {
            // Check if the user has already passed the quiz
            if ($quiz->isPassed($userId)) {
                $latestAttempt = $quiz->getLatestAttempt($userId);
                return view('quiz.result', compact('quiz', 'attempt', 'course', 'lecture', 'latestAttempt'));
            }
            
            $attempt = QuizAttempt::create([
                'user_id' => $userId,
                'quiz_id' => $quizId,
                'status' => 'in_progress',
                'started_at' => now(),
            ]);
        }
        
        // Check if time is up for timed quizzes
        if ($quiz->time_limit && $attempt->isTimeUp()) {
            return $this->completeAttempt($attempt);
        }
        
        // Get answered questions for this attempt
        $answeredQuestionIds = $attempt->answers()->pluck('quiz_question_id')->toArray();
        
        return view('quiz.take', compact('quiz', 'course', 'lecture', 'attempt', 'answeredQuestionIds'));
    }
    
    /**
     * Submit an answer for a quiz question
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $attemptId
     * @return \Illuminate\Http\Response
     */
    public function submitAnswer(Request $request, $attemptId)
    {
        $attempt = QuizAttempt::findOrFail($attemptId);
        
        // Make sure this attempt belongs to the authenticated user
        if ($attempt->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized action.'
            ], 403);
        }
        
        // Check if time is up for timed quizzes
        $quiz = $attempt->quiz;
        if ($quiz->time_limit && $attempt->isTimeUp()) {
            return $this->completeAttempt($attempt);
        }
        
        // Validate the request
        $validated = $request->validate([
            'question_id' => 'required|exists:quiz_questions,id',
            'option_id' => 'required_if:question_type,multiple_choice|nullable|exists:quiz_options,id',
            'answer_text' => 'required_if:question_type,open_ended|nullable|string',
            'question_type' => 'required|in:multiple_choice,open_ended',
        ]);
        
        $questionId = $validated['question_id'];
        $question = QuizQuestion::findOrFail($questionId);
        
        // Make sure the question belongs to the quiz
        if ($question->quiz_id !== $quiz->id) {
            return response()->json([
                'success' => false,
                'message' => 'The question does not belong to this quiz.'
            ], 400);
        }
        
        // Create or update the answer
        $answer = QuizAnswer::updateOrCreate(
            ['quiz_attempt_id' => $attemptId, 'quiz_question_id' => $questionId],
            [
                'quiz_option_id' => $validated['question_type'] === 'multiple_choice' ? $validated['option_id'] : null,
                'answer_text' => $validated['question_type'] === 'open_ended' ? $validated['answer_text'] : null,
            ]
        );
        
        // Evaluate the answer
        $answer->evaluateAnswer();
        
        // Check if all questions have been answered
        $totalQuestions = $quiz->questions()->count();
        $answeredQuestions = $attempt->answers()->count();
        
        $isComplete = ($answeredQuestions >= $totalQuestions);
        
        return response()->json([
            'success' => true,
            'is_correct' => $answer->is_correct,
            'points_earned' => $answer->points_earned,
            'is_complete' => $isComplete,
            'message' => 'Answer submitted successfully.'
        ]);
    }
    
    /**
     * Complete a quiz attempt
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $attemptId
     * @return \Illuminate\Http\Response
     */
    public function complete(Request $request, $attemptId)
    {
        $attempt = QuizAttempt::findOrFail($attemptId);
        
        // Make sure this attempt belongs to the authenticated user
        if ($attempt->user_id !== Auth::id()) {
            return redirect()->route('user.dashboard')->with('error', 'Unauthorized action.');
        }
        
        return $this->completeAttempt($attempt);
    }
    
    /**
     * Helper method to complete an attempt and redirect to results
     *
     * @param  \App\Models\QuizAttempt  $attempt
     * @return \Illuminate\Http\Response
     */
    private function completeAttempt(QuizAttempt $attempt)
    {
        // Calculate score
        $attempt->calculateScore();
        
        $course = $attempt->quiz->lecture->course;
        $lecture = $attempt->quiz->lecture;
        
        return redirect()->route('quiz.result', [
            'course' => $course->id,
            'lecture' => $lecture->id,
            'quiz' => $attempt->quiz->id,
            'attempt' => $attempt->id
        ]);
    }
    
    /**
     * Show quiz attempt results
     *
     * @param  int  $courseId
     * @param  int  $lectureId
     * @param  int  $quizId
     * @param  int  $attemptId
     * @return \Illuminate\Http\Response
     */
    public function result($courseId, $lectureId, $quizId, $attemptId)
    {
        $course = Course::findOrFail($courseId);
        $lecture = Lecture::findOrFail($lectureId);
        $quiz = Quiz::findOrFail($quizId);
        $attempt = QuizAttempt::with(['answers.question', 'answers.selectedOption'])->findOrFail($attemptId);
        
        // Make sure this attempt belongs to the authenticated user
        if ($attempt->user_id !== Auth::id()) {
            return redirect()->route('user.dashboard')->with('error', 'Unauthorized action.');
        }
        
        // Make sure the attempt is completed
        if ($attempt->isInProgress()) {
            return redirect()->route('quiz.take', [
                'course' => $courseId,
                'lecture' => $lectureId,
                'quiz' => $quizId
            ])->with('info', 'Please complete the quiz before viewing the results.');
        }
        
        return view('quiz.result', compact('quiz', 'attempt', 'course', 'lecture'));
    }
} 