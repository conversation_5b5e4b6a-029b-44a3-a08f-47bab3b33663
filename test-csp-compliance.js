/**
 * CSP Compliance Test Script
 * Tests all edited pages for console errors after CSP compliance changes
 */

const puppeteer = require('puppeteer');
const fs = require('fs');

// Pages to test (add your actual URLs here)
const pagesToTest = [
    // Authentication pages
    { name: 'Login', url: 'http://localhost:8000/login' },
    { name: 'Register', url: 'http://localhost:8000/register' },
    { name: 'Password Reset', url: 'http://localhost:8000/password/reset' },
    
    // Admin pages
    { name: 'Dashboard', url: 'http://localhost:8000/dashboard' },
    { name: 'Users Create', url: 'http://localhost:8000/users/create' },
    { name: 'Users Edit', url: 'http://localhost:8000/users/1/edit' },
    
    // Component pages
    { name: 'Checkout', url: 'http://localhost:8000/checkout' },
    
    // Add more URLs as needed
];

async function testPageForErrors(page, testPage) {
    const errors = [];
    
    // Listen for console errors
    page.on('console', msg => {
        if (msg.type() === 'error') {
            errors.push(`Console Error: ${msg.text()}`);
        }
    });
    
    // Listen for page errors
    page.on('pageerror', error => {
        errors.push(`Page Error: ${error.message}`);
    });
    
    // Listen for failed requests
    page.on('requestfailed', request => {
        errors.push(`Request Failed: ${request.url()} - ${request.failure().errorText}`);
    });
    
    try {
        console.log(`Testing: ${testPage.name} (${testPage.url})`);
        
        // Navigate to page
        await page.goto(testPage.url, { 
            waitUntil: 'networkidle2',
            timeout: 30000 
        });
        
        // Wait for page to fully load
        await page.waitForTimeout(2000);
        
        // Check for CSP violations specifically
        const cspViolations = await page.evaluate(() => {
            const violations = [];
            // Check if there are any inline styles
            const elementsWithStyle = document.querySelectorAll('[style]');
            elementsWithStyle.forEach(el => {
                violations.push(`Inline style found: ${el.tagName} - ${el.getAttribute('style')}`);
            });
            return violations;
        });
        
        errors.push(...cspViolations);
        
        return {
            page: testPage.name,
            url: testPage.url,
            errors: errors,
            success: errors.length === 0
        };
        
    } catch (error) {
        return {
            page: testPage.name,
            url: testPage.url,
            errors: [`Navigation Error: ${error.message}`],
            success: false
        };
    }
}

async function runTests() {
    console.log('🚀 Starting CSP Compliance Tests...\n');
    
    const browser = await puppeteer.launch({ 
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Set viewport
    await page.setViewport({ width: 1200, height: 800 });
    
    const results = [];
    
    for (const testPage of pagesToTest) {
        const result = await testPageForErrors(page, testPage);
        results.push(result);
        
        if (result.success) {
            console.log(`✅ ${result.page}: PASSED`);
        } else {
            console.log(`❌ ${result.page}: FAILED`);
            result.errors.forEach(error => {
                console.log(`   - ${error}`);
            });
        }
        console.log('');
    }
    
    await browser.close();
    
    // Generate report
    const report = {
        timestamp: new Date().toISOString(),
        summary: {
            total: results.length,
            passed: results.filter(r => r.success).length,
            failed: results.filter(r => !r.success).length
        },
        results: results
    };
    
    // Save report to file
    fs.writeFileSync('csp-test-report.json', JSON.stringify(report, null, 2));
    
    console.log('📊 Test Summary:');
    console.log(`Total Pages: ${report.summary.total}`);
    console.log(`Passed: ${report.summary.passed}`);
    console.log(`Failed: ${report.summary.failed}`);
    console.log('\n📄 Detailed report saved to: csp-test-report.json');
    
    return report;
}

// Run the tests
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = { runTests, testPageForErrors };
