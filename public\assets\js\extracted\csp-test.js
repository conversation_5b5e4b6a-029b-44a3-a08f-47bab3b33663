/**
 * CSP Test Script
 * This script tests if CSP violations are being properly fixed
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('CSP Test: Starting tests...');
    
    // Test 1: Try to create an element with inline style
    const testDiv = document.createElement('div');
    testDiv.className = 'csp-test-div';
    // Create a style with nonce instead of inline style
    const testStyle = document.createElement('style');
    const nonceValue = document.querySelector('meta[name="csp-nonce"]')?.getAttribute('content');
    if (nonceValue) {
        testStyle.setAttribute('nonce', nonceValue);
        testStyle.textContent = '.csp-test-div { color: red; background: yellow; }';
        document.head.appendChild(testStyle);
    }
    document.body.appendChild(testDiv);
    
    setTimeout(() => {
        if (testDiv.className.includes('csp-test-div')) {
            console.log('CSP Test: PASSED - Style applied via CSS class with nonce');
        } else {
            console.error('CSP Test: FAILED - Style not applied correctly');
        }
    }, 100);
    
    // Test 2: Try to create an element with inline event handler
    const testButton = document.createElement('button');
    testButton.textContent = 'Test Button';
    testButton.className = 'csp-test-button';
    
    // Add to body first
    if (document.body) {
        document.body.appendChild(testButton);
    } else {
        document.addEventListener('DOMContentLoaded', () => {
            document.body.appendChild(testButton);
        });
    }
    
    // Test if our CSP fix can handle onclick attributes
    setTimeout(() => {
        // Try to add onclick - this should be handled by our CSP fix
        testButton.setAttribute('onclick', 'console.log("test click")');
        
        // Manually trigger our CSP fix if available
        if (typeof window.immediateCSPFix !== 'undefined' && window.immediateCSPFix.fix) {
            window.immediateCSPFix.fix();
        }
        
        // Check after a longer delay to allow our fix to work
        setTimeout(() => {
            if (testButton.hasAttribute('onclick')) {
                console.error('CSP Test: FAILED - Inline onclick not removed');
            } else {
                console.log('CSP Test: PASSED - Inline onclick removed and converted to event listener');
            }
        }, 200);
    }, 100);
    
    // Test 3: Check if nonce is present on all scripts and styles
    setTimeout(() => {
        const scriptsWithoutNonce = document.querySelectorAll('script:not([src]):not([nonce])');
        const stylesWithoutNonce = document.querySelectorAll('style:not([nonce])');
        
        if (scriptsWithoutNonce.length > 0) {
            console.error('CSP Test: FAILED - Found', scriptsWithoutNonce.length, 'scripts without nonce');
        } else {
            console.log('CSP Test: PASSED - All inline scripts have nonce');
        }
        
        if (stylesWithoutNonce.length > 0) {
            console.error('CSP Test: FAILED - Found', stylesWithoutNonce.length, 'styles without nonce');
        } else {
            console.log('CSP Test: PASSED - All styles have nonce');
        }
    }, 200);
    
    // Test 4: Check if CSP fixes are available
    if (typeof window.immediateCSPFix !== 'undefined') {
        console.log('CSP Test: PASSED - Immediate CSP fix is available');
        console.log('CSP Test: Nonce value:', window.immediateCSPFix.nonce);
    } else {
        console.error('CSP Test: FAILED - No CSP fix available');
    }
    
    console.log('CSP Test: All tests completed');
});