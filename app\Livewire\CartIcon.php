<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Shoppingcart;

class CartIcon extends Component
{
    public $cartCount = 0;

    public function mount()
    {
        $this->updateCartCount();
    }

    public function updateCartCount()
    {
        if (auth()->check()) {
            $this->cartCount = Shoppingcart::where('user_id', auth()->id())->count();
        } else {
            $this->cartCount = 0;
        }
    }

    protected $listeners = ['cartUpdated' => 'updateCartCount'];

    public function refreshCart()
    {
        $this->updateCartCount();
    }

    public function render()
    {
        // Always get fresh cart count on render
        $this->updateCartCount();
        return view('livewire.cart-icon');
    }
}
