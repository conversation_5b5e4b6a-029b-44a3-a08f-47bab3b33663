<div>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <style>
            @layer utilities {
                input[type="number"]::-webkit-inner-spin-button,
                input[type="number"]::-webkit-outer-spin-button {
                    -webkit-appearance: none;
                    margin: 0;
                }
            }

            .cart-table {
                width: 100%;
                border-collapse: separate;
                border-spacing: 0 0.5rem;
            }

            .cart-table th {
                background-color: #f8f9fa;
                padding: 0.75rem 1rem;
                text-align: left;
                font-weight: 600;
                color: #495057;
                font-size: 0.75rem;
            }

            .cart-table td {
                padding: 0.75rem 1rem;
                background-color: white;
                border-bottom: 1px solid #e9ecef;
                vertical-align: middle;
            }

            .cart-table tr:first-child td {
                border-top-left-radius: 0.375rem;
                border-top-right-radius: 0.375rem;
            }

            .cart-table tr:last-child td {
                border-bottom-left-radius: 0.375rem;
                border-bottom-right-radius: 0.375rem;
            }

            .course-image {
                width: 60px;
                height: 60px;
                object-fit: cover;
                border-radius: 0.375rem;
            }

            .remove-btn {
                color: #dc3545;
                transition: all 0.3s ease;
                font-size: 0.875rem;
            }

            .remove-btn:hover {
                color: #c82333;
                text-decoration: underline;
            }

            .checkout-btn {
                background-color: #0d6efd;
                transition: all 0.3s ease;
                padding: 0.5rem 1rem;
            }

            .checkout-btn:hover {
                background-color: #0b5ed7;
            }

            .cart-container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 1rem;
            }

            .course-title {
                font-size: 0.875rem;
                font-weight: 600;
                margin-bottom: 0.25rem;
            }

            .course-description {
                font-size: 0.75rem;
                color: #6c757d;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }

            .price-tag {
                font-size: 0.875rem;
                font-weight: 600;
                color: #212529;
            }
        </style>

        <div class="cart-container">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <h4 class="mb-0">Shopping Cart</h4>
                </div>
                <div class="card-body px-0 pt-0 pb-2">
                    @if($cartitems->count() > 0)
                        <div class="table-responsive">
                            <table class="cart-table align-items-center mb-0">
                                <thead>
                                    <tr>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Course</th>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Description</th>
                                        <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Price</th>
                                        <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($cartitems as $item)
                                        <tr>
                                            <td class="cart-col-30">
                                                <div class="d-flex align-items-center">
                                                    <img src="{{ $item->course ? ($item->course->image_path ? Storage::url($item->course->image_path) : asset('images/default-course.jpg')) : ($item->lecture ? ($item->lecture->image_path ? Storage::url($item->lecture->image_path) : asset('images/default-course.jpg')) : asset('images/default-course.jpg')) }}"
                                                         class="course-image me-3"
                                                         alt="course-image">
                                                    <div>
                                                        <h6 class="course-title mb-0">{{ $item->course?->name ?? $item->lecture?->name ?? 'Unknown Item' }}</h6>
                                                        @if($item->discount_amount > 0)
                                                            <span class="badge bg-success mt-1">Discounted</span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="cart-col-40">
                                                <p class="course-description mb-0">{{ $item->course?->description ?? $item->lecture?->description ?? 'No description available' }}</p>
                                                @if($item->discount_amount > 0)
                                                    <div class="alert alert-success py-1 px-2 mt-2 mb-0 cart-discount-alert">
                                                        <strong>Discount applied!</strong> {{ $item->discount_reason }}
                                                    </div>
                                                @endif
                                            </td>
                                            <td class="cart-col-15 text-center">
                                                @if($item->discount_amount > 0)
                                                    <div>
                                                        <span class="text-decoration-line-through text-muted">${{ number_format($item->original_price, 2) }}</span>
                                                    </div>
                                                    <div class="d-flex justify-content-center align-items-center mb-1">
                                                        <span class="text-success me-1">-${{ number_format($item->discount_amount, 2) }}</span>
                                                    </div>
                                                @endif
                                                <span class="price-tag">${{ number_format($item->price, 2) }}</span>
                                            </td>
                                            <td class="cart-col-15 text-center">
                                                <button wire:click="removeFromCart({{ $item->id }})" class="remove-btn btn btn-link text-danger">
                                                    Remove
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <p class="text-muted">Your cart is empty.</p>
                        </div>
                    @endif
                </div>
            </div>

            @if($cartitems->count() > 0)
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Total Amount</h5>
                            <h5 class="mb-0">${{ $cartitems->sum(fn($item) => $item->price) }}</h5>
                        </div>
                        <div class="mt-3">
                            <button wire:click="checkout" class="checkout-btn btn btn-primary w-100">
                                Proceed to Checkout
                            </button>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </main>
</div>
