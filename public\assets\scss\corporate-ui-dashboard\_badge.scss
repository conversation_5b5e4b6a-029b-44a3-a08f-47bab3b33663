@each $prop, $value in $theme-colors {
  .badge.bg-#{$prop} {
    background: rgba($value, .1) !important;
  }
}
.badge {
  text-transform: inherit;
}

// Colors
//
// Contextual variations (linked badges get darker on :hover).

@each $color, $value in $theme-colors {
  .badge-#{$color} {
    @include badge-variant($value);
  }
}

// Size variations

.badge-sm {
  padding: $badge-sm-padding;
  font-size: $badge-sm-font-size;
  border-radius: $border-radius-sm;
}

.badge-md {
  padding: $badge-md-padding;
}

.badge-lg {
  padding: $badge-lg-padding;
}
