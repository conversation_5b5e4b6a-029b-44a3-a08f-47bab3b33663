@section('styles')
    <link rel="stylesheet" href="{{ asset('css/user-dashboard.css') }}">
@endsection

<x-app-layout>
        <div class="container py-4">
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-gradient-primary p-4">
            <div class="row">
                            <div class="col-md-8">
                                <h3 class="text-white mb-0">My Learning Dashboard</h3>
                                <p class="text-white opacity-8 mb-0">Welcome back, {{ Auth::user()->name }}</p>
                            </div>
                            <div class="col-md-4 text-end d-flex align-items-center justify-content-end">
                                <a href="{{ route('courses') }}" class="btn btn-white btn-sm ms-auto">
                                    <i class="fas fa-search me-1"></i> Browse New Courses
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                <div class="d-flex">
                    <div class="me-3">
                        <i class="fas fa-check-circle fa-lg"></i>
                    </div>
                    <div>
                        <strong>Success!</strong> {{ session('success') }}
                    </div>
                </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <div class="d-flex">
                    <div class="me-3">
                        <i class="fas fa-exclamation-triangle fa-lg"></i>
                    </div>
                    <div>
                        <strong>Error!</strong> {{ session('error') }}
                    </div>
                </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

        <!-- Progress Stats Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card shadow-sm border-0">
                    <div class="card-body p-4">
                        <div class="row">
                            <div class="col-8">
                                <div class="numbers">
                                    <p class="text-sm mb-0 text-uppercase font-weight-bold">Active Courses</p>
                                    <h5 class="font-weight-bolder mb-0">
                                        {{ count($purchasedItems) }}
                                    </h5>
                                </div>
                            </div>
                            <div class="col-4 text-end">
                                <div class="icon icon-shape bg-gradient-primary shadow-primary text-center rounded-circle p-3">
                                    <i class="fas fa-book text-white text-lg opacity-10"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card shadow-sm border-0">
                    <div class="card-body p-4">
                        <div class="row">
                            <div class="col-8">
                                <div class="numbers">
                                    <p class="text-sm mb-0 text-uppercase font-weight-bold">Pending Orders</p>
                                    <h5 class="font-weight-bolder mb-0">
                                        {{ count($pendingItems) }}
                                    </h5>
                                </div>
                            </div>
                            <div class="col-4 text-end">
                                <div class="icon icon-shape bg-gradient-warning shadow-warning text-center rounded-circle p-3">
                                    <i class="fas fa-hourglass-half text-white text-lg opacity-10"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card shadow-sm border-0">
                    <div class="card-body p-4">
                        <div class="row">
                            <div class="col-8">
                                <div class="numbers">
                                    <p class="text-sm mb-0 text-uppercase font-weight-bold">Quiz Results</p>
                                    <h5 class="font-weight-bolder mb-0">
                                        {{ $quizStats['total'] ?? 0 }}
                                    </h5>
                                </div>
                            </div>
                            <div class="col-4 text-end">
                                <div class="icon icon-shape bg-gradient-info shadow-info text-center rounded-circle p-3">
                                    <i class="fas fa-chart-bar text-white text-lg opacity-10"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card shadow-sm border-0">
                    <div class="card-body p-4">
                        <div class="row">
                            <div class="col-8">
                                <div class="numbers">
                                    <p class="text-sm mb-0 text-uppercase font-weight-bold">Last Login</p>
                                    <h5 class="font-weight-bolder mb-0">
                                        {{ Auth::user()->updated_at->diffForHumans() }}
                                    </h5>
                                </div>
                            </div>
                            <div class="col-4 text-end">
                                <div class="icon icon-shape bg-gradient-success shadow-success text-center rounded-circle p-3">
                                    <i class="fas fa-user-clock text-white text-lg opacity-10"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

            <div class="row mb-4">
                <div class="col-12">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-white p-3 d-flex align-items-center">
                        <h5 class="mb-0 flex-grow-1"><i class="fas fa-graduation-cap me-2 text-primary"></i>My Courses</h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-primary active" id="view-grid">
                                <i class="fas fa-th-large"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-primary" id="view-list">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-3">
                            @if(count($purchasedItems) > 0)
                            <!-- Grid View -->
                            <div id="grid-view">
                                <div class="row g-3">
                                    @foreach($purchasedItems as $item)
                                    <div class="col-lg-4 col-md-6 col-12">
                                        <div class="card h-100 shadow-sm border-0 course-card">
                                            <div class="position-relative">
                                                <img src="{{ $item['type'] == 'course' ?
                                                    (isset($item['item']->image_path) ? Storage::url($item['item']->image_path) : 'https://via.placeholder.com/300x200?text=Course+Image') :
                                                    (isset($item['item']->course->image_path) ? Storage::url($item['item']->course->image_path) : 'https://via.placeholder.com/300x200?text=Lecture+Image') }}"
                                                    class="card-img-top course-card-img" alt="{{ $item['item']->name ?? $item['item']->lecture_title }}">

                                                <div class="position-absolute top-0 end-0 m-2">
                                                    <span class="badge bg-{{ $item['type'] == 'course' ? 'primary' : 'info' }} px-2 py-1">
                                                        <i class="fas fa-{{ $item['type'] == 'course' ? 'book' : 'file-alt' }} me-1"></i>
                                                        {{ ucfirst($item['type']) }}
                                                    </span>
                                                </div>
                                            </div>

                                            <div class="card-body p-3">
                                                <h6 class="card-title fw-bold mb-2">{{ $item['type'] == 'course' ? $item['item']->name : $item['item']->lecture_title }}</h6>

                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <small class="text-muted">
                                                        <i class="fas fa-calendar-alt me-1"></i> {{ $item['order_date']->format('M d, Y') }}
                                                    </small>

                                                    @if(isset($item['expires_at']) && $item['expires_at'])
                                                        <small class="{{ now()->gt($item['expires_at']) ? 'text-danger' : 'text-warning' }}">
                                                            <i class="fas fa-{{ now()->gt($item['expires_at']) ? 'exclamation-circle' : 'clock' }} me-1"></i>
                                                            {{ now()->gt($item['expires_at']) ? 'Expired' : 'Expires' }}
                                                        </small>
                                                    @endif
                                                </div>

                                                @php
                                                    // Calculate progress percentage
                                                    if ($item['type'] == 'course') {
                                                        $progressPercent = $item['item']->getProgressPercentageForUser(Auth::id());
                                                        $completedLectures = $item['item']->getCompletedLectureCountForUser(Auth::id());
                                                        $totalLectures = $item['item']->total_lecture_count;
                                                    } else {
                                                        $progress = $item['item']->getProgressForUser(Auth::id());
                                                        $progressPercent = $progress ? $progress->progress_percent : 0;
                                                        $completedLectures = 0;
                                                        $totalLectures = 1;
                                                    }
                                                @endphp

                                                <div class="mb-2">
                                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                                        <small class="text-muted">Progress</small>
                                                        <small class="fw-bold">{{ number_format($progressPercent, 0) }}%</small>
                                                    </div>
                                                    <div class="progress progress-thin">
                                                        <div class="progress-bar bg-success dynamic-progress-bar" role="progressbar"
                                                             data-width="{{ $progressPercent }}"
                                                             aria-valuenow="{{ $progressPercent }}"
                                                             aria-valuemin="0"
                                                             aria-valuemax="100"></div>
                                                    </div>
                                                </div>

                                                <div class="d-grid gap-2">
                                                    @if($item['type'] == 'course')
                                                        <a href="{{ route('user.course.purchased', $item['item']->id) }}" class="btn btn-primary btn-sm">
                                                            <i class="fas fa-play-circle me-1"></i> Continue Learning
                                                        </a>
                                                    @else
                                                        <a href="{{ route('user.lecture.purchased', ['course' => $item['item']->course->id, 'lecture' => $item['item']->id]) }}" class="btn btn-primary btn-sm">
                                                            <i class="fas fa-play-circle me-1"></i> View Lecture
                                                        </a>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>

                            <!-- List View -->
                            <div class="table-responsive d-none" id="list-view">
                                <table class="table align-items-center mb-0">
                                    <thead>
                                        <tr>
                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Content</th>
                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Type</th>
                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Purchase Date</th>
                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Status</th>
                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Progress</th>
                                            <th class="text-secondary opacity-7"></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($purchasedItems as $item)
                                            <tr>
                                                <td>
                                                    <div class="d-flex px-2 py-1">
                                                        <div>
                                                            <img src="{{ $item['type'] == 'course' ?
                                                                (isset($item['item']->image_path) ? Storage::url($item['item']->image_path) : 'https://via.placeholder.com/300x200?text=Course+Image') :
                                                                (isset($item['item']->course->image_path) ? Storage::url($item['item']->course->image_path) : 'https://via.placeholder.com/300x200?text=Lecture+Image') }}"
                                                                class="avatar avatar-sm me-3" alt="{{ $item['item']->name ?? $item['item']->lecture_title }}">
                                                        </div>
                                                        <div class="d-flex flex-column justify-content-center">
                                                            <h6 class="mb-0 text-sm">{{ $item['type'] == 'course' ? $item['item']->name : $item['item']->lecture_title }}</h6>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-{{ $item['type'] == 'course' ? 'primary' : 'info' }} rounded-pill">{{ ucfirst($item['type']) }}</span>
                                                </td>
                                                <td>
                                                    <p class="text-xs font-weight-bold mb-0">{{ $item['order_date']->format('M d, Y') }}</p>
                                                </td>
                                                <td>
                                                    @if(isset($item['expires_at']) && $item['expires_at'])
                                                        <span class="badge bg-{{ now()->gt($item['expires_at']) ? 'danger' : 'warning' }}">
                                                            {{ now()->gt($item['expires_at']) ? 'Expired' : 'Expires: ' . $item['expires_at']->format('M d, Y') }}
                                                        </span>
                                                    @else
                                                        <span class="badge bg-success">Active</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @php
                                                        // Calculate progress percentage
                                                        if ($item['type'] == 'course') {
                                                            $progressPercent = $item['item']->getProgressPercentageForUser(Auth::id());
                                                            $completedLectures = $item['item']->getCompletedLectureCountForUser(Auth::id());
                                                            $totalLectures = $item['item']->total_lecture_count;
                                                        } else {
                                                            $progress = $item['item']->getProgressForUser(Auth::id());
                                                            $progressPercent = $progress ? $progress->progress_percent : 0;
                                                            $completedLectures = 0;
                                                            $totalLectures = 1;
                                                        }

                                                        // Determine text color class
                                                        if ($progressPercent > 75) {
                                                            $textColorClass = 'text-success';
                                                        } elseif ($progressPercent > 25) {
                                                            $textColorClass = 'text-primary';
                                                        } else {
                                                            $textColorClass = 'text-muted';
                                                        }
                                                    @endphp
                                                    <div class="d-flex align-items-center flex-column">
                                                        <div class="progress progress-mini">
                                                            <div class="progress-bar bg-success dynamic-progress-bar" role="progressbar"
                                                                data-width="{{ $progressPercent }}"
                                                                aria-valuenow="{{ $progressPercent }}"
                                                                aria-valuemin="0"
                                                                aria-valuemax="100"></div>
                                                        </div>
                                                        <span class="text-xs mt-1 {{ $textColorClass }}">
                                                            {{ number_format($progressPercent, 0) }}%
                                                            @if($item['type'] == 'course')
                                                                <span>({{ $completedLectures }}/{{ $totalLectures }})</span>
                                                            @endif
                                                        </span>
                                                    </div>
                                                </td>
                                                <td class="align-middle">
                                                    @if($item['type'] == 'course')
                                                        <a href="{{ route('user.course.purchased', $item['item']->id) }}" class="btn btn-link text-primary mb-0">
                                                            <i class="fas fa-play-circle me-1"></i> Continue
                                                        </a>

                                                        @php
                                                            $course = $item['item'];
                                                            $user = Auth::user();
                                                            $hasCertificate = $user->certificates()->where('course_id', $course->id)->exists();
                                                            $hasPendingRequest = $user->hasPendingCertificateRequest($course->id);
                                                            $canRequestCertificate = $user->canRequestCertificate($course->id);
                                                        @endphp

                                                        @if($hasCertificate)
                                                            <a href="{{ route('user.certificates') }}" class="btn btn-link text-success mb-0">
                                                                <i class="fas fa-certificate me-1"></i> Certificate
                                                            </a>
                                                        @elseif($hasPendingRequest)
                                                            <span class="badge bg-warning">Certificate Request Pending</span>
                                                        @elseif($canRequestCertificate)
                                                            <form action="{{ route('user.request-certificate', $course->id) }}" method="POST" class="d-inline">
                                                                @csrf
                                                                <button type="submit" class="btn btn-link text-secondary mb-0">
                                                                    <i class="fas fa-certificate me-1"></i> Request Certificate
                                                                </button>
                                                            </form>
                                                        @endif
                                                    @else
                                                        <a href="{{ route('user.lecture.purchased', ['course' => $item['item']->course->id, 'lecture' => $item['item']->id]) }}" class="btn btn-link text-primary mb-0">
                                                            <i class="fas fa-play-circle me-1"></i> View
                                                        </a>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            @else
                            <div class="text-center py-5">
                                <div class="mb-4">
                                    <i class="fas fa-book-open fa-4x text-muted opacity-5"></i>
                                </div>
                                <h4 class="font-weight-normal">No courses yet</h4>
                                <p class="text-muted mb-4">You don't have any purchased courses in your account yet.</p>
                                <a href="{{ route('courses') }}" class="btn btn-primary">Browse Courses</a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Quiz Results Section -->
    @if(isset($quizAttempts) && count($quizAttempts) > 0)
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-lg-8 col-md-12 mx-auto">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-white p-3 d-flex align-items-center">
                            <h5 class="mb-0 flex-grow-1"><i class="fas fa-chart-line me-2 text-info"></i>Quiz Results</h5>
                            <a href="{{ route('user.quiz-results') }}" class="btn btn-sm btn-outline-info">
                                <i class="fas fa-eye me-1"></i> View All Results
                            </a>
                        </div>
                        <div class="card-body p-3">
                            <div class="row mb-3">
                                <div class="col-md-3 col-6 text-center">
                                    <div class="border rounded p-3">
                                        <h4 class="text-success mb-1">{{ $quizStats['passed'] ?? 0 }}</h4>
                                        <small class="text-muted">Passed</small>
                                    </div>
                                </div>
                                <div class="col-md-3 col-6 text-center">
                                    <div class="border rounded p-3">
                                        <h4 class="text-danger mb-1">{{ $quizStats['failed'] ?? 0 }}</h4>
                                        <small class="text-muted">Failed</small>
                                    </div>
                                </div>
                                <div class="col-md-3 col-6 text-center">
                                    <div class="border rounded p-3">
                                        <h4 class="text-warning mb-1">{{ $quizStats['pending'] ?? 0 }}</h4>
                                        <small class="text-muted">Pending Review</small>
                                    </div>
                                </div>
                                <div class="col-md-3 col-6 text-center">
                                    <div class="border rounded p-3">
                                        <h4 class="text-info mb-1">{{ $quizStats['total'] ?? 0 }}</h4>
                                        <small class="text-muted">Total Attempts</small>
                                    </div>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table align-items-center mb-0">
                                    <thead>
                                        <tr>
                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Quiz</th>
                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Course</th>
                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Score</th>
                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Status</th>
                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Date</th>
                                            <th class="text-secondary opacity-7"></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($quizAttempts->take(5) as $attempt)
                                            <tr>
                                                <td>
                                                    <div class="d-flex px-2 py-1">
                                                        <div class="d-flex flex-column justify-content-center">
                                                            <h6 class="mb-0 text-sm">{{ $attempt->quiz->title }}</h6>
                                                            <p class="text-xs text-secondary mb-0">{{ $attempt->quiz->lecture->lecture_title }}</p>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <p class="text-xs font-weight-bold mb-0">{{ $attempt->quiz->lecture->course->name }}</p>
                                                </td>
                                                <td>
                                                    <p class="text-xs font-weight-bold mb-0">
                                                        {{ $attempt->score ?? 0 }}/{{ $attempt->total_points ?? 0 }}
                                                        <span class="text-secondary">({{ $attempt->percentage_score ?? 0 }}%)</span>
                                                    </p>
                                                </td>
                                                <td>
                                                    @if($attempt->isPassed())
                                                        <span class="badge bg-success">Passed</span>
                                                    @elseif($attempt->isFailed())
                                                        <span class="badge bg-danger">Failed</span>
                                                    @elseif($attempt->isPendingReview())
                                                        <span class="badge bg-warning">Pending Review</span>
                                                    @else
                                                        <span class="badge bg-secondary">In Progress</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <p class="text-xs font-weight-bold mb-0">{{ $attempt->completed_at ? $attempt->completed_at->format('M d, Y') : 'In Progress' }}</p>
                                                </td>
                                                <td class="align-middle">
                                                    @if($attempt->isCompleted())
                                                        <a href="{{ route('quiz.result', [
                                                            'course' => $attempt->quiz->lecture->course->id,
                                                            'lecture' => $attempt->quiz->lecture->id,
                                                            'quiz' => $attempt->quiz->id,
                                                            'attempt' => $attempt->id
                                                        ]) }}" class="btn btn-link text-primary mb-0">
                                                            <i class="fas fa-eye me-1"></i> View Result
                                                        </a>
                                                    @else
                                                        <a href="{{ route('quiz.show', [
                                                            'course' => $attempt->quiz->lecture->course->id,
                                                            'lecture' => $attempt->quiz->lecture->id,
                                                            'quiz' => $attempt->quiz->id
                                                        ]) }}" class="btn btn-link text-warning mb-0">
                                                            <i class="fas fa-play me-1"></i> Continue
                                                        </a>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    @if(count($pendingItems) > 0)
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-lg-8 col-md-12 mx-auto">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-white p-3">
                            <h5 class="mb-0"><i class="fas fa-clock me-2 text-warning"></i>Pending Approvals</h5>
                        </div>
                        <div class="card-body p-3">
                            <div class="alert alert-info border-0 bg-info bg-gradient text-white" role="alert">
                                <div class="d-flex">
                                    <div class="me-3">
                                        <i class="fas fa-info-circle fa-lg"></i>
                                    </div>
                                    <div>
                                        <strong>Payment Processing:</strong> These items are waiting for admin payment approval. You'll get access once your payment is approved.
                            </div>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table align-items-center mb-0">
                                    <thead>
                                        <tr>
                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Content</th>
                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Type</th>
                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Order Date</th>
                                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Order ID</th>
                                            <th class="text-secondary opacity-7"></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($pendingItems as $item)
                                            <tr>
                                                <td>
                                                    <div class="d-flex px-2 py-1">
                                                        <div>
                                                            <img src="{{ $item['type'] == 'course' ?
                                                                (isset($item['item']->image_path) ? Storage::url($item['item']->image_path) : 'https://via.placeholder.com/300x200?text=Course+Image') :
                                                                (isset($item['item']->course->image_path) ? Storage::url($item['item']->course->image_path) : 'https://via.placeholder.com/300x200?text=Lecture+Image') }}"
                                                                class="avatar avatar-sm me-3" alt="{{ $item['item']->name ?? $item['item']->lecture_title }}">
                                                        </div>
                                                        <div class="d-flex flex-column justify-content-center">
                                                            <h6 class="mb-0 text-sm">{{ $item['type'] == 'course' ? $item['item']->name : $item['item']->lecture_title }}</h6>
                                                </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-{{ $item['type'] == 'course' ? 'primary' : 'info' }} rounded-pill">{{ ucfirst($item['type']) }}</span>
                                                </td>
                                                <td>
                                                    <p class="text-xs font-weight-bold mb-0">{{ $item['order_date']->format('M d, Y') }}</p>
                                                </td>
                                                <td>
                                                    <p class="text-xs font-weight-bold mb-0">#{{ $item['order_id'] }}</p>
                                                </td>
                                                <td class="align-middle">
                                                    <a href="{{ route('payment.pending', ['order' => $item['order_id']]) }}" class="btn btn-link text-warning mb-0">
                                                        <i class="fas fa-eye me-1"></i> View Status
                                                    </a>
                                                </td>
                                            </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

        <x-app.footer />

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const gridView = document.getElementById('grid-view');
            const listView = document.getElementById('list-view');
            const gridBtn = document.getElementById('view-grid');
            const listBtn = document.getElementById('view-list');

            if (gridBtn && listBtn && gridView && listView) {
                // Grid button click
                gridBtn.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Show only grid view
                    gridView.classList.remove('d-none');
                    listView.classList.add('d-none');

                    // Update button states
                    gridBtn.classList.add('active');
                    listBtn.classList.remove('active');
                });

                // List button click
                listBtn.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Show only list view
                    gridView.classList.add('d-none');
                    listView.classList.remove('d-none');

                    // Update button states
                    listBtn.classList.add('active');
                    gridBtn.classList.remove('active');
                });
            }
        });
    </script>

    <style>
        .hover-shadow {
            transition: all 0.3s ease;
        }
        .hover-shadow:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
        }
        .icon-shape {
            width: 48px;
            height: 48px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        .progress {
            overflow: visible;
        }
        .progress-bar {
            position: relative;
        }
        .card {
            transition: all 0.2s ease;
        }
        .bg-gradient-primary {
            background-image: linear-gradient(310deg, #7928CA 0%, #FF0080 100%);
        }
        .bg-gradient-warning {
            background-image: linear-gradient(310deg, #F53939 0%, #FBCF33 100%);
        }
        .bg-gradient-success {
            background-image: linear-gradient(310deg, #2DCE89 0%, #2DCEFF 100%);
        }
        .bg-gradient-info {
            background-image: linear-gradient(310deg, #17a2b8 0%, #6f42c1 100%);
        }

        /* Ensure proper container behavior */
        .card-body {
            /* Removed overflow: hidden; to allow table responsiveness */
        }

        /* View toggle styles */
        #grid-view {
            display: block !important;
        }

        #grid-view.d-none {
            display: none !important;
        }

        #list-view {
            display: none !important;
        }

        #list-view:not(.d-none) {
            display: block !important;
        }

        /* Responsive grid layout */
        /* 4 cards per row on extra large screens (1200px+) */
        @media (min-width: 1200px) {
            .col-xl-3 {
                flex: 0 0 25% !important;
                max-width: 25% !important;
            }
        }

        /* 3 cards per row on large screens (992px-1199px) */
        @media (min-width: 992px) and (max-width: 1199.98px) {
            .col-lg-4 {
                flex: 0 0 33.333333% !important;
                max-width: 33.333333% !important;
            }
        }

        /* 2 cards per row on medium screens (768px-991px) */
        @media (min-width: 768px) and (max-width: 991.98px) {
            .col-md-6 {
                flex: 0 0 50% !important;
                max-width: 50% !important;
            }
        }

        /* 1 card per row on small screens (below 768px) */
        @media (max-width: 767.98px) {
            .col-sm-12 {
                flex: 0 0 100% !important;
                max-width: 100% !important;
            }
        }

        /* Card hover effects */
        .hover-shadow {
            transition: all 0.3s ease;
        }

        .hover-shadow:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
        }

        /* Ensure cards don't overflow */
        .card-img-top {
            width: 100%;
            height: 160px;
            object-fit: cover;
        }

        /* Card styling to match the design */
        .card {
            border-radius: 12px !important;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .card-body {
            padding: 1rem !important;
            overflow-x: hidden;
        }

        /* Ensure proper row behavior */
        .row {
            margin-left: -0.75rem !important;
            margin-right: -0.75rem !important;
        }

        .row > * {
            padding-left: 0.75rem !important;
            padding-right: 0.75rem !important;
        }
        .avatar-sm {
            width: 36px;
            height: 36px;
            border-radius: 0.25rem;
            object-fit: cover;
        }
        .rounded-pill {
            border-radius: 50rem !important;
        }

        /* Ensure table responsiveness for horizontal scrolling */
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        /* GRID FIX - Force proper Bootstrap grid behavior */
        .row.g-3 {
            display: flex !important;
            flex-wrap: wrap !important;
            margin: 0 -0.5rem !important;
        }

        .row.g-3 > .col-lg-4,
        .row.g-3 > .col-md-6,
        .row.g-3 > .col-12 {
            padding: 0 0.5rem 1rem 0.5rem !important;
            box-sizing: border-box !important;
        }

        /* Override any conflicting styles */
        @media (min-width: 992px) {
            .row.g-3 > .col-lg-4 {
                flex: 0 0 33.333333% !important;
                max-width: 33.333333% !important;
                width: 33.333333% !important;
            }
        }

        @media (min-width: 768px) and (max-width: 991.98px) {
            .row.g-3 > .col-md-6 {
                flex: 0 0 50% !important;
                max-width: 50% !important;
                width: 50% !important;
            }
        }

        @media (max-width: 767.98px) {
            .row.g-3 > .col-12 {
                flex: 0 0 100% !important;
                max-width: 100% !important;
                width: 100% !important;
            }
        }
    </style>
</x-app-layout>
