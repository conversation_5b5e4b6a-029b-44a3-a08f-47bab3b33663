<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Models\PhoneVerification;


class VeevoTechService
{
    protected $apiKey;
    protected $baseUrl;
    protected $senderNumber;
    protected $header;
    protected $isDisabled;

    public function __construct()
    {
        $this->apiKey = config('services.veevotech.key');
        $this->baseUrl = 'https://api.veevotech.com/v3';
        $this->senderNumber = config('services.veevotech.sender', 'Default');
        $this->header = config('services.veevotech.header', 'Default');
        $this->isDisabled = config('services.veevotech.disabled', false);
    }

    public function sendSMS($to, $message)
    {
        try {
            // Format phone number to ensure it starts with +
            $to = $this->formatPhoneNumber($to);

            // Prepare the request data
            $data = [
                'hash' => $this->apiKey,
                'receivernum' => $to,
                'sendernum' => $this->senderNumber,
                'header' => $this->header,
                'textmessage' => $message
            ];

            // Log the request for debugging
            Log::info('VeevoTech SMS Request:', $data);

            // If SMS service is disabled, log and return mock response
            if ($this->isDisabled) {
                Log::info('VeevoTech SMS is disabled. Message would have been sent to: ' . $to);
                return [
                    'STATUS' => 'SUCCESS',
                    'MESSAGE' => 'SMS sending is currently disabled',
                    'mock' => true
                ];
            }

            // Make the API request
            $response = Http::post($this->baseUrl . '/sendsms', $data);

            // Parse the response
            $responseData = json_decode($response->body(), true);

            // Log the response for debugging
            Log::info('VeevoTech SMS Response:', [
                'status' => $response->status(),
                'body' => $responseData
            ]);

            // Check if the response indicates an error
            if (isset($responseData['STATUS']) && $responseData['STATUS'] === 'ERROR') {
                $errorMessage = $responseData['ERROR_DESCRIPTION'] ?? 'Unknown error occurred';
                Log::error('VeevoTech API Error: ' . $errorMessage);
                throw new \Exception('Failed to send SMS: ' . $errorMessage);
            }

            if (!$response->successful()) {
                Log::error('VeevoTech API Error: ' . $response->body());
                throw new \Exception('Failed to send SMS: ' . $response->body());
            }

            return $responseData;
        } catch (\Exception $e) {
            Log::error('VeevoTech Error: ' . $e->getMessage());
            throw $e;
        }
    }

    public function sendVerificationCode($phoneNumber)
    {
        $code = mt_rand(100000, 999999);
        $expiresAt = now()->addMinutes(15);

        // Store the code in the database
        PhoneVerification::updateOrCreate(
            ['phone' => $phoneNumber],
            ['code' => $code, 'expires_at' => $expiresAt]
        );

        // Format the message
        $message = "Your verification code is: $code";

        // Send the SMS
        return $this->sendSMS($phoneNumber, $message);
    }

    protected function formatPhoneNumber($phone)
    {
        // Remove any non-digit characters except +
        $phone = preg_replace('/[^0-9+]/', '', $phone);

        // If the number doesn't start with +, add it
        if (!str_starts_with($phone, '+')) {
            $phone = '+' . $phone;
        }

        return $phone;
    }
} 