/**
 * Ultimate Early CSP Fix
 * Runs as early as possible to prevent any CSP violations
 */

(function() {
    'use strict';
    
    // Get nonce from current script
    const currentScript = document.currentScript;
    const nonce = currentScript ? currentScript.getAttribute('nonce') : null;
    
    if (!nonce) {
        console.error('Ultimate Early CSP Fix: No nonce available');
        return;
    }
    
    console.log('Ultimate Early CSP Fix: Starting with nonce:', nonce);
    
    let styleCounter = 0;
    
    // Function to create safe styles
    function createSafeStyle(css, className) {
        const style = document.createElement('style');
        style.setAttribute('nonce', nonce);
        style.textContent = className ? '.' + className + ' { ' + css + ' }' : css;
        document.head.appendChild(style);
        return style;
    }
    
    // Function to fix all violations immediately
    function fixAllViolationsNow() {
        try {
            // Fix all inline styles
            const elementsWithStyle = document.querySelectorAll('[style]');
            elementsWithStyle.forEach(element => {
                const inlineStyle = element.getAttribute('style');
                if (inlineStyle && inlineStyle.trim()) {
                    element.removeAttribute('style');
                    const className = 'uecf-' + Date.now() + '-' + (++styleCounter);
                    element.classList.add(className);
                    createSafeStyle(inlineStyle, className);
                }
            });
            
            // Fix all scripts without nonce
            const scriptsWithoutNonce = document.querySelectorAll('script:not([src]):not([nonce])');
            scriptsWithoutNonce.forEach(script => {
                script.setAttribute('nonce', nonce);
            });
            
            // Fix all styles without nonce
            const stylesWithoutNonce = document.querySelectorAll('style:not([nonce])');
            stylesWithoutNonce.forEach(style => {
                style.setAttribute('nonce', nonce);
            });
            
        } catch (error) {
            console.error('Ultimate Early CSP Fix: Error during fix:', error);
        }
    }
    
    // Override dangerous methods immediately
    const originalSetAttribute = Element.prototype.setAttribute;
    Element.prototype.setAttribute = function(name, value) {
        if (name === 'style' && value && value.trim()) {
            const className = 'uecf-override-' + Date.now() + '-' + (++styleCounter);
            this.classList.add(className);
            createSafeStyle(value, className);
            return;
        }
        return originalSetAttribute.call(this, name, value);
    };
    
    // Override style property
    const originalStyleDescriptor = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'style');
    if (originalStyleDescriptor && originalStyleDescriptor.set) {
        Object.defineProperty(HTMLElement.prototype, 'style', {
            set: function(value) {
                if (typeof value === 'string' && value.trim()) {
                    const className = 'uecf-style-' + Date.now() + '-' + (++styleCounter);
                    this.classList.add(className);
                    createSafeStyle(value, className);
                    return;
                }
                return originalStyleDescriptor.set.call(this, value);
            },
            get: originalStyleDescriptor.get
        });
    }
    
    // Override document.createElement to add nonces automatically
    const originalCreateElement = document.createElement;
    document.createElement = function(tagName) {
        const element = originalCreateElement.call(this, tagName);
        if (tagName.toLowerCase() === 'style' || tagName.toLowerCase() === 'script') {
            element.setAttribute('nonce', nonce);
        }
        return element;
    };
    
    // Run fix immediately
    fixAllViolationsNow();
    
    // Set up observer for any new content
    if (typeof MutationObserver !== 'undefined') {
        const observer = new MutationObserver(function(mutations) {
            fixAllViolationsNow();
        });
        
        observer.observe(document.documentElement, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style']
        });
    }
    
    // Run fix continuously
    const fixInterval = setInterval(fixAllViolationsNow, 10);
    
    // Stop after 10 seconds to avoid performance issues
    setTimeout(() => {
        clearInterval(fixInterval);
        console.log('Ultimate Early CSP Fix: Continuous fixing stopped after 10 seconds');
    }, 10000);
    
    // Expose global function
    window.ultimateEarlyCSPFix = {
        fix: fixAllViolationsNow,
        nonce: nonce
    };
    
    console.log('Ultimate Early CSP Fix: Initialized successfully');
    
})();