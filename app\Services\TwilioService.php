<?php

namespace App\Services;

use Twilio\Rest\Client;
use Illuminate\Support\Facades\Log;
use App\Models\PhoneVerification;

class TwilioService
{
    protected $client;
    protected $sid;
    protected $token;
    protected $fromNumber;
    protected $whatsAppFromNumber;

    public function __construct()
    {
        $this->sid = config('services.twilio.sid');
        $this->token = config('services.twilio.token');
        $this->fromNumber = config('services.twilio.from');
        $this->whatsAppFromNumber = config('services.twilio.whatsapp_from', 'whatsapp:+14155238886');
    }

    public function sendSMS($to, $message)
    {
        try {
            $twilio = new Client($this->sid, $this->token);
            $response = $twilio->messages->create(
                "whatsapp:$to",
                [
                    'from' => $this->whatsAppFromNumber,
                    'body' => $message,
                ]
            );

            return $response->sid;
        } catch (\Twilio\Exceptions\RestException $e) {
            \Log::error('Twilio REST Error: ' . $e->getMessage());
            throw $e;
        } catch (\Exception $e) {
            \Log::error('Twilio General Error: ' . $e->getMessage());
            throw $e;
        }
    }

    public function sendVerificationCode($phoneNumber)
    {
        $code = mt_rand(100000, 999999);
        $expiresAt = now()->addMinutes(15);

        PhoneVerification::updateOrCreate(
            ['phone' => $phoneNumber],
            ['code' => $code, 'expires_at' => $expiresAt]
        );

        return $this->sendSMS($phoneNumber, "Your verification code is: $code");
    }
}
