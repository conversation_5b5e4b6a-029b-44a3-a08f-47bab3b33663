"use strict";!function(){var e,t;-1<navigator.platform.indexOf("Win")&&(document.getElementsByClassName("main-content")[0]&&(e=document.querySelector(".main-content"),new PerfectScrollbar(e)),document.getElementsByClassName("sidenav")[0]&&(e=document.querySelector(".sidenav"),new PerfectScrollbar(e)),document.getElementsByClassName("navbar-collapse")[0]&&(t=document.querySelector(".navbar:not(.navbar-expand-lg) .navbar-collapse"),new PerfectScrollbar(t)),document.getElementsByClassName("fixed-plugin")[0]&&(t=document.querySelector(".fixed-plugin"),new PerfectScrollbar(t)))}(),navbarBlurOnScroll("navbarBlur");var fixedPlugin,fixedPluginButton,fixedPluginButtonNav,fixedPluginCard,fixedPluginCloseButton,navbar,buttonNavbarFixed,popoverTriggerList=[].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]')),popoverList=popoverTriggerList.map(function(e){return new bootstrap.Popover(e)}),tooltipTriggerList=[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')),tooltipList=tooltipTriggerList.map(function(e){return new bootstrap.Tooltip(e)}),total=(document.querySelector(".fixed-plugin")&&(fixedPlugin=document.querySelector(".fixed-plugin"),fixedPluginButton=document.querySelector(".fixed-plugin-button"),fixedPluginButtonNav=document.querySelector(".fixed-plugin-button-nav"),fixedPluginCard=document.querySelector(".fixed-plugin .card"),fixedPluginCloseButton=document.querySelectorAll(".fixed-plugin-close-button"),navbar=document.getElementById("navbarBlur"),buttonNavbarFixed=document.getElementById("navbarFixed"),fixedPluginButton&&(fixedPluginButton.onclick=function(){fixedPlugin.classList.contains("show")?fixedPlugin.classList.remove("show"):fixedPlugin.classList.add("show")}),fixedPluginButtonNav&&(fixedPluginButtonNav.onclick=function(){fixedPlugin.classList.contains("show")?fixedPlugin.classList.remove("show"):fixedPlugin.classList.add("show")}),fixedPluginCloseButton.forEach(function(e){e.onclick=function(){fixedPlugin.classList.remove("show")}}),document.querySelector("body").onclick=function(e){e.target!=fixedPluginButton&&e.target!=fixedPluginButtonNav&&e.target.closest(".fixed-plugin .card")!=fixedPluginCard&&fixedPlugin.classList.remove("show")},navbar&&"true"==navbar.getAttribute("navbar-scroll")&&buttonNavbarFixed.setAttribute("checked","true")),document.querySelectorAll(".nav-pills"));function getEventTarget(e){return(e=e||window.event).target||e.srcElement}function sidebarColor(e){for(var t=e.parentElement.children,n=e.getAttribute("data-color"),i=0;i<t.length;i++)t[i].classList.remove("active");e.classList.contains("active")?e.classList.remove("active"):e.classList.add("active"),document.querySelector(".sidenav").setAttribute("data-color",n),document.querySelector("#sidenavCard")&&((e=document.querySelector("#sidenavCardIcon")).setAttribute("class",""),e.classList.add("text-"+n))}function navbarFixed(e){var t=["position-sticky","blur","shadow-blur","mt-4","left-auto","top-1","z-index-sticky"];const n=document.getElementById("navbarBlur");e.getAttribute("checked")?(n.classList.remove(...t),n.setAttribute("navbar-scroll","false"),navbarBlurOnScroll("navbarBlur"),e.removeAttribute("checked")):(n.classList.add(...t),n.setAttribute("navbar-scroll","true"),navbarBlurOnScroll("navbarBlur"),e.setAttribute("checked","true"))}function navbarBlurOnScroll(e){const t=document.getElementById(e);e=!!t&&t.getAttribute("navbar-scroll");let n=["position-sticky","blur","shadow-blur","mt-4","left-auto","top-1","z-index-sticky"],i=["shadow-none"];function l(){t&&(t.classList.remove(...n),t.classList.add(...i),a("transparent"))}function a(e){let t=document.querySelectorAll(".navbar-main .nav-link"),n=document.querySelectorAll(".navbar-main .sidenav-toggler-line");"blur"===e?(t.forEach(e=>{e.classList.remove("text-body")}),n.forEach(e=>{e.classList.add("bg-dark")})):"transparent"===e&&(t.forEach(e=>{e.classList.add("text-body")}),n.forEach(e=>{e.classList.remove("bg-dark")}))}window.onscroll=debounce("true"==e?function(){5<window.scrollY?(t.classList.add(...n),t.classList.remove(...i),a("blur")):l()}:function(){l()},10)}function debounce(i,l,a){var o;return function(){var e=this,t=arguments,n=a&&!o;clearTimeout(o),o=setTimeout(function(){o=null,a||i.apply(e,t)},l),n&&i.apply(e,t)}}function sidebarType(e){for(var t=e.parentElement.children,n=e.getAttribute("data-class"),i=[],l=0;l<t.length;l++)t[l].classList.remove("active"),i.push(t[l].getAttribute("data-class"));e.classList.contains("active")?e.classList.remove("active"):e.classList.add("active");for(var a=document.querySelector(".sidenav"),l=0;l<i.length;l++)a.classList.remove(i[l]);a.classList.add(n)}total.forEach(function(a,e){var o=document.createElement("div"),t=a.querySelector("li:first-child .nav-link").cloneNode();t.innerHTML="-",o.classList.add("moving-tab","position-absolute","nav-link"),o.appendChild(t),a.appendChild(o),a.getElementsByTagName("li").length;o.style.padding="0px",o.style.width=a.querySelector("li:nth-child(1)").offsetWidth+"px",o.style.transform="translate3d(0px, 0px, 0px)",o.style.transition=".5s ease",a.onmouseover=function(e){let t=getEventTarget(e),l=t.closest("li");if(l){let n=Array.from(l.closest("ul").children),i=n.indexOf(l)+1;a.querySelector("li:nth-child("+i+") .nav-link").onclick=function(){o=a.querySelector(".moving-tab");let e=0;if(a.classList.contains("flex-column")){for(var t=1;t<=n.indexOf(l);t++)e+=a.querySelector("li:nth-child("+t+")").offsetHeight;o.style.transform="translate3d(0px,"+e+"px, 0px)",o.style.height=a.querySelector("li:nth-child("+t+")").offsetHeight}else{for(t=1;t<=n.indexOf(l);t++)e+=a.querySelector("li:nth-child("+t+")").offsetWidth;o.style.transform="translate3d("+e+"px, 0px, 0px)",o.style.width=a.querySelector("li:nth-child("+i+")").offsetWidth+"px"}}}}}),window.addEventListener("resize",function(e){total.forEach(function(n,e){n.querySelector(".moving-tab").remove();var i=document.createElement("div"),l=n.querySelector(".nav-link.active").cloneNode();l.innerHTML="-",i.classList.add("moving-tab","position-absolute","nav-link"),i.appendChild(l),n.appendChild(i),i.style.padding="0px",i.style.transition=".5s ease";let a=n.querySelector(".nav-link.active").parentElement;if(a){let e=Array.from(a.closest("ul").children);l=e.indexOf(a)+1;let t=0;if(n.classList.contains("flex-column")){for(var o=1;o<=e.indexOf(a);o++)t+=n.querySelector("li:nth-child("+o+")").offsetHeight;i.style.transform="translate3d(0px,"+t+"px, 0px)",i.style.width=n.querySelector("li:nth-child("+l+")").offsetWidth+"px",i.style.height=n.querySelector("li:nth-child("+o+")").offsetHeight}else{for(o=1;o<=e.indexOf(a);o++)t+=n.querySelector("li:nth-child("+o+")").offsetWidth;i.style.transform="translate3d("+t+"px, 0px, 0px)",i.style.width=n.querySelector("li:nth-child("+l+")").offsetWidth+"px"}}}),window.innerWidth<991?total.forEach(function(e,t){e.classList.contains("flex-column")||e.classList.add("flex-column","on-resize")}):total.forEach(function(e,t){e.classList.contains("on-resize")&&e.classList.remove("flex-column","on-resize")})});const iconNavbarSidenav=document.getElementById("iconNavbarSidenav"),iconSidenav=document.getElementById("iconSidenav"),sidenav=document.getElementById("sidenav-main");let body=document.getElementsByTagName("body")[0],className="g-sidenav-pinned";function toggleSidenav(){body.classList.contains(className)?(body.classList.remove(className),sidenav.classList.remove("bg-transparent")):(body.classList.add(className),sidenav.classList.remove("bg-transparent"),iconSidenav.classList.remove("d-none"))}iconNavbarSidenav&&iconNavbarSidenav.addEventListener("click",toggleSidenav),iconSidenav&&iconSidenav.addEventListener("click",toggleSidenav);let referenceButtons=document.querySelector("[data-class]");function sidenavTypeOnResize(){let e=document.querySelectorAll('[onclick="sidebarType(this)"]');window.innerWidth<1200?e.forEach(function(e){e.classList.add("disabled")}):e.forEach(function(e){e.classList.remove("disabled")})}window.addEventListener("resize",sidenavTypeOnResize),window.addEventListener("load",sidenavTypeOnResize);
//# sourceMappingURL=_site_dashboard_free/assets/js/dashboard-free.js.map