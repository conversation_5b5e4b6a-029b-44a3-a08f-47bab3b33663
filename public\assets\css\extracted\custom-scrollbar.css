/* Custom scrollbar styling to replace Perfect Scrollbar */
.custom-scrollbar {
    overflow-y: auto;
    scrollbar-width: thin; /* Firefox */
    scrollbar-color: rgba(0, 0, 0, 0.3) transparent; /* Firefox */
}

/* WebKit browsers (Chrome, Safari) */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.5);
}

/* Ensure the scrollbar doesn't take up space in the layout */
.custom-scrollbar {
    position: relative;
}

/* Add specific styling for sidenav scrollbar */
#sidenav-scrollbar.custom-scrollbar {
    max-height: calc(100vh - 200px);
}