<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Course;
use App\Models\CourseName;
use Illuminate\Support\Facades\Storage;
use Mews\Purifier\Facades\Purifier;

class CourseController extends Controller
{
    public function create()
    {
        $courseNames = 1;
        return view('courses.create', compact('courseNames'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'type' => 'required|string',
            'course_name_id' => 'required|exists:coursename,id',
            'lecture_name' => 'required|string|max:255',
            'description' => 'required|string',
            'weekly_price' => 'required|numeric|min:0',
            'monthly_price' => 'required|numeric|min:0',
            'youtube_url' => 'nullable|url',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
        ]);

        $validated['description'] = Purifier::clean($validated['description']);
        $imagePath = $request->file('image')->store('public/courses');

        Course::create([
            'type' => $validated['type'],
            'course_name_id' => $validated['course_name_id'],
            'lecture_name' => $validated['lecture_name'],
            'description' => $validated['description'],
            'weekly_price' => $validated['weekly_price'],
            'monthly_price' => $validated['monthly_price'],
            'youtube_url' => $validated['youtube_url'],
            'image_path' => $imagePath,
        ]);

        return redirect()->route('courses.index')->with('success', 'Course added successfully!');
    }

    public function index()
    {
        $courses = Course::with('courseName')->get();
        return view('courses.index', compact('courses'));
    }


}
