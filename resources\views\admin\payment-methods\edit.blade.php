@extends('admin.layout')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edit Payment Method</h1>
        <a href="{{ route('admin.payment-methods.index') }}" class="btn btn-sm btn-primary">
            <i class="fas fa-arrow-left fa-sm text-white-50 me-1"></i> Back to Payment Methods
        </a>
    </div>

    @if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif

    @if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{{ $paymentMethod->name }}</h6>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="payment-method-preview p-4 border rounded">
                        <div class="d-flex align-items-center mb-3">
                            @if(Str::startsWith($paymentMethod->icon, 'fas '))
                                <i class="{{ $paymentMethod->icon }} fa-2x me-3 {{ $paymentMethod->details['color'] ?? '' }}"></i>
                            @else
                                <img src="{{ asset($paymentMethod->icon) }}" alt="{{ $paymentMethod->name }}" width="40" class="me-3">
                            @endif
                            <div>
                                <h5 class="mb-0">{{ $paymentMethod->name }}</h5>
                                <small class="text-muted">{{ $paymentMethod->description }}</small>
                            </div>
                        </div>
                        <div class="payment-method-details">
                            <div class="alert alert-info">
                                <h6 class="mb-2"><i class="fas fa-info-circle me-2"></i>Payment Instructions</h6>
                                <p class="mb-0">{!! nl2br(e($paymentMethod->instructions)) !!}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <form action="{{ route('admin.payment-methods.update', $paymentMethod->id) }}" method="POST">
                @csrf
                @method('PUT')

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="name" class="form-label">Name</label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ old('name', $paymentMethod->name) }}" required>
                            @error('name')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="icon" class="form-label">Icon (Font Awesome Class)</label>
                            <input type="text" class="form-control" id="icon" name="icon" value="{{ old('icon', $paymentMethod->icon) }}">
                            <small class="text-muted">Example: fas fa-money-bill-wave</small>
                            @error('icon')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Short Description</label>
                    <input type="text" class="form-control" id="description" name="description" value="{{ old('description', $paymentMethod->description) }}">
                    @error('description')
                        <div class="text-danger mt-1">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="instructions" class="form-label">Instructions</label>
                    <textarea class="form-control" id="instructions" name="instructions" rows="5">{{ old('instructions', $paymentMethod->instructions) }}</textarea>
                    @error('instructions')
                        <div class="text-danger mt-1">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Payment Method specific fields -->
                @if($paymentMethod->key === 'jazzcash' || $paymentMethod->key === 'easypaisa')
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="account_number" class="form-label">Account Number</label>
                            <input type="text" class="form-control" id="account_number" name="account_number"
                                value="{{ old('account_number', $paymentMethod->details['account'] ?? '') }}" required>
                            @error('account_number')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                @endif

                @if($paymentMethod->key === 'banktransfer')
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="bank_name" class="form-label">Bank Name</label>
                            <input type="text" class="form-control" id="bank_name" name="bank_name"
                                value="{{ old('bank_name', $paymentMethod->details['bank_name'] ?? '') }}" required>
                            @error('bank_name')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="account_title" class="form-label">Account Title</label>
                            <input type="text" class="form-control" id="account_title" name="account_title"
                                value="{{ old('account_title', $paymentMethod->details['account_title'] ?? '') }}" required>
                            @error('account_title')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="account_number" class="form-label">Account Number</label>
                            <input type="text" class="form-control" id="account_number" name="account_number"
                                value="{{ old('account_number', $paymentMethod->details['account_number'] ?? '') }}" required>
                            @error('account_number')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="iban" class="form-label">IBAN</label>
                            <input type="text" class="form-control" id="iban" name="iban"
                                value="{{ old('iban', $paymentMethod->details['iban'] ?? '') }}">
                            @error('iban')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                @endif

                @if($paymentMethod->key === 'card')
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="processor" class="form-label">Payment Processor</label>
                            <select class="form-select" id="processor" name="processor">
                                <option value="stripe" {{ (old('processor', $paymentMethod->details['processor'] ?? '') === 'stripe') ? 'selected' : '' }}>Stripe</option>
                                <option value="paypal" {{ (old('processor', $paymentMethod->details['processor'] ?? '') === 'paypal') ? 'selected' : '' }}>PayPal</option>
                                <option value="other" {{ (old('processor', $paymentMethod->details['processor'] ?? '') === 'other') ? 'selected' : '' }}>Other</option>
                            </select>
                            @error('processor')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                @endif

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input type="number" class="form-control" id="sort_order" name="sort_order"
                                value="{{ old('sort_order', $paymentMethod->sort_order) }}" min="0">
                            @error('sort_order')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="is_active" class="form-label d-block">Status</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" role="switch" id="is_active" name="is_active" value="1"
                                    {{ old('is_active', $paymentMethod->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    {{ $paymentMethod->is_active ? 'Active' : 'Inactive' }}
                                </label>
                            </div>
                            @error('is_active')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Save Changes
                    </button>
                    <a href="{{ route('admin.payment-methods.index') }}" class="btn btn-secondary">
                        <i class="fas fa-times me-1"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
