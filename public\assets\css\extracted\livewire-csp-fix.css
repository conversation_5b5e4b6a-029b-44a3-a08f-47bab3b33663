/* Livewire CSP Fix Styles */

/* Pre-defined styles for Livewire components to avoid inline styles */
[wire\:loading], [wire\:loading\.delay], [wire\:loading\.inline-block], [wire\:loading\.inline], [wire\:loading\.block], [wire\:loading\.flex], [wire\:loading\.table], [wire\:loading\.grid], [wire\:loading\.inline-flex] {
    display: none;
}

[wire\:loading\.delay\.shortest], [wire\:loading\.delay\.shorter], [wire\:loading\.delay\.short], [wire\:loading\.delay\.long], [wire\:loading\.delay\.longer], [wire\:loading\.delay\.longest] {
    display: none;
}

[wire\:offline] {
    display: none;
}

[wire\:dirty]:not(textarea):not(input):not(select) {
    display: inline-block;
    position: relative;
}

[wire\:dirty]:not(textarea):not(input):not(select)::before {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    height: 8px;
    width: 8px;
    border-radius: 50%;
    background-color: #f56565;
    transform: translate(-50%, 50%);
}

/* Livewire loading states */
[wire\:loading].inline { display: inline; }
[wire\:loading].inline-block { display: inline-block; }
[wire\:loading].block { display: block; }
[wire\:loading].flex { display: flex; }
[wire\:loading].table { display: table; }
[wire\:loading].grid { display: grid; }
[wire\:loading].inline-flex { display: inline-flex; }

/* Livewire loading delay states */
[wire\:loading].delay-shortest { transition-delay: 50ms; }
[wire\:loading].delay-shorter { transition-delay: 100ms; }
[wire\:loading].delay-short { transition-delay: 150ms; }
[wire\:loading].delay-default { transition-delay: 200ms; }
[wire\:loading].delay-long { transition-delay: 300ms; }
[wire\:loading].delay-longer { transition-delay: 500ms; }
[wire\:loading].delay-longest { transition-delay: 1000ms; }

/* Livewire target states */
[wire\:target].opacity-25 { opacity: 0.25; }
[wire\:target].opacity-50 { opacity: 0.5; }
[wire\:target].opacity-75 { opacity: 0.75; }

/* Livewire dirty states */
[wire\:dirty].border-red-500 { border-color: #f56565; }
[wire\:dirty].border-green-500 { border-color: #48bb78; }
[wire\:dirty].border-blue-500 { border-color: #4299e1; }

/* Livewire transition classes */
.wire-transition { transition-property: all; }
.wire-transition-all { transition-property: all; }
.wire-transition-colors { transition-property: background-color, border-color, color, fill, stroke; }
.wire-transition-opacity { transition-property: opacity; }
.wire-transition-shadow { transition-property: box-shadow; }
.wire-transition-transform { transition-property: transform; }

.wire-transition-none { transition-duration: 0ms; }
.wire-transition-75 { transition-duration: 75ms; }
.wire-transition-100 { transition-duration: 100ms; }
.wire-transition-150 { transition-duration: 150ms; }
.wire-transition-200 { transition-duration: 200ms; }
.wire-transition-300 { transition-duration: 300ms; }
.wire-transition-500 { transition-duration: 500ms; }
.wire-transition-700 { transition-duration: 700ms; }
.wire-transition-1000 { transition-duration: 1000ms; }

/* Livewire ease transitions */
.wire-ease-linear { transition-timing-function: linear; }
.wire-ease-in { transition-timing-function: cubic-bezier(0.4, 0, 1, 1); }
.wire-ease-out { transition-timing-function: cubic-bezier(0, 0, 0.2, 1); }
.wire-ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

/* Livewire delay transitions */
.wire-delay-75 { transition-delay: 75ms; }
.wire-delay-100 { transition-delay: 100ms; }
.wire-delay-150 { transition-delay: 150ms; }
.wire-delay-200 { transition-delay: 200ms; }
.wire-delay-300 { transition-delay: 300ms; }
.wire-delay-500 { transition-delay: 500ms; }
.wire-delay-700 { transition-delay: 700ms; }
.wire-delay-1000 { transition-delay: 1000ms; }

/* Livewire animations */
@keyframes wire-spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.wire-animate-spin {
    animation: wire-spin 1s linear infinite;
}

@keyframes wire-ping {
    0% { transform: scale(1); opacity: 1; }
    75%, 100% { transform: scale(2); opacity: 0; }
}

.wire-animate-ping {
    animation: wire-ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

@keyframes wire-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: .5; }
}

.wire-animate-pulse {
    animation: wire-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes wire-bounce {
    0%, 100% { 
        transform: translateY(-25%);
        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }
    50% {
        transform: translateY(0);
        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
}

.wire-animate-bounce {
    animation: wire-bounce 1s infinite;
}