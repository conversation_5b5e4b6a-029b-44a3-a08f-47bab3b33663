/* CSP-compliant styles for sign-in page */

/* Captcha display styling */
.captcha-container {
    display: inline-block;
    padding: 10px 15px;
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    font-family: monospace;
    font-size: 1.25rem;
    font-weight: bold;
    letter-spacing: 2px;
    color: #212529;
    text-align: center;
    min-width: 120px;
    user-select: none;
}

/* Oblique image styling */
.auth-bg-image {
    background-image: url('../../../assets/img/curved-images/curved6.jpg');
    background-size: cover;
    background-position: center;
}

/* Password toggle button styling */
.auth-password-toggle {
    background: transparent;
    border: none;
    color: #6c757d;
    padding: 0;
    font-size: 1rem;
}

.auth-password-toggle:hover {
    color: #495057;
}

/* Fix for any inline styles in blur elements */
.blur {
    backdrop-filter: blur(5px);
    background-color: rgba(255, 255, 255, 0.8);
}

/* Fix for any inline styles in the form */
.auth-input {
    border-color: #ced4da;
}

.auth-input:focus {
    border-color: #774dd3;
    box-shadow: 0 0 0 0.25rem rgba(119, 77, 211, 0.25);
}

.auth-input-password {
    padding-right: 40px;
}

.auth-captcha-input {
    flex: 1;
}

.auth-submit-btn {
    background-color: #774dd3;
    border-color: #774dd3;
}

.auth-submit-btn:hover {
    background-color: #6a44be;
    border-color: #6a44be;
}

.auth-signup-btn {
    background-color: #64748b;
    border-color: #64748b;
}

.auth-signup-btn:hover {
    background-color: #576a7d;
    border-color: #576a7d;
}

/* Icon colors */
.auth-icon-primary {
    color: #774dd3;
}

.auth-icon-secondary {
    color: #64748b;
}

/* Support link styling */
.auth-support-link {
    color: #774dd3;
    text-decoration: none;
}

.auth-support-link:hover {
    text-decoration: underline;
}

/* Captcha display styled class */
.captcha-display-styled {
    display: inline-block;
    padding: 10px 15px;
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    font-family: monospace;
    font-size: 1.25rem;
    font-weight: bold;
    letter-spacing: 2px;
    color: #212529;
    text-align: center;
    min-width: 120px;
}

/* Oblique image styled class */
.oblique-image-styled {
    background-image: url('../../../assets/img/curved-images/curved6.jpg');
    background-size: cover;
    background-position: center;
}