@extends('admin.layout')

@section('title', 'Manage Coupons')

@section('header', 'Coupon Management')

@section('actions')
<a href="{{ route('admin.coupons.create') }}" class="btn btn-sm btn-primary">
    <i class="fas fa-plus"></i> Add New Coupon
</a>
@endsection

@section('content')
    <div class="card">
        <div class="card-body px-0 pt-0 pb-2">
            <div class="table-responsive p-0">
                <table class="table align-items-center mb-0">
                    <thead>
                        <tr>
                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Code</th>
                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Discount</th>
                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Valid Until</th>
                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Uses / Max</th>
                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Status</th>
                            <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($coupons as $coupon)
                        <tr>
                            <td>
                                <div class="d-flex px-2 py-1">
                                    <div class="d-flex flex-column justify-content-center">
                                        <h6 class="mb-0 text-sm">{{ $coupon->code }}</h6>
                                        @if($coupon->description)
                                        <p class="text-xs text-secondary mb-0">{{ Str::limit($coupon->description, 30) }}</p>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td>
                                <p class="text-xs font-weight-bold mb-0">
                                    @if($coupon->type == 'percentage')
                                    {{ $coupon->value }}%
                                    @elseif($coupon->type == 'fixed')
                                    {{ config('app.currency_symbol', '$') }}{{ $coupon->value }}
                                    @else
                                    Free
                                    @endif
                                </p>
                            </td>
                            <td>
                                <p class="text-xs font-weight-bold mb-0">
                                    {{ $coupon->valid_until->format('M d, Y') }}
                                </p>
                                <p class="text-xs text-secondary mb-0">
                                    @if($coupon->valid_until->isPast())
                                    <span class="text-danger">Expired</span>
                                    @else
                                    {{ $coupon->valid_until->diffForHumans() }}
                                    @endif
                                </p>
                            </td>
                            <td>
                                <p class="text-xs font-weight-bold mb-0">
                                    {{ $coupon->uses_count ?? 0 }}
                                    @if($coupon->max_uses)
                                    / {{ $coupon->max_uses }}
                                    @else
                                    / ∞
                                    @endif
                                </p>
                            </td>
                            <td>
                                <span class="badge bg-{{ $coupon->is_active ? 'success' : 'secondary' }}">
                                    {{ $coupon->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-link text-secondary mb-0"
                                        data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="fa fa-ellipsis-v text-xs"></i>
                                    </button>
                                    <div class="dropdown-menu dropdown-menu-end">
                                        <a class="dropdown-item" href="{{ route('admin.coupons.edit', $coupon->id) }}">
                                            <i class="fas fa-edit me-2"></i> Edit
                                        </a>
                                        <a class="dropdown-item text-danger delete-coupon" href="javascript:;"
                                           data-coupon-id="{{ $coupon->id }}" data-coupon-code="{{ $coupon->code }}">
                                            <i class="fas fa-trash me-2"></i> Delete
                                        </a>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <p class="text-sm mb-0">No coupons found</p>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            @if($coupons->hasPages())
            <div class="px-3 pt-4">
                {{ $coupons->links() }}
            </div>
            @endif
        </div>
    </div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set up delete confirmation
        const deleteLinks = document.querySelectorAll('.delete-coupon');
        deleteLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const couponId = this.getAttribute('data-coupon-id');
                const couponCode = this.getAttribute('data-coupon-code');

                if (confirm(`Are you sure you want to delete coupon "${couponCode}"? This action cannot be undone.`)) {
                    // Create and submit a form to delete the coupon
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/admin/coupons/${couponId}`;
                    form.style.display = 'none';

                    const csrfToken = document.createElement('input');
                    csrfToken.type = 'hidden';
                    csrfToken.name = '_token';
                    csrfToken.value = '{{ csrf_token() }}';

                    const methodField = document.createElement('input');
                    methodField.type = 'hidden';
                    methodField.name = '_method';
                    methodField.value = 'DELETE';

                    form.appendChild(csrfToken);
                    form.appendChild(methodField);
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
</script>
@endsection
