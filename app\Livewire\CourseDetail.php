<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Course;
use App\Models\Order;
use App\Models\UserCourse;
use Illuminate\Support\Facades\Auth;

class CourseDetail extends Component
{
    public $course;
    public $priceToggle = 'weekly';
    public $instructorProfiles = [];

    public function mount($id)
    {
        $this->course = Course::with(['lectures', 'features', 'ratings' => function($query) {
            $query->where('is_approved', true)
                 ->where('show_publicly', true)
                 ->with('user')
                 ->latest();
        }])->findOrFail($id);

        // Ensure instructor data is available for display
        if (!$this->course->instructor) {
            $this->course->instructor = 'Instructor'; // Default value if none set
        }

        // Get instructor profiles for the course and its lectures
        $this->loadInstructorProfiles();
    }

    /**
     * Load instructor profiles for the course and its lectures
     */
    private function loadInstructorProfiles()
    {
        // Get all unique instructor names from this course and its lectures
        $instructorNames = collect([$this->course->instructor]);
        $lectureInstructors = $this->course->lectures->pluck('instructor')->filter();
        $instructorNames = $instructorNames->merge($lectureInstructors)->filter()->unique()->values();

        if ($instructorNames->isEmpty()) {
            return;
        }

        // Load instructor profiles with debug info
        $profiles = \App\Models\InstructorProfile::whereIn('name', $instructorNames)
            ->where('is_active', true)
            ->get();

        // Log retrieved profiles for debugging
        \Log::info('Instructor profiles loaded for course', [
            'course_id' => $this->course->id,
            'instructor_names' => $instructorNames->toArray(),
            'profiles_count' => $profiles->count(),
            'profiles' => $profiles->pluck('name', 'id')->toArray()
        ]);

        $this->instructorProfiles = $profiles->keyBy('name')->toArray();
    }

    public function addToCart($id, $priceType = 'weekly', $type = 'course')
    {
        if (!auth()->check()) {
            session()->flash('error', 'Please login or register to add items to cart.');
            return redirect()->route('sign-in');
        }

        // Check if user is verified
        if (!auth()->user()->email_verified_at || !auth()->user()->phone_verified_at) {
            Auth::logout();
            session()->invalidate();
            session()->regenerateToken();
            return redirect()->route('sign-in')
                ->with('error', 'Please verify your email and phone number to add items to cart.');
        }

        $userId = auth()->id();

        if ($type === 'course') {
            // Get course price
            $price = $priceType === 'weekly' ? $this->course->weekly_price : $this->course->monthly_price;

            // Check if user has already purchased any lectures from this course
            $purchasedLectures = \App\Models\UserCourse::where('user_id', $userId)
                ->whereNotNull('lecture_id')
                ->where('status', 'active')
                ->whereHas('lecture', function ($query) use ($id) {
                    $query->where('course_id', $id);
                })
                ->with('lecture')
                ->get();

            // If user has purchased lectures from this course, subtract their cost
            $discountAmount = 0;
            foreach ($purchasedLectures as $purchasedLecture) {
                if ($purchasedLecture->lecture) {
                    $lecturePrice = $priceType === 'weekly'
                        ? $purchasedLecture->lecture->weekly_price
                        : $purchasedLecture->lecture->monthly_price;
                    $discountAmount += $lecturePrice;
                }
            }

            // Apply discount - don't go below zero
            $finalPrice = max(0, $price - $discountAmount);

            // Generate a reason message if there's a discount
            $discountReason = null;
            if ($discountAmount > 0) {
                $purchasedLectureNames = $purchasedLectures->pluck('lecture.name')->implode(', ');
                $discountReason = "Discount for previously purchased lecture(s): " . $purchasedLectureNames;
            }

            // Logic for adding course to cart
            \App\Models\Shoppingcart::updateOrCreate(
                ['user_id' => $userId, 'course_id' => $id],
                [
                    'price' => $finalPrice,
                    'price_type' => (string)$priceType,
                    'original_price' => $price,
                    'discount_amount' => $discountAmount,
                    'discount_reason' => $discountReason
                ]
            );

            // If there was a discount, show a message
            if ($discountAmount > 0) {
                session()->flash('success', 'Course added to cart! $' . number_format($discountAmount, 2) . ' discount applied for previously purchased lectures.');
            } else {
                session()->flash('success', 'Course added to cart successfully!');
            }
        } else {
            // Logic for adding lecture to cart
            $lecture = $this->course->lectures->find($id);
            if (!$lecture) {
                session()->flash('error', 'Lecture not found.');
                return;
            }

            $lecturePrice = $priceType === 'weekly' ? $lecture->weekly_price : $lecture->monthly_price;

            \App\Models\Shoppingcart::updateOrCreate(
                ['user_id' => $userId, 'lecture_id' => $id],
                ['price' => $lecturePrice, 'price_type' => (string)$priceType]
            );

            session()->flash('success', 'Lecture added to cart successfully!');
        }

        // Dispatch event to update cart icon
        $this->dispatch('cartUpdated');
    }

    public function isPurchased($itemId, $type = 'course')
    {
        if (!auth()->check()) {
            return false;
        }

        return UserCourse::where('user_id', auth()->id())
            ->where($type.'_id', $itemId)
            ->where('status', 'active')
            ->exists();
    }

    public function isPending($itemId, $type = 'course')
    {
        if (!auth()->check()) {
            return false;
        }

        $userId = auth()->id();
        $pendingOrders = Order::where('user_id', $userId)
            ->whereIn('status', ['pending', 'awaiting_payment'])
            ->get();

        foreach ($pendingOrders as $order) {
            $cartItems = json_decode($order->cart_items, true);
            if (is_array($cartItems)) {
                foreach ($cartItems as $item) {
                    $itemKey = $type . '_id';
                    if (isset($item[$itemKey]) && $item[$itemKey] == $itemId) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    public function render()
    {
        return view('livewire.course-detail')->layout('layouts.app');
    }
}
