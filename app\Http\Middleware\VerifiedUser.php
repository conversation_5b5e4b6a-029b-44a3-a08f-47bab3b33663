<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

class VerifiedUser
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next)
    {
        // List of routes that are completely public (no auth required)
        $publicRoutes = [
            'dashboard',
            'courses',
            'course.detail',
            'lecture.detail',
            'instructor-profiles',
            'search',
            'sign-in',
            'sign-up',
            'password.request',
            'password.reset',
            'password.email'
        ];

        // List of routes that require auth but allow unverified access
        $unverifiedAllowedRoutes = [
            'verification.notice',
            'verification.form',
            'phone.verify',
            'email.verify',
            'verification.resend',
            'logout'
        ];

        $currentRoute = $request->route()->getName();
        $user = Auth::user();

        // If route is public and user is not logged in, allow access
        if (in_array($currentRoute, $publicRoutes) && !$user) {
            return $next($request);
        }

        // If user is logged in but not verified
        if ($user && (!$user->email_verified_at || !$user->phone_verified_at)) {
            // Allow access only to verification routes
            if (in_array($currentRoute, $unverifiedAllowedRoutes)) {
                return $next($request);
            }

            // For public routes, force logout and redirect
            if (in_array($currentRoute, $publicRoutes)) {
                Auth::logout();
                $request->session()->invalidate();
                $request->session()->regenerateToken();
                return redirect()->route($currentRoute)
                    ->with('warning', 'Please verify your account or browse as a guest.');
            }

            // For all other routes, redirect to verification
            return redirect()->route('verification.notice')
                ->with('error', 'You must verify both your email and phone number before accessing this page.');
        }

        // If user is verified admin/superadmin, allow all access (only email verification required for admin)
        if ($user && $user->email_verified_at && ($user->isAdmin() || $user->isSuperAdmin())) {
            return $next($request);
        }

        // If user is verified regular user, allow access
        if ($user && $user->email_verified_at && $user->phone_verified_at) {
            return $next($request);
        }

        // For public routes, allow access
        if (in_array($currentRoute, $publicRoutes)) {
            return $next($request);
        }

        // If we get here, redirect to verification
        return redirect()->route('verification.notice')
            ->with('error', 'You must verify both your email and phone number before accessing this page.');
    }
}
