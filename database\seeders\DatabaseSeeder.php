<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    public function run()
    {
        $this->call([
            CountriesStatesCitiesSeeder::class,
            CategoriesSeeder::class,
            RoleSeeder::class,
            CoursesAndLecturesSeeder::class,
            QuizSeeder::class,
            CourseFeatureSeeder::class,
            LectureFeatureSeeder::class,
            AdminUserSeeder::class,
            PaymentMethodSeeder::class,
            SuperAdminSeeder::class,
            UserSeeder::class,
            AdminPermissionsSeeder::class,
            InstructorProfileSeeder::class,
        ]);

        User::factory()->create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('secret'),
            'about' => "Hi, I'm <PERSON>, Decisions: If you can't decide, the answer is no. If two equally difficult paths, choose the one more painful in the short term (pain avoidance is creating an illusion of equality).",
        ]);
    }
}
