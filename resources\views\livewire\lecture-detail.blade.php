<div class="container py-5">
    <!-- Lecture Header -->
    <div class="row mb-5">
        <div class="col-lg-8">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('courses') }}">Courses</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('course.detail', $course->id) }}">{{ $course->name }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ $lecture->name }}</li>
                </ol>
            </nav>

            <h1 class="fw-bold mb-2">{{ $lecture->name }}</h1>
            <p class="lead mb-3">{{ Str::limit(strip_tags($lecture->description), 200) }}</p>

            <!-- Instructor, Reviews, etc -->
            <div class="d-flex flex-wrap align-items-center mb-3">
                <span class="me-3 mb-2">
                    <i class="fas fa-user-tie me-1"></i> {{ $lecture->instructor ?: ($lecture->course->instructor ?? 'Instructor Name') }}
                </span>
                <span class="me-3 mb-2">
                    <i class="fas fa-book me-1"></i> Part of: {{ $course->name }}
                </span>
                <span class="me-3 mb-2">
                    @for ($i = 1; $i <= 5; $i++)
                        @if ($i <= $lecture->average_rating)
                            <i class="fas fa-star text-warning"></i>
                        @elseif ($i - 0.5 <= $lecture->average_rating)
                            <i class="fas fa-star-half-alt text-warning"></i>
                        @else
                            <i class="far fa-star text-warning"></i>
                        @endif
                    @endfor
                    <span class="ms-1">({{ number_format($lecture->average_rating, 1) }})</span>
                </span>
                <span class="mb-2">
                    <i class="fas fa-users me-1"></i> {{ $lecture->rating_count }} {{ Str::plural('review', $lecture->rating_count) }}
                </span>
            </div>

            <!-- Last Updated -->
            <p class="text-muted small">
                <i class="fas fa-history me-1"></i> Last updated {{ $lecture->updated_at->format('M Y') }}
            </p>
        </div>

        <div class="col-lg-4">
            <div class="card shadow">
                <div class="course-image-container">
                    <img src="{{ $lecture->image_path ? Storage::url($lecture->image_path) : 'https://via.placeholder.com/300x200' }}"
                        alt="{{ $lecture->name }}"
                        class="card-img-top">
                </div>

                <div class="card-body">
                    <!-- Pricing Toggle -->
                    <div class="btn-group w-100 mb-3" role="group">
                        <button type="button" class="btn {{ $priceToggle === 'weekly' ? 'btn-primary' : 'btn-outline-primary' }}"
                            wire:click="$set('priceToggle', 'weekly')">
                            Weekly Price
                        </button>
                        <button type="button" class="btn {{ $priceToggle === 'monthly' ? 'btn-primary' : 'btn-outline-primary' }}"
                            wire:click="$set('priceToggle', 'monthly')">
                            Monthly Price
                        </button>
                    </div>

                    <!-- Price -->
                    <h3 class="fw-bold text-center mb-3">${{ $priceToggle === 'weekly' ? $lecture->weekly_price : $lecture->monthly_price }}</h3>

                    <!-- Add to Cart Button -->
                    <div class="d-grid gap-2">
                        @if(auth()->check() && $this->isPurchased($lecture->id, 'lecture'))
                            <button type="button" class="btn btn-success btn-lg" disabled>
                                <i class="fas fa-check-circle me-1"></i> Purchased
                            </button>
                        @elseif(auth()->check() && $this->isPending($lecture->id, 'lecture'))
                            <button type="button" class="btn btn-warning btn-lg" disabled>
                                <i class="fas fa-clock me-1"></i> Pending Approval
                            </button>
                        @elseif(auth()->check() && \App\Models\Shoppingcart::where('user_id', auth()->id())->where('lecture_id', $lecture->id)->exists())
                            <button type="button" class="btn btn-secondary btn-lg" disabled>
                                <i class="fas fa-check me-1"></i> Added to Cart
                            </button>
                        @else
                            <button type="button" wire:click="addToCart({{ $lecture->id }}, '{{ $priceToggle }}')" class="btn btn-primary btn-lg">
                                <i class="fas fa-cart-plus me-1"></i> Add to Cart
                            </button>
                        @endif

                        <a href="{{ route('course.detail', $course->id) }}" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-1"></i> Back to Full Course
                        </a>
                    </div>

                    <!-- Lecture Includes -->
                    <div class="mt-4">
                        <h5>This lecture includes:</h5>
                        <ul class="list-unstyled">
                            <li class="mb-2"><i class="fas fa-video me-2"></i> {{ rand(30, 90) }} minutes on-demand video</li>
                            @if($lecture->youtube_url)
                                <li class="mb-2"><i class="fab fa-youtube me-2"></i> Online video content</li>
                            @endif
                            <li class="mb-2"><i class="fas fa-file-pdf me-2"></i> Downloadable resources</li>
                            <li class="mb-2"><i class="fas fa-infinity me-2"></i> Full lifetime access</li>
                            <li class="mb-2"><i class="fas fa-mobile-alt me-2"></i> Access on mobile and TV</li>

                            @foreach($lecture->features as $feature)
                                <li class="mb-2">
                                    <i class="fas fa-check me-2"></i> {{ $feature->feature_text }}
                                </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Lecture Content Tabs -->
    <div class="row">
        <div class="col-12">
            <ul class="nav nav-tabs mb-4" id="lectureTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab" aria-controls="overview" aria-selected="true">Overview</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button" role="tab" aria-controls="reviews" aria-selected="false">Reviews</button>
                </li>
            </ul>

            <div class="tab-content" id="lectureTabContent">
                <!-- Overview Tab -->
                <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                    <div class="row">
                        <div class="col-md-8">
                            <h3 class="mb-4">About This Lecture</h3>
                            <div class="lecture-description mb-5">
                                {!! $lecture->description !!}
                            </div>

                            @if($lecture->intro_video_url)
                            <!-- Lecture Intro Video -->
                            <div class="mb-5">
                                <h4 class="mb-3">Lecture Introduction</h4>
                                <div class="ratio ratio-16x9">
                                    <iframe 
                                        src="{{ str_replace('watch?v=', 'embed/', $lecture->intro_video_url) }}" 
                                        title="Lecture Introduction Video"
                                        allowfullscreen
                                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture">
                                    </iframe>
                                </div>
                            </div>
                            @endif

                            <h4 class="mb-3">What You'll Learn</h4>
                            <ul class="check-list mb-5">
                                @forelse($lecture->getFeaturesByType('learn') as $feature)
                                    <li>{{ $feature->feature_text }}</li>
                                @empty
                                    <li>Understand key concepts covered in this lecture</li>
                                    <li>Apply these concepts to real-world scenarios</li>
                                    <li>Build on your knowledge from previous lectures</li>
                                    <li>Prepare for upcoming topics in the course</li>
                                @endforelse
                            </ul>

                            <h4 class="mb-3">Prerequisites</h4>
                            <ul class="mb-5">
                                @forelse($lecture->getFeaturesByType('requirement') as $feature)
                                    <li>{{ $feature->feature_text }}</li>
                                @empty
                                    <li>Basic understanding of course fundamentals</li>
                                    <li>Completion of previous lectures (recommended)</li>
                                @endforelse
                            </ul>
                        </div>

                        <div class="col-md-4">
                            <!-- Instructor Card -->
                            <div class="card mb-4">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">Instructor</h5>
                                </div>
                                <div class="card-body">
                                    @php
                                        $instructorName = $lecture->instructor;
                                        $profile = $instructorProfiles[$instructorName] ?? null;
                                    @endphp
                                    <div class="d-flex align-items-center mb-3">
                                        @if($profile && isset($profile['image_path']) && $profile['image_path'])
                                            @php
                                                // Try both storage paths to ensure image shows up
                                                $imagePath = $profile['image_path'];
                                                $storageUrl = Storage::url($imagePath);
                                                $assetUrl = asset('storage/' . $imagePath);
                                            @endphp
                                            <img src="{{ $storageUrl }}" 
                                                onerror="this.onerror=null; this.src='{{ $assetUrl }}';"
                                                class="rounded-circle me-3" 
                                                width="80" height="80"
                                                class="instructor-avatar-img"
                                                alt="{{ $instructorName }}">
                                        @else
                                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3 instructor-avatar-placeholder">
                                                {{ strtoupper(substr($instructorName, 0, 1)) }}
                                            </div>
                                        @endif
                                        <div>
                                            <h6 class="mb-1">{{ $instructorName }}</h6>
                                            <p class="text-muted mb-0 small">
                                                {{ $profile['title'] ?? ($lecture->instructor === $course->instructor ? 'Course & Lecture Instructor' : 'Lecture Instructor') }}
                                            </p>
                                        </div>
                                    </div>

                                    @if($profile)
                                        @if(!empty($profile['bio']))
                                            <p class="small mb-3">{{ $profile['bio'] }}</p>
                                        @endif
                                        
                                        @if(!empty($profile['expertise']))
                                            <div class="mb-3">
                                                <h6 class="mb-2 small fw-bold">Expertise:</h6>
                                                <div>
                                                    @foreach(explode(',', $profile['expertise']) as $expertise)
                                                        <span class="badge rounded-pill px-3 py-2 me-1 mb-1 expertise-badge">{{ trim($expertise) }}</span>
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endif
                                        
                                        @if(!empty($profile['skills']))
                                            <div class="mb-3">
                                                <h6 class="mb-2 small fw-bold">Skills:</h6>
                                                <div>
                                                    @foreach(explode(',', $profile['skills']) as $skill)
                                                        <span class="badge rounded-pill px-3 py-2 me-1 mb-1 skills-badge">{{ trim($skill) }}</span>
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endif
                                        
                                        <div class="mt-3 d-flex gap-2">
                                            @if(!empty($profile['social_linkedin']))
                                                <a href="{{ $profile['social_linkedin'] }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                    <i class="fab fa-linkedin"></i>
                                                </a>
                                            @endif
                                            
                                            @if(!empty($profile['social_twitter']))
                                                <a href="{{ $profile['social_twitter'] }}" target="_blank" class="btn btn-sm btn-outline-info">
                                                    <i class="fab fa-twitter"></i>
                                                </a>
                                            @endif
                                            
                                            @if(!empty($profile['social_website']))
                                                <a href="{{ $profile['social_website'] }}" target="_blank" class="btn btn-sm btn-outline-secondary">
                                                    <i class="fas fa-globe"></i>
                                                </a>
                                            @endif
                                        </div>
                                    @endif

                                    @if($lecture->instructor !== $course->instructor && $course->instructor)
                                        @php
                                            $courseInstructorProfile = $instructorProfiles[$course->instructor] ?? null;
                                        @endphp
                                        <hr class="my-3">
                                        <h6 class="mb-2">Course Instructor:</h6>
                                        <div class="d-flex align-items-center">
                                            @if($courseInstructorProfile && isset($courseInstructorProfile['image_path']) && $courseInstructorProfile['image_path'])
                                                @php
                                                    // Try both storage paths to ensure image shows up
                                                    $imagePath = $courseInstructorProfile['image_path'];
                                                    $storageUrl = Storage::url($imagePath);
                                                    $assetUrl = asset('storage/' . $imagePath);
                                                @endphp
                                                <img src="{{ $storageUrl }}" 
                                                    onerror="this.onerror=null; this.src='{{ $assetUrl }}';"
                                                    class="rounded-circle me-2" 
                                                    width="50" height="50"
                                                    class="instructor-avatar-img"
                                                    alt="{{ $course->instructor }}">
                                            @else
                                                <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center me-2 course-instructor-avatar-placeholder">
                                                    {{ strtoupper(substr($course->instructor, 0, 1)) }}
                                                </div>
                                            @endif
                                            <div>
                                                <h6 class="mb-0">{{ $course->instructor }}</h6>
                                                <p class="text-muted mb-0 small">{{ $courseInstructorProfile['title'] ?? 'Main Course Instructor' }}</p>
                                            </div>
                                        </div>
                                        
                                        @if($courseInstructorProfile && !empty($courseInstructorProfile['expertise']))
                                            <div class="ms-5 mt-2">
                                                <div class="small">
                                                    <strong>Expertise:</strong>
                                                    @foreach(array_slice(explode(',', $courseInstructorProfile['expertise']), 0, 3) as $expertise)
                                                        <span class="badge rounded-pill px-2 py-1 me-1 expertise-badge-small">{{ trim($expertise) }}</span>
                                                    @endforeach
                                                    @if(count(explode(',', $courseInstructorProfile['expertise'])) > 3)
                                                        <span class="badge rounded-pill bg-light text-dark">+{{ count(explode(',', $courseInstructorProfile['expertise'])) - 3 }} more</span>
                                                    @endif
                                                </div>
                                            </div>
                                        @endif
                                    @endif
                                </div>
                            </div>

                            <!-- Related Lectures Card -->
                            <div class="card mb-4">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">Related Lectures</h5>
                                </div>
                                <div class="card-body">
                                    <ul class="list-group list-group-flush">
                                        @foreach($course->lectures->take(5) as $relatedLecture)
                                            <li class="list-group-item {{ $relatedLecture->id === $lecture->id ? 'bg-light' : '' }}">
                                                <a href="{{ route('lecture.detail', ['course' => $course->id, 'lecture' => $relatedLecture->id]) }}" class="text-decoration-none {{ $relatedLecture->id === $lecture->id ? 'text-primary fw-bold' : '' }}">
                                                    <i class="fas fa-play-circle me-2 {{ $relatedLecture->id === $lecture->id ? 'text-primary' : 'text-muted' }}"></i>
                                                    {{ $relatedLecture->name }}
                                                </a>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reviews Tab -->
                <div class="tab-pane fade" id="reviews" role="tabpanel" aria-labelledby="reviews-tab">
                    <div class="row">
                        <div class="col-md-8">
                            <h3 class="mb-4">Student Reviews</h3>

                            <!-- Overall Rating -->
                            <div class="course-rating-summary mb-5">
                                <div class="row align-items-center">
                                    <div class="col-md-4 text-center mb-4 mb-md-0">
                                        <div class="overall-rating">
                                            <h1 class="display-1 fw-bold mb-0">{{ number_format($lecture->average_rating, 1) }}</h1>
                                            <div class="stars mb-2">
                                                @for ($i = 1; $i <= 5; $i++)
                                                    @if ($i <= $lecture->average_rating)
                                                        <i class="fas fa-star text-warning"></i>
                                                    @elseif ($i - 0.5 <= $lecture->average_rating)
                                                        <i class="fas fa-star-half-alt text-warning"></i>
                                                    @else
                                                        <i class="far fa-star text-warning"></i>
                                                    @endif
                                                @endfor
                                            </div>
                                            <p class="text-muted">{{ $lecture->rating_count }} {{ Str::plural('review', $lecture->rating_count) }}</p>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-8">
                                        <div class="rating-bars">
                                            @php
                                                // Calculate percentage for each star rating
                                                $totalRatings = $lecture->rating_count > 0 ? $lecture->rating_count : 1;
                                                $starCounts = [
                                                    5 => $lecture->ratings()->where('rating', 5)->count(),
                                                    4 => $lecture->ratings()->where('rating', 4)->count(),
                                                    3 => $lecture->ratings()->where('rating', 3)->count(),
                                                    2 => $lecture->ratings()->where('rating', 2)->count(),
                                                    1 => $lecture->ratings()->where('rating', 1)->count(),
                                                ];
                                            @endphp
                                            
                                            @for ($i = 5; $i >= 1; $i--)
                                                @php
                                                    $percentage = ($starCounts[$i] / $totalRatings) * 100;
                                                @endphp
                                                <div class="rating-bar d-flex align-items-center mb-2">
                                                    <div class="stars me-2 rating-stars-container">
                                                        {{ $i }} <i class="fas fa-star text-warning"></i>
                                                    </div>
                                                    <div class="progress flex-grow-1 me-3 rating-progress-bar">
                                                        <div class="progress-bar bg-warning" role="progressbar" data-width="{{ $percentage }}" aria-valuenow="{{ $percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                                                    </div>
                                                    <span class="text-muted small">{{ $starCounts[$i] }}</span>
                                                </div>
                                            @endfor
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Reviews List -->
                            <div class="reviews-list">
                                @if($lecture->ratings()->where('is_approved', true)->where('show_publicly', true)->count() > 0)
                                    @foreach($lecture->ratings()->where('is_approved', true)->where('show_publicly', true)->with('user')->latest()->get() as $rating)
                                        <div class="review-item mb-4 p-4 border rounded">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <div>
                                                    <h5 class="mb-0">{{ $rating->user->name }}</h5>
                                                    <div class="stars my-2">
                                                        @for($i = 1; $i <= 5; $i++)
                                                            @if($i <= $rating->rating)
                                                                <i class="fas fa-star text-warning"></i>
                                                            @else
                                                                <i class="far fa-star text-warning"></i>
                                                            @endif
                                                        @endfor
                                                    </div>
                                                </div>
                                                <div class="text-muted">
                                                    <small>{{ $rating->created_at->format('M d, Y') }}</small>
                                                </div>
                                            </div>
                                            
                                            @if($rating->comment)
                                                <p class="review-text mb-0">{{ $rating->comment }}</p>
                                            @else
                                                <p class="text-muted fst-italic mb-0">No comment provided</p>
                                            @endif
                                        </div>
                                    @endforeach
                                @else
                                    <div class="text-center py-5">
                                        <i class="far fa-comment-dots fa-3x text-muted mb-3"></i>
                                        <h4>No Reviews Yet</h4>
                                        <p class="text-muted">Be the first to review this lecture!</p>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">Rating Overview</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-4">
                                        <h1 class="display-4 fw-bold me-3">4.0</h1>
                                        <div>
                                            <div class="mb-1">
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="far fa-star text-warning"></i>
                                            </div>
                                            <p class="text-muted mb-0">Lecture Rating</p>
                                        </div>
                                    </div>

                                    <!-- Rating Breakdown -->
                                    <div class="mb-2 d-flex align-items-center">
                                        <div class="me-2">5 stars</div>
                                        <div class="progress flex-grow-1 rating-progress-small">
                                            <div class="progress-bar bg-warning progress-bar-65" role="progressbar"></div>
                                        </div>
                                        <div class="ms-2">65%</div>
                                    </div>
                                    <div class="mb-2 d-flex align-items-center">
                                        <div class="me-2">4 stars</div>
                                        <div class="progress flex-grow-1 rating-progress-small">
                                            <div class="progress-bar bg-warning progress-bar-25" role="progressbar"></div>
                                        </div>
                                        <div class="ms-2">25%</div>
                                    </div>
                                    <div class="mb-2 d-flex align-items-center">
                                        <div class="me-2">3 stars</div>
                                        <div class="progress flex-grow-1 rating-progress-small">
                                            <div class="progress-bar bg-warning progress-bar-5" role="progressbar"></div>
                                        </div>
                                        <div class="ms-2">5%</div>
                                    </div>
                                    <div class="mb-2 d-flex align-items-center">
                                        <div class="me-2">2 stars</div>
                                        <div class="progress flex-grow-1 rating-progress-small">
                                            <div class="progress-bar bg-warning progress-bar-3" role="progressbar"></div>
                                        </div>
                                        <div class="ms-2">3%</div>
                                    </div>
                                    <div class="mb-2 d-flex align-items-center">
                                        <div class="me-2">1 star</div>
                                        <div class="progress flex-grow-1 rating-progress-small">
                                            <div class="progress-bar bg-warning progress-bar-2" role="progressbar"></div>
                                        </div>
                                        <div class="ms-2">2%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success and Error Messages -->
    @if (session()->has('success'))
        <div class="alert alert-success alert-dismissible fade show mt-4" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="alert alert-danger alert-dismissible fade show mt-4" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <style>
        .course-image-container {
            height: 200px;
            overflow: hidden;
        }

        .course-image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .check-list li {
            position: relative;
            padding-left: 1.5rem;
            margin-bottom: 0.5rem;
            list-style-type: none;
        }

        .check-list li:before {
            content: '\f00c';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: #28a745;
        }
    </style>
    <link rel="stylesheet" href="{{ asset('css/lecture-detail.css') }}">
    <script src="{{ asset('js/lecture-detail.js') }}" @cspNonce></script>
</div>
