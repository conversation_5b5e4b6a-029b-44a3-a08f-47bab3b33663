/**
 * Livewire Base Styles
 * Extracted from @livewireStyles to avoid CSP violations
 */

/* Livewire Loading States */
[wire\:loading][wire\:loading], 
[wire\:loading\.delay][wire\:loading\.delay], 
[wire\:loading\.inline-block][wire\:loading\.inline-block], 
[wire\:loading\.inline][wire\:loading\.inline], 
[wire\:loading\.block][wire\:loading\.block], 
[wire\:loading\.flex][wire\:loading\.flex], 
[wire\:loading\.table][wire\:loading\.table], 
[wire\:loading\.grid][wire\:loading\.grid], 
[wire\:loading\.inline-flex][wire\:loading\.inline-flex] {
    display: none;
}

/* Livewire Loading Delays */
[wire\:loading\.delay\.none][wire\:loading\.delay\.none], 
[wire\:loading\.delay\.shortest][wire\:loading\.delay\.shortest], 
[wire\:loading\.delay\.shorter][wire\:loading\.delay\.shorter], 
[wire\:loading\.delay\.short][wire\:loading\.delay\.short], 
[wire\:loading\.delay\.default][wire\:loading\.delay\.default], 
[wire\:loading\.delay\.long][wire\:loading\.delay\.long], 
[wire\:loading\.delay\.longer][wire\:loading\.delay\.longer], 
[wire\:loading\.delay\.longest][wire\:loading\.delay\.longest] {
    display: none;
}

/* Livewire Offline State */
[wire\:offline][wire\:offline] {
    display: none;
}

/* Livewire Dirty State */
[wire\:dirty]:not(textarea):not(input):not(select) {
    display: none;
}

/* Livewire Progress Bar */
:root {
    --livewire-progress-bar-color: #2299dd;
}

/* Alpine.js and Livewire Cloak */
[x-cloak] {
    display: none !important;
}

[wire\:cloak] {
    display: none !important;
}

/* NProgress Styles for Livewire */
.nprogress-custom-parent {
    overflow: hidden;
    position: relative;
}

.nprogress-custom-parent #nprogress .spinner,
.nprogress-custom-parent #nprogress .bar {
    position: absolute;
}

@-webkit-keyframes nprogress-spinner {
    0% { -webkit-transform: rotate(0deg); }
    100% { -webkit-transform: rotate(360deg); }
}

@keyframes nprogress-spinner {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Additional Livewire Loading Indicators */
.wire-loading {
    display: none;
}

.wire-loading.wire-loading-active {
    display: block;
}

/* Livewire Form Validation */
.wire-validation-error {
    color: #e3342f;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Livewire Modal Backdrop */
.wire-modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

/* Livewire Transition Classes */
.wire-fade-enter {
    opacity: 0;
}

.wire-fade-enter-active {
    opacity: 1;
    transition: opacity 0.3s ease;
}

.wire-fade-leave {
    opacity: 1;
}

.wire-fade-leave-active {
    opacity: 0;
    transition: opacity 0.3s ease;
}