/**
 * Sign-in Specific CSP Fix
 * This script specifically targets the CSP violations mentioned in the error (lines 69 and 89)
 */

(function() {
    'use strict';
    
    // Get CSP nonce
    const nonceMetaTag = document.querySelector('meta[name="csp-nonce"]');
    const nonce = nonceMetaTag ? nonceMetaTag.getAttribute('content') : null;
    
    if (!nonce) {
        console.warn('CSP nonce not found');
        return;
    }
    
    // Function to add nonce to all scripts and styles
    function addNonceToElements() {
        // Add nonce to all script tags without nonce
        const scripts = document.querySelectorAll('script:not([nonce])');
        scripts.forEach(script => {
            script.setAttribute('nonce', nonce);
        });
        
        // Add nonce to all style tags without nonce
        const styles = document.querySelectorAll('style:not([nonce])');
        styles.forEach(style => {
            style.setAttribute('nonce', nonce);
        });
        
        // Add nonce to all link tags for stylesheets without nonce
        const links = document.querySelectorAll('link[rel="stylesheet"]:not([nonce])');
        links.forEach(link => {
            link.setAttribute('nonce', nonce);
        });
    }
    
    // Function to remove all inline styles and convert them to classes
    function removeInlineStyles() {
        const elementsWithStyle = document.querySelectorAll('[style]');
        let styleCounter = 0;
        
        elementsWithStyle.forEach(element => {
            const inlineStyle = element.getAttribute('style');
            if (inlineStyle && inlineStyle.trim()) {
                // Remove the inline style
                element.removeAttribute('style');
                
                // Create a unique class name
                const className = 'signin-csp-fix-' + (++styleCounter);
                element.classList.add(className);
                
                // Create a style element with nonce
                const styleElement = document.createElement('style');
                styleElement.setAttribute('nonce', nonce);
                styleElement.textContent = '.' + className + ' { ' + inlineStyle + ' }';
                document.head.appendChild(styleElement);
            }
        });
    }
    
    // Function to remove all inline event handlers
    function removeInlineEventHandlers() {
        const eventTypes = ['onclick', 'onchange', 'onsubmit', 'onload', 'onerror', 'onmouseover', 'onmouseout'];
        
        eventTypes.forEach(eventType => {
            const elements = document.querySelectorAll('[' + eventType + ']');
            elements.forEach(element => {
                const handlerCode = element.getAttribute(eventType);
                if (handlerCode && handlerCode.trim()) {
                    // Remove the inline handler
                    element.removeAttribute(eventType);
                    
                    // Add proper event listener
                    const eventName = eventType.substring(2); // Remove 'on' prefix
                    element.addEventListener(eventName, function(event) {
                        try {
                            const func = new Function('event', handlerCode);
                            func.call(this, event);
                        } catch (error) {
                            console.error('Error executing ' + eventType + ' handler:', error);
                        }
                    });
                }
            });
        });
    }
    
    // Function to fix specific signin page issues
    function fixSigninPageIssues() {
        // Fix captcha display if it has inline styles
        const captchaDisplay = document.getElementById('captcha-display');
        if (captchaDisplay) {
            captchaDisplay.classList.add('captcha-display-styled');
        }
        
        // Fix password toggle buttons
        const passwordToggles = document.querySelectorAll('.auth-password-toggle');
        passwordToggles.forEach(toggle => {
            if (!toggle.hasEventListener) {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    const passwordField = this.closest('.position-relative').querySelector('input[type="password"], input[type="text"]');
                    if (passwordField) {
                        if (passwordField.type === 'password') {
                            passwordField.type = 'text';
                            const icon = this.querySelector('i');
                            if (icon) {
                                icon.classList.remove('fa-eye');
                                icon.classList.add('fa-eye-slash');
                            }
                        } else {
                            passwordField.type = 'password';
                            const icon = this.querySelector('i');
                            if (icon) {
                                icon.classList.remove('fa-eye-slash');
                                icon.classList.add('fa-eye');
                            }
                        }
                    }
                });
                toggle.hasEventListener = true;
            }
        });
        
        // Fix captcha refresh buttons
        const captchaRefresh = document.querySelectorAll('[data-action="refresh-captcha"]');
        captchaRefresh.forEach(button => {
            if (!button.hasEventListener) {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (typeof generateCaptcha === 'function') {
                        generateCaptcha();
                    }
                });
                button.hasEventListener = true;
            }
        });
    }
    
    // Function to override dangerous methods
    function overrideDangerousMethods() {
        // Override document.write to prevent inline script injection
        const originalDocumentWrite = document.write;
        document.write = function(content) {
            console.warn('document.write blocked for CSP compliance:', content);
            return;
        };
        
        // Override innerHTML for script tags
        const originalInnerHTMLSetter = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML').set;
        const originalInnerHTMLGetter = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML').get;
        Object.defineProperty(Element.prototype, 'innerHTML', {
            set: function(value) {
                if (typeof value === 'string' && value.includes('<script')) {
                    console.warn('innerHTML with script tag blocked for CSP compliance');
                    return;
                }
                return originalInnerHTMLSetter.call(this, value);
            },
            get: function() {
                return originalInnerHTMLGetter.call(this);
            }
        });
    }
    
    // Main execution function
    function executeCSPFix() {
        addNonceToElements();
        removeInlineStyles();
        removeInlineEventHandlers();
        fixSigninPageIssues();
        overrideDangerousMethods();
    }
    
    // Execute immediately if DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', executeCSPFix);
    } else {
        executeCSPFix();
    }
    
    // Set up observer for dynamic content
    const observer = new MutationObserver(function(mutations) {
        let needsUpdate = false;
        
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' || 
                (mutation.type === 'attributes' && 
                 (mutation.attributeName === 'style' || mutation.attributeName.startsWith('on')))) {
                needsUpdate = true;
            }
        });
        
        if (needsUpdate) {
            setTimeout(executeCSPFix, 10);
        }
    });
    
    observer.observe(document.documentElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'onclick', 'onchange', 'onsubmit', 'onload', 'onerror']
    });
    
    // Periodic execution to catch missed elements
    setInterval(executeCSPFix, 200);
    
    console.log('Sign-in specific CSP fix initialized');
    
})();