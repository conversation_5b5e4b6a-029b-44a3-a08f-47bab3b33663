<?php

namespace App\Services;

use App\Models\PhoneVerification;
use Carbon\Carbon;

class PhoneVerificationService
{
    protected $twilioService;

    public function __construct(TwilioService $twilioService)
    {
        $this->twilioService = $twilioService;
    }

    public function sendCode($phone)
    {
        // Generate a random 6-digit code
        $code = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);

        // Store the code in the database
        PhoneVerification::updateOrCreate(
            ['phone' => $phone],
            [
                'code' => $code,
                'expires_at' => Carbon::now()->addMinutes(5)
            ]
        );

        // Send the code via Twilio
        return $this->twilioService->sendVerificationCode($phone, $code);
    }

    public function verify($phone, $code)
    {
        $verification = PhoneVerification::where('phone', $phone)
            ->where('code', $code)
            ->where('expires_at', '>', now())
            ->first();

        if ($verification) {
            $verification->delete();
            return true;
        }

        return false;
    }
} 