<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\Course;
use App\Models\Lecture;
use App\Models\User;

class OrderController extends Controller
{
    /**
     * Display a listing of the orders.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $orders = Order::with('user')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('admin.orders.index', compact('orders'));
    }

    /**
     * Display the specified order.
     *
     * @param  \App\Models\Order  $order
     * @return \Illuminate\View\View
     */
    public function show(Order $order)
    {
        $cartItems = json_decode($order->cart_items, true);
        $items = [];

        if (is_array($cartItems)) {
            foreach ($cartItems as $item) {
                if (isset($item['course_id'])) {
                    $course = Course::find($item['course_id']);
                    if ($course) {
                        $items[] = [
                            'type' => 'Course',
                            'name' => $course->name,
                            'price' => $item['price'] ?? 0,
                        ];
                    }
                } elseif (isset($item['lecture_id'])) {
                    $lecture = Lecture::find($item['lecture_id']);
                    if ($lecture) {
                        $items[] = [
                            'type' => 'Lecture',
                            'name' => $lecture->lecture_title,
                            'course' => $lecture->course->name ?? 'Unknown Course',
                            'price' => $item['price'] ?? 0,
                        ];
                    }
                }
            }
        }

        $billingAddress = json_decode($order->billing_address, true);

        return view('admin.orders.show', compact('order', 'items', 'billingAddress'));
    }

    /**
     * Approve an order payment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Order  $order
     * @return \Illuminate\Http\RedirectResponse
     */
    public function approve(Request $request, Order $order)
    {
        // Update order status to paid
        $order->update(['status' => 'paid']);

        // Grant user access to the purchased courses and lectures
        $userId = $order->user_id;
        $cartItems = json_decode($order->cart_items, true);

        if (is_array($cartItems)) {
            foreach ($cartItems as $item) {
                if (isset($item['course_id'])) {
                    // Check if user already has access to this course
                    $existingAccess = \App\Models\UserCourse::where([
                        'user_id' => $userId,
                        'course_id' => $item['course_id'],
                        'lecture_id' => null,
                        'status' => 'active'
                    ])->first();

                    if (!$existingAccess) {
                        \App\Models\UserCourse::create([
                            'user_id' => $userId,
                            'course_id' => $item['course_id'],
                            'lecture_id' => null,
                            'status' => 'active',
                            'order_id' => $order->id
                        ]);
                    }
                } elseif (isset($item['lecture_id'])) {
                    // This is a lecture purchase - we need to get the course_id from the lecture
                    $lecture = \App\Models\Lecture::find($item['lecture_id']);

                    if ($lecture) {
                        // Check if user already has access to this lecture
                        $existingAccess = \App\Models\UserCourse::where([
                            'user_id' => $userId,
                            'course_id' => $lecture->course_id,
                            'lecture_id' => $item['lecture_id'],
                            'status' => 'active'
                        ])->first();

                        if (!$existingAccess) {
                            \App\Models\UserCourse::create([
                                'user_id' => $userId,
                                'course_id' => $lecture->course_id,
                                'lecture_id' => $item['lecture_id'],
                                'status' => 'active',
                                'order_id' => $order->id
                            ]);
                        }
                    }
                }
            }
        }

        // Send confirmation email to user (optional)
        // You can implement email sending logic here

        return redirect()->route('admin.orders')
            ->with('success', 'Order #' . $order->id . ' has been approved successfully.');
    }

    /**
     * Reject an order payment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Order  $order
     * @return \Illuminate\Http\RedirectResponse
     */
    public function reject(Request $request, Order $order)
    {
        // Update order status to rejected
        $order->update(['status' => 'rejected']);

        // Optional: Add a reason for rejection
        if ($request->has('reason')) {
            $order->update(['rejection_reason' => $request->reason]);
        }

        // Send notification email to user (optional)
        // You can implement email sending logic here

        return redirect()->route('admin.orders')
            ->with('success', 'Order #' . $order->id . ' has been rejected.');
    }
}
